"use strict";
/**
 * Skrill Payment Service - Production Ready
 *
 * Production-ready service for handling Skrill Quick Checkout integration
 * Supports payment creation, QR code generation, and payment status tracking
 *
 * PRODUCTION CONFIGURATION REQUIREMENTS:
 * - SKRILL_API_URL: Production Skrill API endpoint (required)
 * - SKRILL_MERCHANT_EMAIL: Your verified Skrill merchant email (required)
 * - SKRILL_SECRET_WORD: Secret word for webhook validation (required for webhooks)
 * - SKRILL_MERCHANT_ID: Your Skrill merchant ID (optional, for advanced features)
 * - NODE_ENV: Set to 'production' for production deployment
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.skrillPaymentService = void 0;
const axios_1 = __importDefault(require("axios"));
const logger_1 = require("../config/logger");
const zod_1 = require("zod");
const skrillLogger = logger_1.logger.child({ module: 'skrill-payment-service' });
// Production Environment Configuration
const NODE_ENV = process.env.NODE_ENV || 'development';
const IS_PRODUCTION = NODE_ENV === 'production';
// Skrill API Configuration - Production URLs
const SKRILL_API_URL = process.env.SKRILL_API_URL || (IS_PRODUCTION
    ? 'https://www.skrill.com/app/payment.pl' // Production endpoint
    : 'https://www.moneybookers.com/app/payment.pl' // Sandbox endpoint
);
// Required Production Configuration
const SKRILL_MERCHANT_EMAIL = process.env.SKRILL_MERCHANT_EMAIL;
const SKRILL_SECRET_WORD = process.env.SKRILL_SECRET_WORD; // For webhook validation
const SKRILL_MERCHANT_ID = process.env.SKRILL_MERCHANT_ID; // For advanced features
// Validate required production configuration
if (IS_PRODUCTION) {
    if (!SKRILL_MERCHANT_EMAIL) {
        throw new Error('SKRILL_MERCHANT_EMAIL environment variable is required for production');
    }
    if (!SKRILL_SECRET_WORD) {
        skrillLogger.warn('SKRILL_SECRET_WORD not configured - webhook validation will be disabled');
    }
}
// Production-ready validation schemas with enhanced security
const skrillPaymentRequestSchema = zod_1.z.object({
    orderId: zod_1.z.string()
        .min(1, 'Order ID is required')
        .max(100, 'Order ID too long')
        .regex(/^[a-zA-Z0-9_-]+$/, 'Order ID contains invalid characters'),
    amount: zod_1.z.number()
        .positive('Amount must be positive')
        .int('Amount must be an integer (in cents)')
        .min(50, 'Minimum amount is 50 cents') // €0.50 minimum
        .max(100000000, 'Maximum amount is €1,000,000'), // €1M maximum
    currency: zod_1.z.string()
        .length(3, 'Currency must be 3 characters')
        .regex(/^[A-Z]{3}$/, 'Currency must be uppercase')
        .default('EUR'),
    payerName: zod_1.z.string()
        .min(1, 'Payer name is required')
        .max(100, 'Payer name too long')
        .regex(/^[a-zA-Z\s\-'\.]+$/, 'Payer name contains invalid characters'),
    successUrl: zod_1.z.string()
        .url('Invalid success URL')
        .max(500, 'Success URL too long'),
    cancelUrl: zod_1.z.string()
        .url('Invalid cancel URL')
        .max(500, 'Cancel URL too long'),
    languageCode: zod_1.z.string()
        .length(2, 'Language code must be 2 characters')
        .regex(/^[a-z]{2}$/, 'Language code must be lowercase')
        .default('en'),
});
// Production-ready validation schemas (for future use)
// const skrillPaymentResponseSchema = z.object({
//   session_id: z.string().min(1, 'Session ID is required'),
//   redirect_url: z.string().url('Invalid redirect URL'),
//   expires_at: z.string().nullable().optional(),
// });
// const skrillStatusResponseSchema = z.object({
//   status: z.enum(['pending', 'processed', 'cancelled', 'failed'], {
//     errorMap: () => ({ message: 'Invalid payment status' })
//   }),
//   transaction_id: z.string().optional(),
//   amount: z.number().positive('Amount must be positive').optional(),
//   currency: z.string().length(3, 'Currency must be 3 characters').optional(),
// });
// Enhanced webhook validation schema for production
const skrillWebhookSchema = zod_1.z.object({
    pay_to_email: zod_1.z.string().email('Invalid merchant email'),
    pay_from_email: zod_1.z.string().email('Invalid payer email').optional(),
    merchant_id: zod_1.z.string().optional(),
    transaction_id: zod_1.z.string().min(1, 'Transaction ID is required'),
    mb_transaction_id: zod_1.z.string().min(1, 'Skrill transaction ID is required'),
    mb_amount: zod_1.z.number().positive('Amount must be positive'),
    mb_currency: zod_1.z.string().length(3, 'Currency must be 3 characters'),
    status: zod_1.z.enum(['2', '-2', '-3'], {
        errorMap: () => ({ message: 'Invalid webhook status' })
    }),
    md5sig: zod_1.z.string().min(1, 'MD5 signature is required'),
    amount: zod_1.z.number().positive('Amount must be positive'),
    currency: zod_1.z.string().length(3, 'Currency must be 3 characters'),
});
class SkrillPaymentService {
    constructor() {
        this.isProduction = IS_PRODUCTION;
        this.apiUrl = SKRILL_API_URL;
        this.merchantEmail = SKRILL_MERCHANT_EMAIL || '';
        this.secretWord = SKRILL_SECRET_WORD;
        this.merchantId = SKRILL_MERCHANT_ID;
        // Production validation
        if (this.isProduction && !this.merchantEmail) {
            throw new Error('Skrill merchant email is required for production deployment');
        }
        // Log configuration status (without sensitive data)
        skrillLogger.info('Skrill Payment Service initialized', {
            environment: NODE_ENV,
            apiUrl: this.apiUrl,
            merchantEmailConfigured: !!this.merchantEmail,
            secretWordConfigured: !!this.secretWord,
            merchantIdConfigured: !!this.merchantId,
        });
        // Production-ready HTTP client configuration
        this.client = axios_1.default.create({
            baseURL: this.apiUrl,
            timeout: this.isProduction ? 60000 : 30000, // Longer timeout for production
            maxRedirects: 5,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': `POS-System/1.0 (${NODE_ENV})`,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
            },
            validateStatus: (status) => status < 500, // Accept 4xx as valid responses
        });
        // Production-ready request interceptor
        this.client.interceptors.request.use((config) => {
            const requestId = `skrill_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
            // Store metadata in headers for tracking (safe approach)
            if (config.headers) {
                config.headers['X-Request-ID'] = requestId;
                config.headers['X-Request-Start'] = Date.now().toString();
            }
            // Log request (without sensitive data in production)
            skrillLogger.info('Skrill API request initiated', {
                requestId,
                method: config.method?.toUpperCase(),
                url: config.url,
                timeout: config.timeout,
                environment: NODE_ENV,
            });
            return config;
        }, (error) => {
            skrillLogger.error('Skrill API request configuration error', {
                error: error.message,
                stack: this.isProduction ? undefined : error.stack,
            });
            return Promise.reject(error);
        });
        // Production-ready response interceptor
        this.client.interceptors.response.use((response) => {
            const requestId = response.config.headers['X-Request-ID'];
            const startTime = response.config.headers['X-Request-Start'];
            const duration = startTime ? Date.now() - parseInt(startTime) : undefined;
            skrillLogger.info('Skrill API response received', {
                requestId,
                status: response.status,
                statusText: response.statusText,
                duration: duration ? `${duration}ms` : undefined,
                contentLength: response.headers['content-length'],
            });
            return response;
        }, (error) => {
            const requestId = error.config?.headers?.['X-Request-ID'];
            const startTime = error.config?.headers?.['X-Request-Start'];
            const duration = startTime ? Date.now() - parseInt(startTime) : undefined;
            skrillLogger.error('Skrill API response error', {
                requestId,
                status: error.response?.status,
                statusText: error.response?.statusText,
                duration: duration ? `${duration}ms` : undefined,
                message: error.message,
                code: error.code,
                // Only include stack trace in development
                stack: this.isProduction ? undefined : error.stack,
            });
            return Promise.reject(error);
        });
    }
    /**
     * Create a new Skrill payment session
     * Production-ready with enhanced validation and security
     */
    async createPayment(params) {
        try {
            // Validate input parameters with enhanced security checks
            const validatedParams = skrillPaymentRequestSchema.parse(params);
            // Additional production security checks
            if (this.isProduction) {
                // Validate URLs are from allowed domains (implement your domain whitelist)
                const allowedDomains = process.env.ALLOWED_CALLBACK_DOMAINS?.split(',') || [];
                if (allowedDomains.length > 0) {
                    const successDomain = new URL(validatedParams.successUrl).hostname;
                    const cancelDomain = new URL(validatedParams.cancelUrl).hostname;
                    if (!allowedDomains.includes(successDomain) || !allowedDomains.includes(cancelDomain)) {
                        throw new Error('Callback URLs must be from allowed domains');
                    }
                }
            }
            skrillLogger.info('Creating Skrill payment', {
                orderId: validatedParams.orderId,
                amount: validatedParams.amount,
                currency: validatedParams.currency,
                payerNameLength: validatedParams.payerName.length,
                environment: NODE_ENV,
            });
            // Convert amount from cents to currency units for Skrill
            const amountInCurrency = (validatedParams.amount / 100).toFixed(2);
            // Prepare production-ready Skrill Quick Checkout parameters
            const paymentData = new URLSearchParams({
                pay_to_email: this.merchantEmail,
                transaction_id: validatedParams.orderId,
                return_url: validatedParams.successUrl,
                cancel_url: validatedParams.cancelUrl,
                status_url: `${validatedParams.successUrl.replace(/\/$/, '')}/webhook/skrill`, // Webhook URL
                language: validatedParams.languageCode.toUpperCase(),
                amount: amountInCurrency,
                currency: validatedParams.currency,
                detail1_description: 'Payment for Order',
                detail1_text: `Order ID: ${validatedParams.orderId}`,
                detail2_description: 'Payer Name',
                detail2_text: validatedParams.payerName,
                merchant_fields: 'payer_name,order_id',
                payer_name: validatedParams.payerName,
                order_id: validatedParams.orderId,
                // Production security settings
                logo_url: process.env.SKRILL_LOGO_URL || '', // Optional: Your logo URL
                prepare_only: '1', // Prepare payment session only
            });
            // Log request initiation (without sensitive data)
            skrillLogger.info('Initiating Skrill payment request', {
                orderId: validatedParams.orderId,
                amount: amountInCurrency,
                currency: validatedParams.currency,
                language: validatedParams.languageCode,
            });
            const response = await this.client.post('', paymentData);
            // Validate response status
            if (response.status !== 200 && response.status !== 302) {
                throw new Error(`Skrill API returned unexpected status: ${response.status}`);
            }
            // Extract session ID from Skrill response
            const sessionId = this.extractSessionId(response.data);
            if (!sessionId) {
                skrillLogger.error('Failed to extract session ID from Skrill response', {
                    orderId: validatedParams.orderId,
                    responseStatus: response.status,
                    responseLength: response.data?.length || 0,
                });
                throw new Error('Invalid response from Skrill API - no session ID found');
            }
            // Construct secure redirect URL
            const redirectUrl = `${this.apiUrl}?sid=${sessionId}`;
            // Validate the generated URL
            try {
                new URL(redirectUrl);
            }
            catch (urlError) {
                skrillLogger.error('Generated invalid redirect URL', {
                    orderId: validatedParams.orderId,
                    sessionId,
                    redirectUrl,
                });
                throw new Error('Failed to generate valid payment URL');
            }
            const skrillResponse = {
                session_id: sessionId,
                redirect_url: redirectUrl,
                expires_at: null, // Skrill sessions typically don't have explicit expiration
            };
            skrillLogger.info('Skrill payment session created successfully', {
                orderId: validatedParams.orderId,
                sessionId,
                amount: amountInCurrency,
                currency: validatedParams.currency,
            });
            return {
                success: true,
                data: skrillResponse,
            };
        }
        catch (error) {
            // Enhanced error handling for production
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorType = error?.constructor?.name || 'UnknownError';
            skrillLogger.error('Skrill payment creation failed', {
                orderId: params.orderId,
                error: errorMessage,
                errorType,
                // Include stack trace only in development
                stack: this.isProduction ? undefined : error?.stack,
            });
            // Handle different error types appropriately
            if (axios_1.default.isAxiosError(error)) {
                const status = error.response?.status;
                const responseData = error.response?.data;
                // Log API-specific errors
                skrillLogger.error('Skrill API error', {
                    orderId: params.orderId,
                    status,
                    statusText: error.response?.statusText,
                    responseDataLength: typeof responseData === 'string' ? responseData.length : 0,
                });
                // Return appropriate error based on status
                if (status === 400) {
                    return {
                        success: false,
                        error: 'INVALID_PAYMENT_DATA',
                        message: 'Invalid payment parameters provided',
                    };
                }
                else if (status === 401 || status === 403) {
                    return {
                        success: false,
                        error: 'AUTHENTICATION_FAILED',
                        message: 'Skrill authentication failed - check merchant credentials',
                    };
                }
                else if (status && status >= 500) {
                    return {
                        success: false,
                        error: 'SKRILL_SERVICE_UNAVAILABLE',
                        message: 'Skrill payment service is temporarily unavailable',
                    };
                }
                return {
                    success: false,
                    error: 'PAYMENT_CREATION_FAILED',
                    message: 'Failed to create payment session',
                };
            }
            // Handle validation errors
            if (error instanceof zod_1.z.ZodError) {
                skrillLogger.warn('Payment validation failed', {
                    orderId: params.orderId,
                    validationErrors: error.errors,
                });
                return {
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid payment parameters',
                    details: this.isProduction ? undefined : error.errors,
                };
            }
            // Generic error handling
            return {
                success: false,
                error: 'PAYMENT_CREATION_FAILED',
                message: this.isProduction ? 'Payment creation failed' : errorMessage,
            };
        }
    }
    /**
     * Get QR code for Skrill payment
     * Returns the QR code URL directly
     */
    async getQRCode(sessionId) {
        try {
            skrillLogger.info('Getting Skrill payment QR code', {
                module: 'skrill-payment-service',
                sessionId,
            });
            // Generate QR code URL for Skrill payment
            const qrCodeUrl = `${this.apiUrl}?sid=${sessionId}&qr=1`;
            skrillLogger.info('Skrill payment QR code URL generated', {
                module: 'skrill-payment-service',
                sessionId,
                qrCodeUrl,
            });
            return {
                success: true,
                data: { qrCodeUrl },
            };
        }
        catch (error) {
            skrillLogger.error('Failed to get Skrill payment QR code', error);
            return {
                success: false,
                error: 'QR_CODE_FAILED',
                message: 'Failed to get QR code',
            };
        }
    }
    /**
     * Get payment URL for Skrill payment
     */
    async getPaymentUrl(sessionId, returnUrl) {
        try {
            skrillLogger.info('Getting Skrill payment URL', {
                module: 'skrill-payment-service',
                sessionId,
                returnUrl,
            });
            const paymentUrl = `${this.apiUrl}?sid=${sessionId}&return_url=${encodeURIComponent(returnUrl)}`;
            const skrillResponse = {
                session_id: sessionId,
                redirect_url: paymentUrl,
                expires_at: null,
            };
            skrillLogger.info('Skrill payment URL retrieved successfully', {
                module: 'skrill-payment-service',
                sessionId,
                redirect_url: paymentUrl,
            });
            return {
                success: true,
                data: skrillResponse,
            };
        }
        catch (error) {
            skrillLogger.error('Failed to get Skrill payment URL', error);
            return {
                success: false,
                error: 'PAYMENT_URL_FAILED',
                message: 'Failed to get payment URL',
            };
        }
    }
    /**
     * Extract session ID from Skrill response with enhanced security validation
     */
    extractSessionId(responseData) {
        if (!responseData || typeof responseData !== 'string') {
            skrillLogger.warn('Invalid response data for session ID extraction');
            return null;
        }
        // Skrill typically returns HTML with a form containing the session ID
        // Multiple patterns to handle different response formats
        const patterns = [
            /sid=([a-zA-Z0-9]{20,40})/i, // Standard session ID pattern
            /session_id['"]\s*:\s*['"]([a-zA-Z0-9]{20,40})['"]/i, // JSON format
            /name=['"]sid['"][^>]*value=['"]([a-zA-Z0-9]{20,40})['"]/i, // Form input
        ];
        for (const pattern of patterns) {
            const match = responseData.match(pattern);
            if (match && match[1]) {
                const sessionId = match[1];
                // Validate session ID format for security
                if (/^[a-zA-Z0-9]{20,40}$/.test(sessionId)) {
                    skrillLogger.info('Session ID extracted successfully', {
                        sessionIdLength: sessionId.length,
                        pattern: pattern.source,
                    });
                    return sessionId;
                }
                else {
                    skrillLogger.warn('Extracted session ID failed validation', {
                        sessionIdLength: sessionId.length,
                        pattern: pattern.source,
                    });
                }
            }
        }
        skrillLogger.error('Failed to extract valid session ID from Skrill response', {
            responseLength: responseData.length,
            responsePreview: responseData.substring(0, 200), // First 200 chars for debugging
        });
        return null;
    }
    /**
     * Get payment details and status
     * Note: Skrill primarily uses webhooks for status updates
     */
    async getPaymentDetails(sessionId) {
        try {
            // Validate session ID format
            if (!sessionId || !/^[a-zA-Z0-9_-]{10,50}$/.test(sessionId)) {
                return {
                    success: false,
                    error: 'INVALID_SESSION_ID',
                    message: 'Invalid session ID format',
                };
            }
            skrillLogger.info('Getting Skrill payment details', {
                sessionId,
            });
            // Note: Skrill doesn't have a direct API to check payment status
            // Status updates are typically received via webhooks
            // This returns basic session information
            const paymentDetails = {
                session_id: sessionId,
                status: 'pending',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
            };
            return {
                success: true,
                data: paymentDetails,
            };
        }
        catch (error) {
            skrillLogger.error('Failed to get Skrill payment details', {
                sessionId,
                error: error instanceof Error ? error.message : 'Unknown error',
            });
            return {
                success: false,
                error: 'PAYMENT_DETAILS_FAILED',
                message: 'Failed to get payment details',
            };
        }
    }
    /**
     * Validate Skrill webhook signature for production security
     * This method should be used to verify webhook authenticity
     */
    validateWebhookSignature(webhookData) {
        if (!this.secretWord) {
            skrillLogger.warn('Webhook validation skipped - secret word not configured');
            return false;
        }
        try {
            // Validate webhook data structure
            const validatedData = skrillWebhookSchema.parse(webhookData);
            // Construct signature string according to Skrill documentation
            const signatureString = [
                validatedData.merchant_id || '',
                validatedData.transaction_id,
                this.secretWord,
                validatedData.mb_amount,
                validatedData.mb_currency,
                validatedData.status,
            ].join('');
            // Calculate MD5 hash
            const crypto = require('crypto');
            const calculatedSignature = crypto.createHash('md5').update(signatureString).digest('hex').toUpperCase();
            const isValid = calculatedSignature === validatedData.md5sig.toUpperCase();
            skrillLogger.info('Webhook signature validation', {
                transactionId: validatedData.transaction_id,
                isValid,
                providedSignature: validatedData.md5sig.substring(0, 8) + '...', // Partial for security
            });
            return isValid;
        }
        catch (error) {
            skrillLogger.error('Webhook signature validation failed', {
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: this.isProduction ? undefined : error?.stack,
            });
            return false;
        }
    }
}
exports.skrillPaymentService = new SkrillPaymentService();
/**
 * PRODUCTION DEPLOYMENT CHECKLIST:
 *
 * 1. Environment Variables (Required):
 *    - SKRILL_API_URL: https://www.skrill.com/app/payment.pl (production)
 *    - SKRILL_MERCHANT_EMAIL: Your verified Skrill merchant email
 *    - NODE_ENV: production
 *
 * 2. Environment Variables (Optional but Recommended):
 *    - SKRILL_SECRET_WORD: For webhook signature validation
 *    - SKRILL_MERCHANT_ID: For advanced features
 *    - SKRILL_LOGO_URL: Your company logo URL for payment pages
 *    - ALLOWED_CALLBACK_DOMAINS: Comma-separated list of allowed domains for callbacks
 *
 * 3. Security Considerations:
 *    - Ensure all callback URLs use HTTPS in production
 *    - Implement proper webhook endpoint with signature validation
 *    - Set up proper logging and monitoring
 *    - Configure rate limiting for payment endpoints
 *    - Implement proper error handling and user feedback
 *
 * 4. Monitoring and Logging:
 *    - Monitor payment success/failure rates
 *    - Set up alerts for API errors and timeouts
 *    - Log all payment attempts for audit purposes
 *    - Monitor webhook delivery and processing
 *
 * 5. Testing:
 *    - Test with small amounts first
 *    - Verify webhook processing
 *    - Test error scenarios and edge cases
 *    - Validate all callback URLs work correctly
 *
 * 6. Compliance:
 *    - Ensure PCI DSS compliance if handling card data
 *    - Implement proper data retention policies
 *    - Follow GDPR requirements for EU customers
 *    - Maintain transaction audit logs
 */
//# sourceMappingURL=skrillPaymentService.js.map