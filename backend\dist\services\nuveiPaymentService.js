"use strict";
/**
 * Nuvei Payment Service
 *
 * Service for handling Nuvei Payment Gateway integration using Payment Page
 * Implements Nuvei Payment Page API according to official documentation
 * https://docs.nuvei.com/documentation/accept-payment/payment-page/quick-start-for-payment-page/
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.nuveiPaymentService = void 0;
const axios_1 = __importDefault(require("axios"));
const zod_1 = require("zod");
const crypto_1 = require("crypto");
const env_1 = require("../config/env");
const logger_1 = require("../config/logger");
const nuveiLogger = logger_1.logger.child({ module: 'nuvei-payment-service' });
// Validation schemas
const createPaymentSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
    amount: zod_1.z.number().positive().int(),
    currency: zod_1.z.string().length(3).default('GBP'),
    successUrl: zod_1.z.string().url(),
    cancelUrl: zod_1.z.string().url(),
    payerName: zod_1.z.string().min(1),
    payerEmail: zod_1.z.string().email().optional(),
    languageCode: zod_1.z.string().length(2).default('en'),
});
const paymentResponseSchema = zod_1.z.object({
    sessionToken: zod_1.z.string(),
    orderId: zod_1.z.string(),
    status: zod_1.z.string(),
    redirectUrl: zod_1.z.string().url().optional(),
    qrCodeData: zod_1.z.string().optional(),
});
class NuveiPaymentService {
    constructor() {
        this.merchantId = env_1.env.NUVEI_MERCHANT_ID || '';
        this.merchantSiteId = env_1.env.NUVEI_MERCHANT_SITE_ID || '';
        this.secretKey = env_1.env.NUVEI_SECRET_KEY || '';
        this.baseUrl = env_1.env.NUVEI_API_URL || 'https://ppp-test.nuvei.com/ppp/api/v1';
        this.environment = env_1.env.NUVEI_ENVIRONMENT || 'sandbox';
        this.client = axios_1.default.create({
            baseURL: this.baseUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
        });
        // Request interceptor for logging
        this.client.interceptors.request.use((config) => {
            nuveiLogger.info('Nuvei API Request', {
                method: config.method,
                url: config.url,
                data: config.data ? { ...config.data, secretKey: '[REDACTED]' } : undefined,
            });
            return config;
        }, (error) => {
            nuveiLogger.error('Nuvei API Request Error', error);
            return Promise.reject(error);
        });
        // Response interceptor for logging
        this.client.interceptors.response.use((response) => {
            nuveiLogger.info('Nuvei API Response', {
                status: response.status,
                data: response.data,
            });
            return response;
        }, (error) => {
            nuveiLogger.error('Nuvei API Response Error', {
                status: error.response?.status,
                data: error.response?.data,
                message: error.message,
            });
            return Promise.reject(error);
        });
    }
    /**
     * Generate checksum for Nuvei API requests (legacy method)
     */
    generateApiChecksum(params) {
        const crypto = require('crypto');
        const sortedKeys = Object.keys(params).sort();
        const concatenated = sortedKeys.map(key => params[key]).join('') + this.secretKey;
        return crypto.createHash('sha256').update(concatenated).digest('hex');
    }
    /**
     * Generate SHA-256 checksum for Nuvei Payment Page
     * According to Nuvei documentation: concatenate secret key + all parameter values in order
     */
    generatePaymentPageChecksum(params) {
        // Create ordered parameter string (values only, no keys) according to Nuvei docs
        const orderedValues = [
            this.secretKey,
            params.merchant_id,
            params.merchant_site_id,
            params.total_amount,
            params.currency,
            params.user_token_id,
            params.item_name_1,
            params.item_amount_1,
            params.item_quantity_1,
            params.time_stamp,
            params.version,
            params.notify_url,
        ].join('');
        return (0, crypto_1.createHash)('sha256').update(orderedValues).digest('hex');
    }
    /**
     * Generate timestamp in Nuvei format: YYYY-MM-DD.HH:MM:SS
     */
    generateTimestamp() {
        const now = new Date();
        const year = now.getUTCFullYear();
        const month = String(now.getUTCMonth() + 1).padStart(2, '0');
        const day = String(now.getUTCDate()).padStart(2, '0');
        const hours = String(now.getUTCHours()).padStart(2, '0');
        const minutes = String(now.getUTCMinutes()).padStart(2, '0');
        const seconds = String(now.getUTCSeconds()).padStart(2, '0');
        return `${year}-${month}-${day}.${hours}:${minutes}:${seconds}`;
    }
    /**
     * Create Nuvei Payment Page URL according to official documentation
     */
    async createPaymentPageUrl(params) {
        try {
            const validatedParams = createPaymentSchema.parse(params);
            nuveiLogger.info('Creating Nuvei Payment Page URL', {
                orderId: validatedParams.orderId,
                amount: validatedParams.amount,
                currency: validatedParams.currency,
                payerName: validatedParams.payerName,
            });
            // Convert amount from cents to decimal
            const amountInDecimal = (validatedParams.amount / 100).toFixed(2);
            const timestamp = this.generateTimestamp();
            // Prepare Payment Page parameters according to Nuvei documentation
            const paymentParams = {
                merchant_id: this.merchantId,
                merchant_site_id: this.merchantSiteId,
                total_amount: amountInDecimal,
                currency: validatedParams.currency,
                user_token_id: validatedParams.payerEmail || `${validatedParams.orderId}@example.com`,
                item_name_1: `Payment for order ${validatedParams.orderId}`,
                item_amount_1: amountInDecimal,
                item_quantity_1: '1',
                time_stamp: timestamp,
                version: '4.0.0',
                notify_url: `${env_1.env.BASE_URL}/api/v1/nuvei/webhook`,
                success_url: validatedParams.successUrl,
                pending_url: validatedParams.successUrl,
                error_url: validatedParams.cancelUrl,
                back_url: validatedParams.cancelUrl,
                // Optional parameters
                first_name: validatedParams.payerName.split(' ')[0] || validatedParams.payerName,
                last_name: validatedParams.payerName.split(' ').slice(1).join(' ') || '',
                email: validatedParams.payerEmail || `${validatedParams.orderId}@example.com`,
                encoding: 'utf-8',
                numberofitems: '1',
            };
            // Generate checksum
            const checksum = this.generatePaymentPageChecksum(paymentParams);
            paymentParams.checksum = checksum;
            // Build query string
            const queryParams = new URLSearchParams();
            Object.entries(paymentParams).forEach(([key, value]) => {
                queryParams.append(key, String(value));
            });
            // Determine endpoint URL
            const endpoint = this.environment === 'production'
                ? 'https://secure.safecharge.com/ppp/purchase.do'
                : 'https://ppp-test.safecharge.com/ppp/purchase.do';
            const paymentUrl = `${endpoint}?${queryParams.toString()}`;
            nuveiLogger.info('Nuvei Payment Page URL generated', {
                orderId: validatedParams.orderId,
                url: paymentUrl.substring(0, 100) + '...', // Log truncated URL for security
            });
            return {
                success: true,
                data: { payment_url: paymentUrl },
            };
        }
        catch (error) {
            nuveiLogger.error('Failed to create Nuvei Payment Page URL', error);
            if (error instanceof zod_1.z.ZodError) {
                return {
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid payment parameters',
                    details: error.errors,
                };
            }
            return {
                success: false,
                error: 'PAYMENT_URL_CREATION_FAILED',
                message: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Create a new Nuvei payment session (now uses Payment Page)
     */
    async createPayment(params) {
        // Use Payment Page approach instead of direct API
        const paymentPageResult = await this.createPaymentPageUrl(params);
        if (!paymentPageResult.success) {
            return paymentPageResult;
        }
        // Return in the expected format
        return {
            success: true,
            data: {
                id: `nuvei_${params.orderId}_${Date.now()}`,
                status: 'pending',
                amount: params.amount,
                currency: params.currency,
                payment_url: paymentPageResult.data.payment_url,
                redirect_url: paymentPageResult.data.payment_url,
                qr_code: paymentPageResult.data.payment_url, // QR code data is the URL
            },
        };
    }
    /**
     * Legacy create payment method (kept for backward compatibility)
     */
    async createPaymentLegacy(params) {
        try {
            const validatedParams = createPaymentSchema.parse(params);
            nuveiLogger.info('Creating Nuvei payment (legacy)', {
                orderId: validatedParams.orderId,
                amount: validatedParams.amount,
                currency: validatedParams.currency,
                payerName: validatedParams.payerName,
            });
            // Convert amount from cents to decimal
            const amountInDecimal = (validatedParams.amount / 100).toFixed(2);
            const requestData = {
                merchantId: this.merchantId,
                merchantSiteId: this.merchantSiteId,
                clientRequestId: validatedParams.orderId,
                amount: amountInDecimal,
                currency: validatedParams.currency,
                timeStamp: Math.floor(Date.now() / 1000).toString(),
                successUrl: validatedParams.successUrl,
                errorUrl: validatedParams.cancelUrl,
                pendingUrl: validatedParams.cancelUrl,
                notificationUrl: `${env_1.env.BASE_URL}/api/v1/nuvei/webhook`,
                userDetails: {
                    firstName: validatedParams.payerName.split(' ')[0] || validatedParams.payerName,
                    lastName: validatedParams.payerName.split(' ').slice(1).join(' ') || '',
                    email: validatedParams.payerEmail || `${validatedParams.orderId}@example.com`,
                },
                deviceDetails: {
                    ipAddress: '127.0.0.1',
                },
                urlDetails: {
                    successUrl: validatedParams.successUrl,
                    failureUrl: validatedParams.cancelUrl,
                    pendingUrl: validatedParams.cancelUrl,
                    notificationUrl: `${env_1.env.BASE_URL}/api/v1/nuvei/webhook`,
                },
            };
            // Generate checksum
            const checksum = this.generateApiChecksum(requestData);
            requestData.checksum = checksum;
            const response = await this.client.post('/getSessionToken', requestData);
            if (response.data.status !== 'SUCCESS') {
                return {
                    success: false,
                    error: 'PAYMENT_CREATION_FAILED',
                    message: response.data.reason || 'Failed to create Nuvei payment session',
                    details: response.data,
                };
            }
            const paymentResponse = {
                sessionToken: response.data.sessionToken,
                orderId: validatedParams.orderId,
                status: response.data.status,
                redirectUrl: `${this.baseUrl}/checkout.html?sessionToken=${response.data.sessionToken}`,
                qrCodeData: `${this.baseUrl}/checkout.html?sessionToken=${response.data.sessionToken}`,
            };
            return {
                success: true,
                data: paymentResponse,
            };
        }
        catch (error) {
            nuveiLogger.error('Nuvei payment creation failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return {
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid payment parameters',
                    details: error.errors,
                };
            }
            if (axios_1.default.isAxiosError(error)) {
                return {
                    success: false,
                    error: 'API_ERROR',
                    message: error.response?.data?.reason || error.message,
                    details: error.response?.data,
                };
            }
            return {
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Get payment status
     */
    async getPaymentStatus(orderId) {
        try {
            nuveiLogger.info('Getting Nuvei payment status', { orderId });
            const requestData = {
                merchantId: this.merchantId,
                merchantSiteId: this.merchantSiteId,
                clientRequestId: orderId,
                timeStamp: Math.floor(Date.now() / 1000).toString(),
            };
            const checksum = this.generateApiChecksum(requestData);
            requestData.checksum = checksum;
            const response = await this.client.post('/getPaymentStatus', requestData);
            return {
                success: true,
                data: response.data,
            };
        }
        catch (error) {
            nuveiLogger.error('Failed to get Nuvei payment status', error);
            return {
                success: false,
                error: 'API_ERROR',
                message: 'Failed to get payment status',
                details: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Health check for Nuvei service
     */
    async healthCheck() {
        try {
            // Simple health check - verify credentials are configured
            if (!this.merchantId || !this.merchantSiteId || !this.secretKey) {
                return {
                    success: false,
                    error: 'CONFIGURATION_ERROR',
                    message: 'Nuvei credentials not configured',
                };
            }
            return {
                success: true,
                data: { status: 'healthy' },
            };
        }
        catch (error) {
            return {
                success: false,
                error: 'HEALTH_CHECK_FAILED',
                message: 'Nuvei service health check failed',
            };
        }
    }
}
exports.nuveiPaymentService = new NuveiPaymentService();
//# sourceMappingURL=nuveiPaymentService.js.map