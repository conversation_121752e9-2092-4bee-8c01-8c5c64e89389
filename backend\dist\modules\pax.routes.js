"use strict";
/**
 * PAX POS Routes
 * API endpoints for PAX A920 terminal integration
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = paxRoutes;
const paxPosService_1 = require("../services/paxPosService");
const logger_1 = require("../config/logger");
async function paxRoutes(fastify) {
    // Process payment transaction
    fastify.post('/pax/payment', async (request, reply) => {
        try {
            const { amount, tenderType, transType, referenceNumber, items } = request.body;
            // Validate required fields
            if (!amount || amount <= 0) {
                return reply.status(400).send({
                    success: false,
                    error: 'Invalid amount - must be greater than 0',
                });
            }
            // Validate amount is reasonable (not more than $10,000)
            if (amount > 1000000) { // $10,000 in cents
                return reply.status(400).send({
                    success: false,
                    error: 'Amount too large - maximum $10,000 per transaction',
                });
            }
            const paymentRequest = {
                amount,
                tenderType: tenderType || 'CREDIT',
                transType: transType || 'SALE',
                referenceNumber,
                items: items || [],
            };
            logger_1.logger.info('PAX payment request received', {
                amount,
                tenderType,
                transType,
                itemCount: items?.length || 0,
            });
            const result = await paxPosService_1.paxPosService.processPayment(paymentRequest);
            if (result.success) {
                logger_1.logger.info('PAX payment successful', {
                    transactionId: result.transactionId,
                    authCode: result.authCode,
                });
            }
            else {
                logger_1.logger.warn('PAX payment failed', {
                    message: result.message,
                });
            }
            return reply.send({
                success: result.success,
                data: result.success ? {
                    transactionId: result.transactionId,
                    authCode: result.authCode,
                    resultCode: result.resultCode,
                    message: result.message,
                    receiptData: result.receiptData,
                    cardInfo: result.cardInfo,
                } : undefined,
                error: result.success ? undefined : result.message,
            });
        }
        catch (error) {
            logger_1.logger.error('PAX payment endpoint error', { error });
            return reply.status(500).send({
                success: false,
                error: 'Internal server error during payment processing',
            });
        }
    });
    // Get terminal status
    fastify.get('/pax/status', async (request, reply) => {
        try {
            logger_1.logger.info('PAX status request received');
            const status = await paxPosService_1.paxPosService.getTerminalStatus();
            return reply.send({
                success: true,
                data: {
                    connected: status.connected,
                    terminal: {
                        ip: status.ip,
                        port: status.port,
                        model: status.model,
                        serialNumber: status.serialNumber,
                    },
                    capabilities: status.capabilities,
                    timestamp: new Date().toISOString(),
                },
            });
        }
        catch (error) {
            logger_1.logger.error('PAX status endpoint error', { error });
            return reply.status(500).send({
                success: false,
                error: 'Failed to get terminal status',
            });
        }
    });
    // Test terminal connection
    fastify.post('/pax/test', async (request, reply) => {
        try {
            logger_1.logger.info('PAX connection test request received');
            const result = await paxPosService_1.paxPosService.testConnection();
            return reply.send({
                success: result.success,
                message: result.message,
                timestamp: new Date().toISOString(),
            });
        }
        catch (error) {
            logger_1.logger.error('PAX test endpoint error', { error });
            return reply.status(500).send({
                success: false,
                message: 'Connection test failed due to server error',
            });
        }
    });
    // Cancel current transaction
    fastify.post('/pax/cancel', async (request, reply) => {
        try {
            logger_1.logger.info('PAX cancel transaction request received');
            const result = await paxPosService_1.paxPosService.cancelTransaction();
            return reply.send({
                success: result.success,
                message: result.message,
                timestamp: new Date().toISOString(),
            });
        }
        catch (error) {
            logger_1.logger.error('PAX cancel endpoint error', { error });
            return reply.status(500).send({
                success: false,
                message: 'Cancel transaction failed due to server error',
            });
        }
    });
    // Get terminal configuration
    fastify.get('/pax/config', async (request, reply) => {
        try {
            logger_1.logger.info('PAX configuration request received');
            const config = paxPosService_1.paxPosService.getConfiguration();
            return reply.send({
                success: true,
                data: config,
                timestamp: new Date().toISOString(),
            });
        }
        catch (error) {
            logger_1.logger.error('PAX config endpoint error', { error });
            return reply.status(500).send({
                success: false,
                error: 'Failed to get terminal configuration',
            });
        }
    });
    // Health check endpoint
    fastify.get('/pax/health', async (request, reply) => {
        try {
            const status = await paxPosService_1.paxPosService.getTerminalStatus();
            return reply.send({
                success: true,
                data: {
                    service: 'PAX POS Service',
                    status: 'healthy',
                    terminal: {
                        connected: status.connected,
                        model: status.model,
                    },
                    timestamp: new Date().toISOString(),
                },
            });
        }
        catch (error) {
            logger_1.logger.error('PAX health check error', { error });
            return reply.status(503).send({
                success: false,
                error: 'Service unhealthy',
                timestamp: new Date().toISOString(),
            });
        }
    });
    // Get transaction receipt (for reprinting)
    fastify.get('/pax/receipt/:transactionId', async (request, reply) => {
        try {
            const { transactionId } = request.params;
            logger_1.logger.info('PAX receipt request received', { transactionId });
            // In a real implementation, this would fetch the receipt from a database
            // For now, return a placeholder response
            return reply.send({
                success: true,
                data: {
                    transactionId,
                    receiptData: {
                        customer: `Receipt for transaction ${transactionId} (Customer Copy)`,
                        merchant: `Receipt for transaction ${transactionId} (Merchant Copy)`,
                    },
                    timestamp: new Date().toISOString(),
                },
            });
        }
        catch (error) {
            logger_1.logger.error('PAX receipt endpoint error', { error });
            return reply.status(500).send({
                success: false,
                error: 'Failed to retrieve receipt',
            });
        }
    });
    // Refund transaction
    fastify.post('/pax/refund', async (request, reply) => {
        try {
            const { originalTransactionId, amount } = request.body;
            if (!originalTransactionId) {
                return reply.status(400).send({
                    success: false,
                    error: 'Original transaction ID is required for refunds',
                });
            }
            logger_1.logger.info('PAX refund request received', {
                originalTransactionId,
                amount,
            });
            // Process refund
            const refundRequest = {
                amount: amount || 0, // If no amount specified, it's a full refund
                transType: 'REFUND',
                referenceNumber: originalTransactionId,
            };
            const result = await paxPosService_1.paxPosService.processPayment(refundRequest);
            return reply.send({
                success: result.success,
                data: result.success ? {
                    transactionId: result.transactionId,
                    authCode: result.authCode,
                    message: result.message,
                    originalTransactionId,
                } : undefined,
                error: result.success ? undefined : result.message,
            });
        }
        catch (error) {
            logger_1.logger.error('PAX refund endpoint error', { error });
            return reply.status(500).send({
                success: false,
                error: 'Refund processing failed',
            });
        }
    });
}
//# sourceMappingURL=pax.routes.js.map