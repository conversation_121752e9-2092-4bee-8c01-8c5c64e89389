"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.receiptRoutes = receiptRoutes;
const receiptController_1 = require("../controllers/receiptController");
async function receiptRoutes(fastify) {
    // Generate receipt for a transaction
    fastify.get('/receipts/:transactionId', {
        schema: {
            params: {
                type: 'object',
                properties: {
                    transactionId: { type: 'string' }
                },
                required: ['transactionId']
            },
            response: {
                200: {
                    type: 'object',
                    properties: {
                        success: { type: 'boolean' },
                        data: {
                            type: 'object',
                            properties: {
                                transactionId: { type: 'string' },
                                customerReceipt: { type: 'string' },
                                merchantReceipt: { type: 'string' },
                                timestamp: { type: 'string' }
                            }
                        }
                    }
                }
            }
        }
    }, receiptController_1.receiptController.generateReceipt);
    // Print receipt
    fastify.post('/receipts/print', {
        schema: {
            body: {
                type: 'object',
                properties: {
                    transactionId: { type: 'string' },
                    copies: { type: 'number', minimum: 1, maximum: 5, default: 1 },
                    customerCopy: { type: 'boolean', default: true }
                },
                required: ['transactionId']
            },
            response: {
                200: {
                    type: 'object',
                    properties: {
                        success: { type: 'boolean' },
                        data: {
                            type: 'object',
                            properties: {
                                transactionId: { type: 'string' },
                                printJobs: { type: 'array' },
                                message: { type: 'string' }
                            }
                        }
                    }
                }
            }
        }
    }, receiptController_1.receiptController.printReceipt);
    // Get receipt in different formats
    fastify.get('/receipts/:transactionId/formats', {
        schema: {
            params: {
                type: 'object',
                properties: {
                    transactionId: { type: 'string' }
                },
                required: ['transactionId']
            }
        }
    }, receiptController_1.receiptController.getReceiptFormats);
    // Email receipt
    fastify.post('/receipts/email', {
        schema: {
            body: {
                type: 'object',
                properties: {
                    transactionId: { type: 'string' },
                    email: { type: 'string', format: 'email' },
                    customerCopy: { type: 'boolean', default: true }
                },
                required: ['transactionId', 'email']
            }
        }
    }, receiptController_1.receiptController.emailReceipt);
}
//# sourceMappingURL=receipt.routes.js.map