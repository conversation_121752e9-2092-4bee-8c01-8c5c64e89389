import Fastify from 'fastify';
import { env } from './config/env';
import { logger } from './config/logger';
import { connectDatabase } from './config/database';

import paymentRoutes from './modules/payment.routes';
import stripeRoutes from './modules/stripe.routes';
import transactionRoutes from './modules/transaction.routes';
import protocolRoutes from './modules/protocol.routes';
import logsRoutes from './modules/logs.routes';

const isDevelopment = env.NODE_ENV === 'development';

export async function createApp() {
  const app = Fastify({
    logger: logger,
    trustProxy: true,
    disableRequestLogging: !isDevelopment,
  });

  await app.register(import('@fastify/helmet'), {
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
  });

  await app.register(import('@fastify/cors'), {
    origin: '*', // Allow all origins
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  });

  await app.register(import('@fastify/rate-limit'), {
    max: env.RATE_LIMIT_MAX,
    timeWindow: env.RATE_LIMIT_WINDOW,
  });

  app.addHook('onRequest', async (request) => {
    request.log.info({ url: request.url, method: request.method }, 'Incoming request');
  });

  app.addHook('onResponse', async (request, reply) => {
    request.log.info(
      {
        url: request.url,
        method: request.method,
        statusCode: reply.statusCode,
        responseTime: reply.getResponseTime()
      },
      'Request completed'
    );
  });

  app.setErrorHandler(async (error, request, reply) => {
    request.log.error({ error, url: request.url, method: request.method }, 'Request error');

    if (error.validation) {
      return reply.status(400).send({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Request validation failed',
        details: error.validation,
      });
    }

    if (error.statusCode && error.statusCode < 500) {
      return reply.status(error.statusCode).send({
        success: false,
        error: error.code || 'CLIENT_ERROR',
        message: error.message,
      });
    }

    return reply.status(500).send({
      success: false,
      error: 'INTERNAL_SERVER_ERROR',
      message: isDevelopment ? error.message : 'An unexpected error occurred',
    });
  });

  app.setNotFoundHandler(async (request, reply) => {
    return reply.status(404).send({
      success: false,
      error: 'NOT_FOUND',
      message: `Route ${request.method} ${request.url} not found`,
    });
  });

  app.get('/health', async () => {
    return {
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        environment: env.NODE_ENV,
      }
    };
  });

  await app.register(paymentRoutes, { prefix: '/api/v1' });
  await app.register(stripeRoutes, { prefix: '/api/v1' });
  await app.register(transactionRoutes, { prefix: '/api/v1' });
  await app.register(protocolRoutes, { prefix: '/api/v1' });
  await app.register(logsRoutes, { prefix: '/api/v1' });

  const stripeEncryptedRoutes = await import('./modules/stripe-encrypted.routes');
  await app.register(stripeEncryptedRoutes.default, { prefix: '/api/v1/stripe' });

  const { receiptRoutes } = await import('./modules/receipt.routes');
  await app.register(receiptRoutes, { prefix: '/api/v1' });

  const { realTimePaymentRoutes } = await import('./modules/realTimePayment.routes');
  await app.register(realTimePaymentRoutes, { prefix: '/api/v1' });

  const paxRoutes = await import('./modules/pax.routes');
  await app.register(paxRoutes.default, { prefix: '/api/v1' });

  // Import and register webhook routes
  const webhookRoutes = await import('./modules/webhook.routes');
  await app.register(webhookRoutes.default, { prefix: '/api/v1' });

  // Import and register enhanced payment routes
  const enhancedPaymentRoutes = await import('./modules/enhanced-payment.routes');
  await app.register(enhancedPaymentRoutes.default, { prefix: '/api/v1' });

  // Import and register Stripe Terminal routes
  const stripeTerminalRoutes = await import('./modules/stripe-terminal.routes');
  await app.register(stripeTerminalRoutes.default, { prefix: '/api/v1' });

  // Import and register Novalnet routes
  const novalnetRoutes = await import('./modules/novalnet.routes');
  await app.register(novalnetRoutes.default, { prefix: '/api/v1' });

  // Import and register Novalnet webhook routes
  const novalnetWebhookRoutes = await import('./modules/novalnet-webhook.routes');
  await app.register(novalnetWebhookRoutes.default, { prefix: '/api/v1' });

  // Import and register Move Payment routes
  const moveRoutes = await import('./modules/move.routes');
  await app.register(moveRoutes.default, { prefix: '/api/v1' });

  // Import and register Skrill Payment routes
  const skrillRoutes = await import('./modules/skrill.routes');
  await app.register(skrillRoutes.skrillRoutes, { prefix: '/api/v1' });

  // Import and register Nuvei Payment routes
  const nuveiRoutes = await import('./modules/nuvei.routes');
  await app.register(nuveiRoutes.default, { prefix: '/api/v1' });

  // Import and register XMoney Payment routes
  const xmoneyRoutes = await import('./modules/xmoney.routes');
  await app.register(xmoneyRoutes.default, { prefix: '/api/v1' });

  // Import and register Payoneer Payment routes
  const payoneerRoutes = await import('./modules/payoneer.routes');
  await app.register(payoneerRoutes.default, { prefix: '/api/v1' });

  // Import and register Square Payment routes
  const squareRoutes = await import('./modules/square.routes');
  await app.register(squareRoutes.default, { prefix: '/api/v1' });

  // Import and register Vivid Money Payment routes
  const vividRoutes = await import('./modules/vivid.routes');
  await app.register(vividRoutes.default, { prefix: '/api/v1' });

  // Import and register Viva Wallet Payment routes
  const vivaRoutes = await import('./modules/viva.routes');
  await app.register(vivaRoutes.default, { prefix: '/api/v1' });

  // Import and register branding routes
  const brandingRoutes = await import('./modules/branding.routes');
  await app.register(brandingRoutes.default, { prefix: '/api' });

  return app;
}

export async function startServer() {
  try {
    await connectDatabase();
    const app = await createApp();

    await app.listen({
      port: env.PORT,
      host: '0.0.0.0'
    });

    logger.info(`Server listening on port ${env.PORT} in ${env.NODE_ENV} mode`);
    return app;
  } catch (error) {
    logger.error({ error }, 'Failed to start server');
    process.exit(1);
  }
}
