import { z } from 'zod';
declare const protocolMessageSchema: z.ZodObject<{
    protocolEventCode: z.ZodEnum<["101.1", "101.3", "101.5", "101.8"]>;
    transactionId: z.ZodString;
    stripePaymentIntentId: z.ZodOptional<z.ZodString>;
    amount: z.ZodNumber;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    amount?: number;
    stripePaymentIntentId?: string;
    metadata?: Record<string, any>;
    transactionId?: string;
    protocolEventCode?: "101.1" | "101.3" | "101.5" | "101.8";
}, {
    amount?: number;
    stripePaymentIntentId?: string;
    metadata?: Record<string, any>;
    transactionId?: string;
    protocolEventCode?: "101.1" | "101.3" | "101.5" | "101.8";
}>;
export interface ProtocolMessage {
    protocolEventCode: '101.1' | '101.3' | '101.5' | '101.8';
    mti: string;
    transactionId: string;
    stripePaymentIntentId?: string;
    timestamp: string;
    de2_pan?: string;
    de3_processingCode?: string;
    de4_transactionAmount?: number;
    de11_stan?: string;
    de12_localTime?: string;
    de13_localDate?: string;
    de18_merchantCategoryCode?: string;
    de37_retrievalReferenceNumber?: string;
    de38_approvalCode?: string;
    de39_responseCode?: string;
    de41_cardAcceptorTerminalId?: string;
    de42_cardAcceptorIdCode?: string;
}
export declare class ProtocolService {
    private generateSTAN;
    private generateRRN;
    private formatTime;
    private formatDate;
    private createProtocolMessage;
    private getMTIForProtocol;
    sendProtocolMessage(params: z.infer<typeof protocolMessageSchema>): Promise<any>;
    getProtocolDescription(code: string): string;
}
export declare const protocolService: ProtocolService;
export {};
//# sourceMappingURL=protocolService.d.ts.map