"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.captureIntent = exports.createIntent = exports.getConnectionToken = void 0;
const stripeService_1 = require("../services/stripeService");
const zod_1 = require("zod");
const logger_1 = require("../config/logger");
const controllerLogger = logger_1.logger.child({ module: 'stripe-controller' });
const createIntentSchema = zod_1.z.object({
    amount: zod_1.z.number().positive().int(),
    currency: zod_1.z.string().length(3),
    payment_method_types: zod_1.z.array(zod_1.z.string()).optional().default(['card']),
    capture_method: zod_1.z.enum(['automatic', 'manual']).optional().default('automatic'),
    metadata: zod_1.z.record(zod_1.z.string()).optional(),
});
const captureIntentSchema = zod_1.z.object({
    paymentIntentId: zod_1.z.string().min(1),
    amountToCapture: zod_1.z.number().positive().int().optional(),
});
const getConnectionToken = async (_request, reply) => {
    try {
        controllerLogger.info('Processing connection token request');
        const token = await stripeService_1.stripeService.createConnectionToken();
        return reply.send({
            success: true,
            data: { secret: token.secret }
        });
    }
    catch (error) {
        controllerLogger.error({ error }, 'Failed to get connection token');
        return reply.status(500).send({
            success: false,
            error: 'STRIPE_CONNECTION_TOKEN_ERROR',
            message: 'Failed to create connection token'
        });
    }
};
exports.getConnectionToken = getConnectionToken;
const createIntent = async (request, reply) => {
    try {
        const validatedBody = createIntentSchema.parse(request.body);
        controllerLogger.info({ amount: validatedBody.amount, currency: validatedBody.currency }, 'Creating payment intent');
        // Pass the validated body directly, which now includes payment_method_types and capture_method with defaults
        const intent = await stripeService_1.stripeService.createPaymentIntent(validatedBody);
        return reply.send({
            success: true,
            data: {
                client_secret: intent.client_secret,
                id: intent.id,
                amount: intent.amount,
                currency: intent.currency,
                status: intent.status,
                payment_method_types: intent.payment_method_types,
                capture_method: intent.capture_method
            }
        });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            controllerLogger.warn({ error: error.errors }, 'Invalid payment intent request');
            return reply.status(400).send({
                success: false,
                error: 'VALIDATION_ERROR',
                message: 'Invalid request parameters',
                details: error.errors
            });
        }
        controllerLogger.error({ error }, 'Failed to create payment intent');
        return reply.status(500).send({
            success: false,
            error: 'STRIPE_PAYMENT_INTENT_ERROR',
            message: 'Failed to create payment intent'
        });
    }
};
exports.createIntent = createIntent;
const captureIntent = async (request, reply) => {
    try {
        const validatedBody = captureIntentSchema.parse(request.body);
        controllerLogger.info({ paymentIntentId: validatedBody.paymentIntentId }, 'Capturing payment intent');
        const intent = await stripeService_1.stripeService.capturePaymentIntent(validatedBody);
        return reply.send({
            success: true,
            data: {
                id: intent.id,
                status: intent.status,
                amount_received: intent.amount_received
            }
        });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            controllerLogger.warn({ error: error.errors }, 'Invalid capture request');
            return reply.status(400).send({
                success: false,
                error: 'VALIDATION_ERROR',
                message: 'Invalid request parameters',
                details: error.errors
            });
        }
        controllerLogger.error({ error }, 'Failed to capture payment intent');
        return reply.status(500).send({
            success: false,
            error: 'STRIPE_CAPTURE_ERROR',
            message: 'Failed to capture payment intent'
        });
    }
};
exports.captureIntent = captureIntent;
//# sourceMappingURL=stripeController.js.map