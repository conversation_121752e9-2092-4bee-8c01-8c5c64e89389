/**
 * Nuvei Payment Service
 *
 * Service for handling Nuvei Payment Gateway integration using Payment Page
 * Implements Nuvei Payment Page API according to official documentation
 * https://docs.nuvei.com/documentation/accept-payment/payment-page/quick-start-for-payment-page/
 */
export interface NuveiPaymentRequest {
    orderId: string;
    amount: number;
    currency?: string;
    successUrl: string;
    cancelUrl: string;
    payerName: string;
    payerEmail?: string;
    languageCode?: string;
}
export interface NuveiPaymentResponse {
    sessionToken?: string;
    orderId?: string;
    status: string;
    redirectUrl?: string;
    qrCodeData?: string;
    id?: string;
    amount?: number;
    currency?: string;
    payment_url?: string;
    redirect_url?: string;
    qr_code?: string;
}
export interface NuveiServiceResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
    details?: any;
}
declare class NuveiPaymentService {
    private client;
    private merchantId;
    private merchantSiteId;
    private secretKey;
    private baseUrl;
    private environment;
    constructor();
    /**
     * Generate checksum for Nuvei API requests (legacy method)
     */
    private generateApiChecksum;
    /**
     * Generate SHA-256 checksum for Nuvei Payment Page
     * According to Nuvei documentation: concatenate secret key + all parameter values in order
     */
    private generatePaymentPageChecksum;
    /**
     * Generate timestamp in Nuvei format: YYYY-MM-DD.HH:MM:SS
     */
    private generateTimestamp;
    /**
     * Create Nuvei Payment Page URL according to official documentation
     */
    createPaymentPageUrl(params: NuveiPaymentRequest): Promise<NuveiServiceResponse<{
        payment_url: string;
    }>>;
    /**
     * Create a new Nuvei payment session (now uses Payment Page)
     */
    createPayment(params: NuveiPaymentRequest): Promise<NuveiServiceResponse<NuveiPaymentResponse>>;
    /**
     * Legacy create payment method (kept for backward compatibility)
     */
    createPaymentLegacy(params: NuveiPaymentRequest): Promise<NuveiServiceResponse<NuveiPaymentResponse>>;
    /**
     * Get payment status
     */
    getPaymentStatus(orderId: string): Promise<NuveiServiceResponse<any>>;
    /**
     * Health check for Nuvei service
     */
    healthCheck(): Promise<NuveiServiceResponse<{
        status: string;
    }>>;
}
export declare const nuveiPaymentService: NuveiPaymentService;
export {};
//# sourceMappingURL=nuveiPaymentService.d.ts.map