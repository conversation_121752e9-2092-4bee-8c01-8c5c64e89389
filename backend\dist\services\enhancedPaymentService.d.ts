/**
 * Enhanced Payment Service
 *
 * Integrates Stripe Terminal payments with Stripe and webhook processing
 * Handles the complete payment lifecycle from card reading to confirmation
 */
import Stripe from 'stripe';
export interface PaymentRequest {
    amount: number;
    currency?: string;
    description?: string;
    metadata?: Record<string, string>;
    terminalId: string;
    merchantName: string;
    cardData?: {
        encryptedPan?: string;
        maskedPan?: string;
        expiryDate?: string;
        cardholderName?: string;
        cardBrand?: string;
        cardType?: string;
        emvData?: any;
        ksn?: string;
    };
}
export interface PaymentResult {
    success: boolean;
    paymentIntent?: Stripe.PaymentIntent;
    paymentMethod?: Stripe.PaymentMethod;
    error?: string;
    transactionId?: string;
    receiptData?: ReceiptData;
}
export interface ReceiptData {
    transactionId: string;
    amount: number;
    currency: string;
    cardBrand: string;
    cardLast4: string;
    cardType: string;
    timestamp: string;
    merchantName: string;
    terminalId: string;
    approvalCode?: string;
    authCode?: string;
    rrn?: string;
    status: 'approved' | 'declined' | 'pending';
}
export declare class EnhancedPaymentService {
    /**
     * Process payment with encrypted card data from PAX terminal
     */
    processTerminalPayment(request: PaymentRequest): Promise<PaymentResult>;
    /**
     * Create payment method from encrypted card data
     */
    private createPaymentMethodFromEncryptedData;
    /**
     * Create payment intent
     */
    private createPaymentIntent;
    /**
     * Generate receipt data
     */
    private generateReceiptData;
    /**
     * Decrypt card data (placeholder - implement actual decryption)
     */
    private decryptCardData;
    /**
     * Handle webhook confirmation of payment
     */
    handleWebhookConfirmation(paymentIntentId: string, status: 'succeeded' | 'failed' | 'canceled'): Promise<void>;
    /**
     * Get payment status
     */
    getPaymentStatus(paymentIntentId: string): Promise<{
        status: string;
        amount?: number;
        currency?: string;
        created?: number;
        metadata?: Stripe.Metadata;
    }>;
    /**
     * Cancel payment
     */
    cancelPayment(paymentIntentId: string): Promise<boolean>;
    /**
     * Log payment events
     */
    private logPaymentEvent;
}
export declare const enhancedPaymentService: EnhancedPaymentService;
//# sourceMappingURL=enhancedPaymentService.d.ts.map