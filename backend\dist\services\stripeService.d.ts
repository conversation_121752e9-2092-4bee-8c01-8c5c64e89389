import Stripe from 'stripe';
import { z } from 'zod';
declare const createPaymentIntentSchema: z.ZodObject<{
    amount: z.ZodNumber;
    currency: z.ZodString;
    payment_method_types: z.ZodDefault<z.ZodOptional<z.<PERSON>od<PERSON><PERSON>y<z.ZodString, "many">>>;
    capture_method: z.ZodDefault<z.ZodOptional<z.ZodEnum<["automatic", "manual"]>>>;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    amount?: number;
    currency?: string;
    metadata?: Record<string, string>;
    payment_method_types?: string[];
    capture_method?: "automatic" | "manual";
}, {
    amount?: number;
    currency?: string;
    metadata?: Record<string, string>;
    payment_method_types?: string[];
    capture_method?: "automatic" | "manual";
}>;
declare const capturePaymentIntentSchema: z.ZodObject<{
    paymentIntentId: z.ZodString;
    amountToCapture: z.<PERSON>od<PERSON>ptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    paymentIntentId?: string;
    amountToCapture?: number;
}, {
    paymentIntentId?: string;
    amountToCapture?: number;
}>;
export declare class StripeService {
    createConnectionToken(): Promise<Stripe.Terminal.ConnectionToken>;
    createPaymentIntent(params: z.infer<typeof createPaymentIntentSchema>): Promise<Stripe.PaymentIntent>;
    capturePaymentIntent(params: z.infer<typeof capturePaymentIntentSchema>): Promise<Stripe.PaymentIntent>;
    retrievePaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent>;
}
export declare const stripeService: StripeService;
export {};
//# sourceMappingURL=stripeService.d.ts.map