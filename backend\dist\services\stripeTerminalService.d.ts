import Stripe from 'stripe';
export interface TerminalLocation {
    id: string;
    display_name: string;
    address: {
        line1: string;
        line2?: string;
        city: string;
        state: string;
        postal_code: string;
        country: string;
    };
}
export interface TerminalReader {
    id: string;
    object: 'terminal.reader';
    device_type: string;
    ip_address?: string;
    label: string;
    location: string;
    serial_number: string;
    status: 'online' | 'offline';
}
export interface PaymentIntentData {
    amount: number;
    currency: string;
    payment_method_types: string[];
    capture_method: 'automatic' | 'manual';
    metadata?: Record<string, string>;
}
export interface ConnectionToken {
    secret: string;
    location?: string;
}
export declare class StripeTerminalService {
    private stripe;
    private defaultLocation?;
    constructor();
    /**
     * Create a connection token for terminal SDK
     */
    createConnectionToken(location?: string): Promise<ConnectionToken>;
    /**
     * Create or get default location
     */
    ensureLocation(): Promise<TerminalLocation>;
    /**
     * List all terminal readers
     */
    listReaders(): Promise<TerminalReader[]>;
    /**
     * Get a specific terminal reader
     */
    getReader(readerId: string): Promise<TerminalReader>;
    /**
     * Create a payment intent for terminal processing
     */
    createPaymentIntent(data: PaymentIntentData): Promise<Stripe.PaymentIntent>;
    /**
     * Process payment on terminal reader
     */
    processPayment(readerId: string, paymentIntentId: string): Promise<Stripe.PaymentIntent>;
    /**
     * Cancel payment intent
     */
    cancelPaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent>;
    /**
     * Capture payment intent (for manual capture)
     */
    capturePaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent>;
    /**
     * Create refund for a payment
     */
    createRefund(paymentIntentId: string, amount?: number): Promise<Stripe.Refund>;
    /**
     * Get terminal reader status
     */
    getReaderStatus(readerId: string): Promise<{
        status: string;
        last_seen_at?: number;
    }>;
    /**
     * Simulate payment for testing (when no physical terminal available)
     */
    simulatePayment(data: PaymentIntentData): Promise<Stripe.PaymentIntent>;
    /**
     * Get payment method details from payment intent
     */
    getPaymentMethodDetails(paymentIntentId: string): Promise<any>;
}
export declare const stripeTerminalService: StripeTerminalService;
//# sourceMappingURL=stripeTerminalService.d.ts.map