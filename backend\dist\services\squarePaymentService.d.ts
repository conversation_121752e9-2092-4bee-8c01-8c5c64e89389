/**
 * Square Payment Service
 *
 * Production-level Square Web Payments SDK and Payments API integration
 * Implements card payments, payment links, and secure payment processing
 * https://developer.squareup.com/docs/web-payments/overview
 * https://developer.squareup.com/docs/payments-api/take-payments/card-payments
 */
export interface SquarePaymentRequest {
    orderId: string;
    amount: number;
    currency?: string;
    successUrl: string;
    cancelUrl: string;
    payerName: string;
    payerEmail?: string;
    languageCode?: string;
}
export interface SquarePaymentTokenRequest {
    sourceId: string;
    orderId: string;
    amount: number;
    currency?: string;
    payerName: string;
    payerEmail?: string;
    locationId?: string;
}
export interface SquarePaymentResponse {
    id: string;
    status: string;
    checkout_url?: string;
    payment_url?: string;
    amount: number;
    currency: string;
    application_id?: string;
    location_id?: string;
    receipt_url?: string;
}
export interface SquareServiceResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
    details?: any;
}
/**
 * Square Payment Service Class
 * Uses official Square Node.js SDK for production-level integration
 */
declare class SquarePaymentService {
    private client;
    private httpClient;
    private accessToken;
    private applicationId;
    private environment;
    private locationId;
    constructor();
    /**
     * Create a new Square payment
     */
    createPayment(params: SquarePaymentRequest): Promise<SquareServiceResponse<SquarePaymentResponse>>;
    /**
     * Process payment token from Web Payments SDK
     * Creates a payment using a token generated by the frontend
     */
    processPaymentToken(params: SquarePaymentTokenRequest): Promise<SquareServiceResponse<any>>;
    /**
     * Get payment status
     */
    getPaymentStatus(paymentId: string): Promise<SquareServiceResponse<any>>;
    /**
     * Health check for Square service
     */
    healthCheck(): Promise<SquareServiceResponse<{
        status: string;
    }>>;
    /**
     * Generate QR code data for payment
     */
    generateQRCodeData(checkoutUrl: string): string;
    /**
     * Get supported payment methods
     */
    getSupportedPaymentMethods(): string[];
    /**
     * Get supported currencies
     */
    getSupportedCurrencies(): string[];
}
export declare const squarePaymentService: SquarePaymentService;
export {};
//# sourceMappingURL=squarePaymentService.d.ts.map