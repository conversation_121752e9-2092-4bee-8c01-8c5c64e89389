import mongoose, { Document } from 'mongoose';
export interface IProtocolMessage extends Document {
    protocol: string;
    mti: string;
    amount: number;
    pan?: string;
    stan: string;
    authCode?: string;
    origStan?: string;
    timestamp: string;
}
declare const _default: mongoose.Model<IProtocolMessage, {}, {}, {}, mongoose.Document<unknown, {}, IProtocolMessage, {}> & IProtocolMessage & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=ProtocolMessage.mongo.d.ts.map