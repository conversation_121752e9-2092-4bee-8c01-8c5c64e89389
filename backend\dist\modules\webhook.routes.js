"use strict";
/**
 * Stripe Webhook Routes
 *
 * Handles Stripe webhook events for PAX terminal transactions
 * Processes payment confirmations, failures, and other events
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = webhookRoutes;
const stripe_1 = __importDefault(require("stripe"));
const SystemLog_1 = require("../models/SystemLog");
const stripe = new stripe_1.default(process.env.STRIPE_SECRET_KEY, {
    apiVersion: '2024-04-10',
});
async function webhookRoutes(fastify) {
    /**
     * Stripe Webhook Endpoint
     * Handles all Stripe webhook events
     */
    fastify.post('/webhook', async (request, reply) => {
        try {
            const signature = request.headers['stripe-signature'];
            const rawBody = Buffer.from(JSON.stringify(request.body));
            if (!signature) {
                return reply.status(400).send({
                    success: false,
                    error: 'Missing Stripe signature header'
                });
            }
            // Verify webhook signature
            const event = await verifyWebhookSignature(rawBody, signature);
            if (!event) {
                return reply.status(400).send({
                    success: false,
                    error: 'Invalid webhook signature'
                });
            }
            // Log webhook event
            await logWebhookEvent(event);
            // Handle the event
            const result = await handleWebhookEvent(event);
            fastify.log.info('Webhook processed successfully', {
                eventType: event.type,
                eventId: event.id,
                result
            });
            reply.send({
                success: true,
                eventType: event.type,
                eventId: event.id,
                processed: result.processed
            });
        }
        catch (error) {
            fastify.log.error('Webhook processing failed', error);
            reply.status(500).send({
                success: false,
                error: error instanceof Error ? error.message : 'Webhook processing failed'
            });
        }
    });
    /**
     * Webhook health check
     */
    fastify.get('/webhook/health', async (_request, reply) => {
        reply.send({
            success: true,
            message: 'Webhook endpoint is healthy',
            timestamp: new Date().toISOString()
        });
    });
}
/**
 * Verify Stripe webhook signature
 */
async function verifyWebhookSignature(rawBody, signature) {
    try {
        const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
        if (!webhookSecret) {
            console.error('STRIPE_WEBHOOK_SECRET not configured');
            return null;
        }
        const event = stripe.webhooks.constructEvent(rawBody, signature, webhookSecret);
        return event;
    }
    catch (error) {
        console.error('Webhook signature verification failed:', error);
        return null;
    }
}
/**
 * Handle different Stripe webhook events
 */
async function handleWebhookEvent(event) {
    try {
        switch (event.type) {
            case 'payment_intent.succeeded':
                return await handlePaymentIntentSucceeded(event.data.object);
            case 'payment_intent.payment_failed':
                return await handlePaymentIntentFailed(event.data.object);
            case 'payment_intent.canceled':
                return await handlePaymentIntentCanceled(event.data.object);
            case 'charge.succeeded':
                return await handleChargeSucceeded(event.data.object);
            case 'charge.failed':
                return await handleChargeFailed(event.data.object);
            case 'payment_method.attached':
                return await handlePaymentMethodAttached(event.data.object);
            default:
                console.log(`Unhandled event type: ${event.type}`);
                return { processed: false };
        }
    }
    catch (error) {
        console.error(`Error handling webhook event ${event.type}:`, error);
        return { processed: false };
    }
}
/**
 * Handle successful payment intent
 */
async function handlePaymentIntentSucceeded(paymentIntent) {
    try {
        console.log('PaymentIntent succeeded:', paymentIntent.id);
        // Extract metadata
        const metadata = paymentIntent.metadata || {};
        const terminalId = metadata.terminal_id || 'unknown';
        const merchantName = metadata.merchant_name || 'Unknown Merchant';
        const cardType = metadata.card_type || 'unknown';
        const cardBrand = metadata.card_brand || 'unknown';
        // Get payment method details
        const paymentMethod = paymentIntent.payment_method;
        const amount = paymentIntent.amount; // Amount in cents
        const currency = paymentIntent.currency;
        // Get charges data safely
        const charges = paymentIntent.charges?.data || [];
        const firstCharge = charges[0];
        // Generate transaction record
        const transactionData = {
            transactionId: paymentIntent.id,
            type: 'CARD_PAYMENT',
            title: 'PAX Terminal Payment Successful',
            amount: amount / 100, // Convert to dollars
            currency: currency.toUpperCase(),
            status: 'SUCCESS',
            timestamp: new Date(),
            terminalInfo: {
                terminalId,
                merchantName,
                cardType,
                cardBrand,
                paymentMethodId: paymentMethod
            },
            stripeData: {
                paymentIntentId: paymentIntent.id,
                chargeId: firstCharge?.id,
                receiptUrl: firstCharge?.receipt_url,
                authorizationCode: firstCharge?.authorization_code
            }
        };
        // Log successful transaction
        await logTransactionEvent('payment_success', transactionData);
        // Here you could:
        // 1. Update your database with transaction details
        // 2. Send confirmation emails/SMS
        // 3. Update inventory if applicable
        // 4. Trigger receipt printing
        // 5. Update analytics/reporting
        console.log('Payment processed successfully:', {
            paymentIntentId: paymentIntent.id,
            amount: amount / 100,
            currency,
            terminalId
        });
        return {
            processed: true,
            action: 'payment_recorded'
        };
    }
    catch (error) {
        console.error('Error processing successful payment:', error);
        return { processed: false, action: 'error' };
    }
}
/**
 * Handle failed payment intent
 */
async function handlePaymentIntentFailed(paymentIntent) {
    try {
        console.log('PaymentIntent failed:', paymentIntent.id);
        const metadata = paymentIntent.metadata || {};
        const terminalId = metadata.terminal_id || 'unknown';
        const amount = paymentIntent.amount;
        const currency = paymentIntent.currency;
        const lastPaymentError = paymentIntent.last_payment_error;
        // Log failed transaction
        await logTransactionEvent('payment_failed', {
            transactionId: paymentIntent.id,
            type: 'CARD_PAYMENT_FAILED',
            amount: amount / 100,
            currency: currency.toUpperCase(),
            terminalId,
            error: {
                code: lastPaymentError?.code,
                message: lastPaymentError?.message,
                type: lastPaymentError?.type
            }
        });
        console.log('Payment failed:', {
            paymentIntentId: paymentIntent.id,
            error: lastPaymentError?.message,
            terminalId
        });
        return {
            processed: true,
            action: 'payment_failure_logged'
        };
    }
    catch (error) {
        console.error('Error processing failed payment:', error);
        return { processed: false, action: 'error' };
    }
}
/**
 * Handle canceled payment intent
 */
async function handlePaymentIntentCanceled(paymentIntent) {
    try {
        console.log('PaymentIntent canceled:', paymentIntent.id);
        const metadata = paymentIntent.metadata || {};
        const terminalId = metadata.terminal_id || 'unknown';
        await logTransactionEvent('payment_canceled', {
            transactionId: paymentIntent.id,
            type: 'CARD_PAYMENT_CANCELED',
            terminalId,
            canceledAt: new Date()
        });
        return {
            processed: true,
            action: 'payment_cancellation_logged'
        };
    }
    catch (error) {
        console.error('Error processing canceled payment:', error);
        return { processed: false, action: 'error' };
    }
}
/**
 * Handle successful charge
 */
async function handleChargeSucceeded(charge) {
    try {
        console.log('Charge succeeded:', charge.id);
        // Additional charge-specific processing
        const receiptUrl = charge.receipt_url;
        if (receiptUrl) {
            console.log('Receipt available at:', receiptUrl);
        }
        return {
            processed: true,
            action: 'charge_processed'
        };
    }
    catch (error) {
        console.error('Error processing successful charge:', error);
        return { processed: false, action: 'error' };
    }
}
/**
 * Handle failed charge
 */
async function handleChargeFailed(charge) {
    try {
        console.log('Charge failed:', charge.id);
        await logTransactionEvent('charge_failed', {
            chargeId: charge.id,
            paymentIntentId: charge.payment_intent,
            failureCode: charge.failure_code,
            failureMessage: charge.failure_message,
            amount: charge.amount / 100
        });
        return {
            processed: true,
            action: 'charge_failure_logged'
        };
    }
    catch (error) {
        console.error('Error processing failed charge:', error);
        return { processed: false, action: 'error' };
    }
}
/**
 * Handle payment method attached
 */
async function handlePaymentMethodAttached(paymentMethod) {
    try {
        console.log('Payment method attached:', paymentMethod.id);
        // Log payment method creation for security/audit purposes
        await logTransactionEvent('payment_method_attached', {
            paymentMethodId: paymentMethod.id,
            type: paymentMethod.type,
            cardBrand: paymentMethod.card?.brand,
            cardLast4: paymentMethod.card?.last4,
            customerId: paymentMethod.customer
        });
        return {
            processed: true,
            action: 'payment_method_logged'
        };
    }
    catch (error) {
        console.error('Error processing payment method attachment:', error);
        return { processed: false, action: 'error' };
    }
}
/**
 * Log webhook events to system log
 */
async function logWebhookEvent(event) {
    try {
        const systemLog = new SystemLog_1.SystemLog({
            level: 'info',
            category: 'webhook',
            message: `Stripe webhook received: ${event.type}`,
            details: {
                eventId: event.id,
                eventType: event.type,
                created: event.created,
                livemode: event.livemode,
                objectId: event.data.object.id
            },
            timestamp: new Date().toISOString()
        });
        await systemLog.save();
    }
    catch (error) {
        console.error('Failed to log webhook event:', error);
    }
}
/**
 * Log transaction events
 */
async function logTransactionEvent(eventType, data) {
    try {
        const systemLog = new SystemLog_1.SystemLog({
            level: eventType.includes('failed') || eventType.includes('error') ? 'error' : 'info',
            category: 'transaction',
            message: `Transaction event: ${eventType}`,
            details: {
                eventType,
                ...data,
                processedAt: new Date().toISOString()
            },
            timestamp: new Date().toISOString()
        });
        await systemLog.save();
    }
    catch (error) {
        console.error('Failed to log transaction event:', error);
    }
}
//# sourceMappingURL=webhook.routes.js.map