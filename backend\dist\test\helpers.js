"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mockProtocolService = exports.mockStripeService = void 0;
exports.createTestApp = createTestApp;
exports.createMockTransaction = createMockTransaction;
exports.createMockProtocolMessage = createMockProtocolMessage;
exports.createMockStripePaymentIntent = createMockStripePaymentIntent;
exports.createMockConnectionToken = createMockConnectionToken;
exports.makeRequest = makeRequest;
exports.expectSuccessResponse = expectSuccessResponse;
exports.expectErrorResponse = expectErrorResponse;
exports.expectValidationError = expectValidationError;
const app_1 = require("../app");
const vitest_1 = require("vitest");
async function createTestApp() {
    const app = await (0, app_1.createApp)();
    return app;
}
exports.mockStripeService = {
    createConnectionToken: vitest_1.vi.fn(),
    createPaymentIntent: vitest_1.vi.fn(),
    capturePaymentIntent: vitest_1.vi.fn(),
    retrievePaymentIntent: vitest_1.vi.fn(),
};
exports.mockProtocolService = {
    sendProtocolMessage: vitest_1.vi.fn(),
    getProtocolDescription: vitest_1.vi.fn(),
};
function createMockTransaction(overrides = {}) {
    return {
        _id: '507f1f77bcf86cd799439011',
        amount: 1000,
        status: 'success',
        protocolCode: '101.1',
        stripePaymentIntentId: 'pi_test_123456789',
        receiptUrl: 'https://example.com/receipt/123',
        metadata: { test: true },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...overrides,
    };
}
function createMockProtocolMessage(overrides = {}) {
    return {
        protocolEventCode: '101.1',
        mti: '0100',
        transactionId: '507f1f77bcf86cd799439011',
        timestamp: new Date().toISOString(),
        de4_transactionAmount: 1000,
        de11_stan: '123456',
        de12_localTime: '123456',
        de13_localDate: '1225',
        de18_merchantCategoryCode: '5999',
        de37_retrievalReferenceNumber: '123456789012',
        de41_cardAcceptorTerminalId: 'PAX920PRO',
        de42_cardAcceptorIdCode: 'MERCHANT001',
        ...overrides,
    };
}
function createMockStripePaymentIntent(overrides = {}) {
    return {
        id: 'pi_test_123456789',
        object: 'payment_intent',
        amount: 1000,
        currency: 'usd',
        status: 'requires_payment_method',
        client_secret: 'pi_test_123456789_secret_test',
        created: Math.floor(Date.now() / 1000),
        ...overrides,
    };
}
function createMockConnectionToken(overrides = {}) {
    return {
        id: 'ctoken_test_123456789',
        object: 'terminal.connection_token',
        secret: 'pst_test_123456789_secret',
        created: Math.floor(Date.now() / 1000),
        ...overrides,
    };
}
async function makeRequest(app, method, url, payload, headers) {
    const response = await app.inject({
        method,
        url,
        payload,
        headers: {
            'Content-Type': 'application/json',
            ...headers,
        },
    });
    return {
        statusCode: response.statusCode,
        body: JSON.parse(response.body),
        headers: response.headers,
    };
}
function expectSuccessResponse(response, expectedData) {
    (0, vitest_1.expect)(response.statusCode).toBe(200);
    (0, vitest_1.expect)(response.body.success).toBe(true);
    if (expectedData) {
        (0, vitest_1.expect)(response.body.data).toMatchObject(expectedData);
    }
}
function expectErrorResponse(response, expectedStatusCode, expectedError) {
    (0, vitest_1.expect)(response.statusCode).toBe(expectedStatusCode);
    (0, vitest_1.expect)(response.body.success).toBe(false);
    if (expectedError) {
        (0, vitest_1.expect)(response.body.error).toBe(expectedError);
    }
}
function expectValidationError(response) {
    expectErrorResponse(response, 400, 'VALIDATION_ERROR');
    (0, vitest_1.expect)(response.body.details).toBeDefined();
}
//# sourceMappingURL=helpers.js.map