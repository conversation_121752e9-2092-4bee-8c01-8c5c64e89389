"use strict";
/**
 * Skrill Payment Routes
 *
 * API routes for Skrill Quick Checkout integration
 * Follows the same pattern as Move Payment routes
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.skrillRoutes = skrillRoutes;
const zod_1 = require("zod");
const skrillPaymentService_1 = require("../services/skrillPaymentService");
const Transaction_mongo_1 = __importDefault(require("../models/Transaction.mongo"));
const logger_1 = require("../config/logger");
const skrillLogger = logger_1.logger.child({ module: 'skrill-routes' });
// Validation schemas
const createPaymentSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
    amount: zod_1.z.number().positive().int(),
    skrillEmail: zod_1.z.string().email().optional(),
    languageCode: zod_1.z.string().length(2).default('en'),
    successUrl: zod_1.z.string().url(),
    cancelUrl: zod_1.z.string().url(),
    payerName: zod_1.z.string().min(1),
    type: zod_1.z.enum(['qr', 'url']).optional().default('qr'), // 'qr' for QR code, 'url' for payment URL
});
const tokenParamsSchema = zod_1.z.object({
    sessionId: zod_1.z.string().min(1),
});
const paymentUrlQuerySchema = zod_1.z.object({
    returnUrl: zod_1.z.string().url(),
});
async function skrillRoutes(fastify) {
    /**
     * Create Skrill payment
     */
    fastify.post('/skrill/payment', async (request, reply) => {
        try {
            console.log('=== RAW REQUEST BODY ===', JSON.stringify(request.body, null, 2));
            const validatedBody = createPaymentSchema.parse(request.body);
            skrillLogger.info('Creating Skrill payment', {
                orderId: validatedBody.orderId,
                amount: validatedBody.amount,
                type: validatedBody.type,
            });
            // Check for existing transaction (idempotency)
            const existingTransaction = await Transaction_mongo_1.default.findOne({
                'metadata.order_id': validatedBody.orderId,
                paymentProvider: 'skrill',
            });
            if (existingTransaction) {
                skrillLogger.info('Found existing Skrill transaction', {
                    orderId: validatedBody.orderId,
                    transactionId: existingTransaction._id,
                });
                // Handle different response types for existing transactions
                if (validatedBody.type === 'url') {
                    // For URL type, get the payment URL using the existing session ID
                    const paymentUrlResponse = await skrillPaymentService_1.skrillPaymentService.getPaymentUrl(existingTransaction.metadata.skrill_session_id, validatedBody.successUrl);
                    if (paymentUrlResponse.success && paymentUrlResponse.data) {
                        return reply.send({
                            success: true,
                            data: {
                                transaction_id: existingTransaction._id,
                                order_id: validatedBody.orderId,
                                session_id: existingTransaction.metadata.skrill_session_id,
                                expires_at: paymentUrlResponse.data.expires_at,
                                redirect_url: paymentUrlResponse.data.redirect_url,
                                amount: validatedBody.amount,
                                currency: 'EUR',
                                type: 'url',
                            },
                        });
                    }
                }
                // Default QR code response for existing transactions
                return reply.send({
                    success: true,
                    data: {
                        transaction_id: existingTransaction._id,
                        order_id: validatedBody.orderId,
                        session_id: existingTransaction.metadata.skrill_session_id,
                        expires_at: existingTransaction.metadata.expires_at,
                        redirect_url: existingTransaction.metadata.redirect_url,
                        amount: validatedBody.amount,
                        currency: 'EUR',
                        type: validatedBody.type || 'qr',
                    },
                });
            }
            console.log('=== CALLING SKRILL PAYMENT SERVICE ===');
            // Create payment with Skrill
            const skrillResponse = await skrillPaymentService_1.skrillPaymentService.createPayment({
                orderId: validatedBody.orderId,
                amount: validatedBody.amount,
                payerName: validatedBody.payerName,
                successUrl: validatedBody.successUrl,
                cancelUrl: validatedBody.cancelUrl,
                languageCode: validatedBody.languageCode,
            });
            if (!skrillResponse.success) {
                skrillLogger.warn('Skrill payment creation failed', {
                    orderId: validatedBody.orderId,
                    error: skrillResponse.error,
                    message: skrillResponse.message,
                });
                return reply.status(400).send({
                    success: false,
                    error: skrillResponse.error || 'PAYMENT_CREATION_FAILED',
                    message: skrillResponse.message || 'Failed to create payment',
                });
            }
            console.log('=== SKRILL SERVICE RESPONSE ===', JSON.stringify(skrillResponse, null, 2));
            // Save transaction to database
            console.log('=== SAVING TRANSACTION TO DATABASE ===');
            const transaction = new Transaction_mongo_1.default({
                amount: validatedBody.amount,
                status: 'pending',
                currency: 'eur', // Skrill typically uses EUR (lowercase for schema)
                paymentMethod: 'skrill_payment',
                paymentProvider: 'skrill',
                metadata: {
                    payment_provider: 'skrill',
                    order_id: validatedBody.orderId,
                    skrill_session_id: skrillResponse.data?.session_id,
                    payer_name: validatedBody.payerName,
                    language_code: validatedBody.languageCode,
                    success_url: validatedBody.successUrl,
                    cancel_url: validatedBody.cancelUrl,
                    expires_at: skrillResponse.data?.expires_at,
                    redirect_url: skrillResponse.data?.redirect_url,
                },
            });
            console.log('=== TRANSACTION OBJECT ===', JSON.stringify(transaction.toObject(), null, 2));
            try {
                await transaction.save();
                console.log('=== TRANSACTION SAVED SUCCESSFULLY ===');
            }
            catch (saveError) {
                console.log('=== DATABASE SAVE ERROR ===');
                console.log('Save Error:', saveError);
                console.log('=== END DATABASE SAVE ERROR ===');
                throw saveError;
            }
            skrillLogger.info('Skrill payment created successfully', {
                orderId: validatedBody.orderId,
                transaction_id: transaction._id,
                session_id: skrillResponse.data?.session_id,
                type: validatedBody.type,
            });
            // Handle different response types based on the 'type' parameter
            if (validatedBody.type === 'url') {
                // For URL type, get the payment URL using the session ID
                const paymentUrlResponse = await skrillPaymentService_1.skrillPaymentService.getPaymentUrl(skrillResponse.data?.session_id || '', validatedBody.successUrl);
                if (paymentUrlResponse.success && paymentUrlResponse.data) {
                    return reply.send({
                        success: true,
                        data: {
                            transaction_id: transaction._id,
                            order_id: validatedBody.orderId,
                            session_id: skrillResponse.data?.session_id,
                            expires_at: paymentUrlResponse.data.expires_at,
                            redirect_url: paymentUrlResponse.data.redirect_url,
                            amount: validatedBody.amount,
                            currency: 'EUR',
                            type: 'url',
                        },
                    });
                }
            }
            // Default QR code response (or fallback if URL generation fails)
            return reply.send({
                success: true,
                data: {
                    transaction_id: transaction._id,
                    order_id: validatedBody.orderId,
                    session_id: skrillResponse.data?.session_id,
                    expires_at: skrillResponse.data?.expires_at,
                    redirect_url: skrillResponse.data?.redirect_url,
                    amount: validatedBody.amount,
                    currency: 'EUR',
                    type: validatedBody.type || 'qr',
                },
            });
        }
        catch (error) {
            skrillLogger.error('Skrill payment creation failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid request parameters',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Get QR code for Skrill payment
     */
    fastify.get('/skrill/payment/:sessionId/qr-code', async (request, reply) => {
        try {
            const { sessionId } = tokenParamsSchema.parse(request.params);
            skrillLogger.info('Getting Skrill payment QR code', { sessionId });
            // Get QR code from Skrill
            const skrillResponse = await skrillPaymentService_1.skrillPaymentService.getQRCode(sessionId);
            if (!skrillResponse.success) {
                skrillLogger.warn('Skrill QR code retrieval failed', {
                    sessionId,
                    error: skrillResponse.error,
                    message: skrillResponse.message,
                });
                return reply.status(400).send({
                    success: false,
                    error: skrillResponse.error || 'QR_CODE_FAILED',
                    message: skrillResponse.message || 'Failed to get QR code',
                });
            }
            skrillLogger.info('Skrill QR code retrieved successfully', {
                sessionId,
                qrCodeUrl: skrillResponse.data?.qrCodeUrl,
            });
            return reply.send({
                success: true,
                data: skrillResponse.data,
            });
        }
        catch (error) {
            skrillLogger.error('Skrill QR code retrieval failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid parameters',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Get payment URL for Skrill payment
     */
    fastify.get('/skrill/payment/:sessionId/pay-url', async (request, reply) => {
        try {
            const { sessionId } = tokenParamsSchema.parse(request.params);
            const { returnUrl } = paymentUrlQuerySchema.parse(request.query);
            skrillLogger.info('Getting Skrill payment URL', { sessionId, returnUrl });
            // Get payment URL from Skrill
            const skrillResponse = await skrillPaymentService_1.skrillPaymentService.getPaymentUrl(sessionId, returnUrl);
            if (!skrillResponse.success) {
                skrillLogger.warn('Skrill payment URL retrieval failed', {
                    sessionId,
                    returnUrl,
                    error: skrillResponse.error,
                    message: skrillResponse.message,
                });
                return reply.status(400).send({
                    success: false,
                    error: skrillResponse.error || 'PAYMENT_URL_FAILED',
                    message: skrillResponse.message || 'Failed to get payment URL',
                });
            }
            skrillLogger.info('Skrill payment URL retrieved successfully', {
                sessionId,
                returnUrl,
                redirect_url: skrillResponse.data?.redirect_url,
            });
            return reply.send({
                success: true,
                data: skrillResponse.data,
            });
        }
        catch (error) {
            skrillLogger.error('Skrill payment URL retrieval failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid parameters',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Get Skrill payment details and status
     */
    fastify.get('/skrill/payment/:sessionId', async (request, reply) => {
        try {
            const { sessionId } = tokenParamsSchema.parse(request.params);
            skrillLogger.info('Getting Skrill payment details', { sessionId });
            // Get payment details from Skrill
            const skrillResponse = await skrillPaymentService_1.skrillPaymentService.getPaymentDetails(sessionId);
            if (!skrillResponse.success) {
                skrillLogger.warn('Skrill payment details retrieval failed', {
                    sessionId,
                    error: skrillResponse.error,
                    message: skrillResponse.message,
                });
                return reply.status(400).send({
                    success: false,
                    error: skrillResponse.error || 'PAYMENT_DETAILS_FAILED',
                    message: skrillResponse.message || 'Failed to get payment details',
                });
            }
            skrillLogger.info('Skrill payment details retrieved successfully', {
                sessionId,
                status: skrillResponse.data?.status,
            });
            return reply.send({
                success: true,
                data: skrillResponse.data,
            });
        }
        catch (error) {
            skrillLogger.error('Skrill payment details retrieval failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid parameters',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
}
//# sourceMappingURL=skrill.routes.js.map