import { FastifyRequest, FastifyReply } from 'fastify';
interface ProcessPaymentBody {
    amount: number;
    currency: string;
    payment_method?: string;
    payment_method_types?: string[];
    confirm?: boolean;
    return_url?: string;
    customer_email?: string;
    description?: string;
}
interface RefundPaymentBody {
    payment_intent_id: string;
    amount?: number;
    reason?: 'duplicate' | 'fraudulent' | 'requested_by_customer';
}
export declare const realTimePaymentController: {
    processPayment(request: FastifyRequest<{
        Body: ProcessPaymentBody;
    }>, reply: FastifyReply): Promise<never>;
    confirmPayment(request: FastifyRequest<{
        Body: {
            payment_intent_id: string;
            payment_method?: string;
        };
    }>, reply: FastifyReply): Promise<never>;
    refundPayment(request: FastifyRequest<{
        Body: RefundPaymentBody;
    }>, reply: FastifyReply): Promise<never>;
    getPaymentStatus(request: FastifyRequest<{
        Params: {
            payment_intent_id: string;
        };
    }>, reply: FastifyReply): Promise<never>;
    cancelPayment(request: FastifyRequest<{
        Body: {
            payment_intent_id: string;
        };
    }>, reply: FastifyReply): Promise<never>;
};
export {};
//# sourceMappingURL=realTimePaymentController.d.ts.map