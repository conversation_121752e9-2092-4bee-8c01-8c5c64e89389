"use strict";
/**
 * Novalnet Webhook Routes
 *
 * Handles Novalnet webhook events for payment notifications
 * Processes payment confirmations, failures, and other transaction events
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = novalnetWebhookRoutes;
const novalnetService_1 = require("../services/novalnetService");
const SystemLog_1 = require("../models/SystemLog");
const Transaction_mongo_1 = __importDefault(require("../models/Transaction.mongo"));
const logger_1 = require("../config/logger");
const webhookLogger = logger_1.logger.child({ module: 'novalnet-webhook' });
async function novalnetWebhookRoutes(fastify) {
    /**
     * Novalnet Webhook Endpoint
     * Handles all Novalnet webhook events
     */
    fastify.post('/novalnet/webhook', async (request, reply) => {
        try {
            const signature = request.headers['x-nn-signature'];
            const rawBody = JSON.stringify(request.body);
            if (!signature) {
                webhookLogger.warn('Missing Novalnet signature header');
                return reply.status(400).send({
                    success: false,
                    error: 'Missing Novalnet signature header'
                });
            }
            // Verify webhook signature
            const isValidSignature = novalnetService_1.novalnetService.verifyWebhookSignature(rawBody, signature);
            if (!isValidSignature) {
                webhookLogger.warn('Invalid Novalnet webhook signature');
                return reply.status(400).send({
                    success: false,
                    error: 'Invalid webhook signature'
                });
            }
            const webhookEvent = request.body;
            // Log webhook event
            await logWebhookEvent(webhookEvent);
            // Handle the event
            const result = await handleNovalnetWebhookEvent(webhookEvent);
            webhookLogger.info('Novalnet webhook processed successfully', {
                eventType: webhookEvent.event.type,
                tid: webhookEvent.event.tid,
                result
            });
            reply.send({
                success: true,
                eventType: webhookEvent.event.type,
                tid: webhookEvent.event.tid,
                processed: result.processed
            });
        }
        catch (error) {
            webhookLogger.error('Novalnet webhook processing failed', error);
            reply.status(500).send({
                success: false,
                error: error instanceof Error ? error.message : 'Webhook processing failed'
            });
        }
    });
    /**
     * Novalnet webhook health check
     */
    fastify.get('/novalnet/webhook/health', async (_request, reply) => {
        reply.send({
            success: true,
            message: 'Novalnet webhook endpoint is healthy',
            timestamp: new Date().toISOString()
        });
    });
}
/**
 * Handle different Novalnet webhook events
 */
async function handleNovalnetWebhookEvent(event) {
    try {
        switch (event.event.type) {
            case 'PAYMENT':
                return await handlePaymentEvent(event);
            case 'TRANSACTION_CAPTURE':
                return await handleTransactionCapture(event);
            case 'TRANSACTION_CANCEL':
                return await handleTransactionCancel(event);
            case 'TRANSACTION_REFUND':
                return await handleTransactionRefund(event);
            case 'TRANSACTION_UPDATE':
                return await handleTransactionUpdate(event);
            case 'CREDIT':
                return await handleCreditEvent(event);
            default:
                webhookLogger.info(`Unhandled Novalnet event type: ${event.event.type}`);
                return { processed: false };
        }
    }
    catch (error) {
        webhookLogger.error(`Error handling Novalnet webhook event ${event.event.type}:`, error);
        return { processed: false };
    }
}
/**
 * Handle payment event (successful payment)
 */
async function handlePaymentEvent(event) {
    try {
        const { transaction } = event;
        webhookLogger.info('Processing Novalnet payment event', {
            tid: transaction.tid,
            amount: transaction.amount,
            status: transaction.status,
            order_no: transaction.order_no
        });
        // Find existing transaction by order number or create new one
        let dbTransaction = await Transaction_mongo_1.default.findOne({
            $or: [
                { 'metadata.novalnet_tid': transaction.tid },
                { 'metadata.order_no': transaction.order_no }
            ]
        });
        if (!dbTransaction) {
            // Create new transaction record
            dbTransaction = new Transaction_mongo_1.default({
                amount: transaction.amount / 100, // Convert from cents to dollars
                currency: transaction.currency.toLowerCase(),
                status: mapNovalnetStatusToTransactionStatus(transaction.status),
                paymentMethod: 'card',
                metadata: {
                    novalnet_tid: transaction.tid,
                    novalnet_payment_type: transaction.payment_type,
                    novalnet_status_code: transaction.status_code,
                    order_no: transaction.order_no,
                    test_mode: transaction.test_mode === 1,
                    payment_provider: 'novalnet'
                }
            });
        }
        else {
            // Update existing transaction
            dbTransaction.status = mapNovalnetStatusToTransactionStatus(transaction.status);
            dbTransaction.metadata = {
                ...dbTransaction.metadata,
                novalnet_tid: transaction.tid,
                novalnet_payment_type: transaction.payment_type,
                novalnet_status_code: transaction.status_code,
                updated_via_webhook: true
            };
            dbTransaction.updatedAt = new Date().toISOString();
        }
        await dbTransaction.save();
        // Log successful transaction processing
        await logTransactionEvent('novalnet_payment_processed', {
            transactionId: dbTransaction._id,
            novalnetTid: transaction.tid,
            amount: transaction.amount / 100,
            currency: transaction.currency,
            status: transaction.status,
            orderNo: transaction.order_no,
            paymentType: transaction.payment_type
        });
        webhookLogger.info('Novalnet payment processed successfully', {
            dbTransactionId: dbTransaction._id,
            novalnetTid: transaction.tid,
            amount: transaction.amount / 100,
            status: transaction.status
        });
        return {
            processed: true,
            action: 'payment_recorded'
        };
    }
    catch (error) {
        webhookLogger.error('Error processing Novalnet payment event:', error);
        return { processed: false, action: 'error' };
    }
}
/**
 * Handle transaction capture event
 */
async function handleTransactionCapture(event) {
    try {
        const { transaction } = event;
        webhookLogger.info('Processing Novalnet capture event', {
            tid: transaction.tid,
            amount: transaction.amount
        });
        // Update transaction status to captured
        const dbTransaction = await Transaction_mongo_1.default.findOneAndUpdate({ 'metadata.novalnet_tid': transaction.tid }, {
            status: 'success',
            'metadata.captured': true,
            'metadata.captured_amount': transaction.amount,
            updatedAt: new Date().toISOString()
        }, { new: true });
        if (dbTransaction) {
            await logTransactionEvent('novalnet_transaction_captured', {
                transactionId: dbTransaction._id,
                novalnetTid: transaction.tid,
                capturedAmount: transaction.amount / 100
            });
        }
        return {
            processed: true,
            action: 'transaction_captured'
        };
    }
    catch (error) {
        webhookLogger.error('Error processing Novalnet capture event:', error);
        return { processed: false, action: 'error' };
    }
}
/**
 * Handle transaction cancel event
 */
async function handleTransactionCancel(event) {
    try {
        const { transaction } = event;
        webhookLogger.info('Processing Novalnet cancel event', {
            tid: transaction.tid
        });
        // Update transaction status to cancelled
        const dbTransaction = await Transaction_mongo_1.default.findOneAndUpdate({ 'metadata.novalnet_tid': transaction.tid }, {
            status: 'cancelled',
            'metadata.cancelled': true,
            updatedAt: new Date().toISOString()
        }, { new: true });
        if (dbTransaction) {
            await logTransactionEvent('novalnet_transaction_cancelled', {
                transactionId: dbTransaction._id,
                novalnetTid: transaction.tid
            });
        }
        return {
            processed: true,
            action: 'transaction_cancelled'
        };
    }
    catch (error) {
        webhookLogger.error('Error processing Novalnet cancel event:', error);
        return { processed: false, action: 'error' };
    }
}
/**
 * Handle transaction refund event
 */
async function handleTransactionRefund(event) {
    try {
        const { transaction } = event;
        webhookLogger.info('Processing Novalnet refund event', {
            tid: transaction.tid,
            amount: transaction.amount
        });
        // Update transaction status to refunded
        const dbTransaction = await Transaction_mongo_1.default.findOneAndUpdate({ 'metadata.novalnet_tid': transaction.tid }, {
            status: 'refunded',
            'metadata.refunded': true,
            'metadata.refund_amount': transaction.amount,
            updatedAt: new Date().toISOString()
        }, { new: true });
        if (dbTransaction) {
            await logTransactionEvent('novalnet_transaction_refunded', {
                transactionId: dbTransaction._id,
                novalnetTid: transaction.tid,
                refundAmount: transaction.amount / 100
            });
        }
        return {
            processed: true,
            action: 'transaction_refunded'
        };
    }
    catch (error) {
        webhookLogger.error('Error processing Novalnet refund event:', error);
        return { processed: false, action: 'error' };
    }
}
/**
 * Handle transaction update event
 */
async function handleTransactionUpdate(event) {
    try {
        const { transaction } = event;
        webhookLogger.info('Processing Novalnet update event', {
            tid: transaction.tid,
            status: transaction.status
        });
        // Update transaction with new status
        const dbTransaction = await Transaction_mongo_1.default.findOneAndUpdate({ 'metadata.novalnet_tid': transaction.tid }, {
            status: mapNovalnetStatusToTransactionStatus(transaction.status),
            'metadata.novalnet_status_code': transaction.status_code,
            updatedAt: new Date().toISOString()
        }, { new: true });
        if (dbTransaction) {
            await logTransactionEvent('novalnet_transaction_updated', {
                transactionId: dbTransaction._id,
                novalnetTid: transaction.tid,
                newStatus: transaction.status
            });
        }
        return {
            processed: true,
            action: 'transaction_updated'
        };
    }
    catch (error) {
        webhookLogger.error('Error processing Novalnet update event:', error);
        return { processed: false, action: 'error' };
    }
}
/**
 * Handle credit event
 */
async function handleCreditEvent(event) {
    try {
        const { transaction } = event;
        webhookLogger.info('Processing Novalnet credit event', {
            tid: transaction.tid,
            amount: transaction.amount
        });
        await logTransactionEvent('novalnet_credit_received', {
            novalnetTid: transaction.tid,
            amount: transaction.amount / 100,
            currency: transaction.currency
        });
        return {
            processed: true,
            action: 'credit_processed'
        };
    }
    catch (error) {
        webhookLogger.error('Error processing Novalnet credit event:', error);
        return { processed: false, action: 'error' };
    }
}
/**
 * Map Novalnet status to transaction status
 */
function mapNovalnetStatusToTransactionStatus(novalnetStatus) {
    switch (novalnetStatus) {
        case 'CONFIRMED':
        case 'SUCCESS':
            return 'success';
        case 'PENDING':
        case 'ON_HOLD':
            return 'pending';
        case 'CANCELLED':
            return 'cancelled';
        case 'REFUNDED':
            return 'refunded';
        case 'FAILURE':
        case 'DEACTIVATED':
            return 'failed';
        default:
            return 'pending';
    }
}
/**
 * Log webhook event to system logs
 */
async function logWebhookEvent(event) {
    try {
        await SystemLog_1.SystemLog.create({
            level: 'info',
            category: 'payment',
            message: 'Novalnet webhook event received',
            details: {
                eventType: event.event.type,
                tid: event.event.tid,
                status: event.result.status,
                transactionStatus: event.transaction?.status,
                amount: event.transaction?.amount,
                currency: event.transaction?.currency,
                orderNo: event.transaction?.order_no
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        webhookLogger.error('Failed to log webhook event:', error);
    }
}
/**
 * Log transaction event to system logs
 */
async function logTransactionEvent(eventType, data) {
    try {
        await SystemLog_1.SystemLog.create({
            level: 'info',
            category: 'payment',
            message: `Novalnet transaction event: ${eventType}`,
            details: {
                eventType,
                ...data
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        webhookLogger.error('Failed to log transaction event:', error);
    }
}
//# sourceMappingURL=novalnet-webhook.routes.js.map