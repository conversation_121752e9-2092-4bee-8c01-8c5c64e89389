/**
 * Payoneer Payment Service
 *
 * Service for handling Payoneer Checkout API integration
 * Supports payment creation, QR code generation, and payment status tracking
 */
export interface PayoneerPaymentRequest {
    orderId: string;
    amount: number;
    currency?: string;
    successUrl: string;
    cancelUrl: string;
    payerName: string;
    payerEmail?: string;
    languageCode?: string;
}
export interface PayoneerPaymentResponse {
    id: string;
    status: string;
    checkout_url: string;
    amount: number;
    currency: string;
}
export interface PayoneerServiceResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
    details?: any;
}
declare class PayoneerPaymentService {
    private client;
    private apiKey;
    private merchantId;
    private baseUrl;
    constructor();
    /**
     * Create a new Payoneer payment
     */
    createPayment(params: PayoneerPaymentRequest): Promise<PayoneerServiceResponse<PayoneerPaymentResponse>>;
    /**
     * Get payment status
     */
    getPaymentStatus(orderId: string): Promise<PayoneerServiceResponse<any>>;
    /**
     * Health check for Payoneer service
     */
    healthCheck(): Promise<PayoneerServiceResponse<{
        status: string;
    }>>;
    /**
     * Generate QR code data for payment
     */
    generateQRCodeData(checkoutUrl: string): string;
    /**
     * Get supported payment methods
     */
    getSupportedPaymentMethods(): string[];
    /**
     * Get supported currencies
     */
    getSupportedCurrencies(): string[];
}
export declare const payoneerPaymentService: PayoneerPaymentService;
export {};
//# sourceMappingURL=payoneerPaymentService.d.ts.map