/**
 * Test Viva Wallet Payment Creation with Basic Auth Only
 * 
 * Tests multiple Basic Auth approaches for payment creation
 * Run with: node test-viva-basic-only.js
 */

const https = require('https');

// Your Viva Wallet credentials
const VIVA_MERCHANT_ID = '30481af3-63d9-42cd-93ea-1937a972b76d';
const VIVA_API_KEY = 'SothunZ2FxVRMkq666sbxbxB6VNbJG';

console.log('🔐 Testing Viva Wallet Payment Creation with Basic Auth Only');
console.log('=' .repeat(60));
console.log('');

// Test multiple Basic Auth approaches
async function testBasicAuthApproaches() {
  const credentials = Buffer.from(`${VIVA_MERCHANT_ID}:${VIVA_API_KEY}`).toString('base64');
  
  const approaches = [
    {
      name: 'Full Payload (lowercase)',
      data: {
        amount: 100,
        customerTrns: 'Test payment with Basic Auth - Full',
        customer: {
          email: '<EMAIL>',
          fullName: 'Test Customer',
          requestLang: 'en-GB',
        },
        sourceCode: 'Default',
        merchantTrns: 'BASIC_FULL_' + Date.now(),
        currencyCode: '826',
        paymentTimeout: 1800,
      }
    },
    {
      name: 'Minimal Payload',
      data: {
        amount: 100,
        customerTrns: 'Test payment with Basic Auth - Minimal',
        sourceCode: 'Default',
        merchantTrns: 'BASIC_MIN_' + Date.now(),
      }
    },
    {
      name: 'Legacy Format (uppercase)',
      data: {
        Amount: 100,
        CustomerTrns: 'Test payment with Basic Auth - Legacy',
        SourceCode: 'Default',
        MerchantTrns: 'BASIC_LEG_' + Date.now(),
      }
    },
    {
      name: 'Alternative Format',
      data: {
        amount: 100,
        description: 'Test payment with Basic Auth - Alt',
        source: 'Default',
        reference: 'BASIC_ALT_' + Date.now(),
      }
    }
  ];

  for (const approach of approaches) {
    console.log(`🔵 Testing: ${approach.name}`);
    console.log('─'.repeat(40));
    
    await new Promise((resolve) => {
      const postData = JSON.stringify(approach.data);

      const options = {
        hostname: 'demo-api.vivapayments.com',
        port: 443,
        path: '/checkout/v2/orders',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`,
          'Accept': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        }
      };

      console.log('📤 Request Data:', JSON.stringify(approach.data, null, 2));
      console.log('');

      const req = https.request(options, (res) => {
        console.log(`📥 Response Status: ${res.statusCode}`);
        
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          console.log('📥 Response Body:', data || '(empty)');
          console.log('');

          if (res.statusCode === 200 || res.statusCode === 201) {
            console.log('🎉 SUCCESS! Basic Auth works with this approach');
            try {
              const response = JSON.parse(data);
              if (response.orderCode || response.OrderCode) {
                const orderCode = response.orderCode || response.OrderCode;
                console.log('✅ Order Code:', orderCode);
                console.log('✅ Payment URL: https://demo.vivapayments.com/web/checkout?ref=' + orderCode);
              }
            } catch (e) {
              console.log('✅ Response received but not JSON');
            }
          } else if (res.statusCode === 401) {
            console.log('❌ Authentication failed - Basic Auth not supported');
          } else if (res.statusCode === 400) {
            console.log('❌ Bad request - Check payload format');
            try {
              const response = JSON.parse(data);
              console.log('❌ Error details:', response);
            } catch (e) {
              console.log('❌ Non-JSON error response');
            }
          } else {
            console.log('❌ Request failed');
          }

          console.log('─'.repeat(60));
          console.log('');
          resolve();
        });
      });

      req.on('error', (error) => {
        console.log('❌ Network Error:', error.message);
        console.log('─'.repeat(60));
        console.log('');
        resolve();
      });

      req.write(postData);
      req.end();
    });
  }
}

// Test if any alternative endpoints work with Basic Auth
async function testAlternativeEndpoints() {
  console.log('🔵 Testing Alternative Endpoints');
  console.log('─'.repeat(40));
  
  const credentials = Buffer.from(`${VIVA_MERCHANT_ID}:${VIVA_API_KEY}`).toString('base64');
  
  const endpoints = [
    '/api/orders',
    '/orders',
    '/checkout/orders',
    '/v1/orders',
    '/payment/orders'
  ];

  const testData = {
    amount: 100,
    customerTrns: 'Test alternative endpoint',
    sourceCode: 'Default',
    merchantTrns: 'ALT_EP_' + Date.now(),
  };

  for (const endpoint of endpoints) {
    console.log(`📍 Testing endpoint: ${endpoint}`);
    
    await new Promise((resolve) => {
      const postData = JSON.stringify(testData);

      const options = {
        hostname: 'demo-api.vivapayments.com',
        port: 443,
        path: endpoint,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`,
          'Accept': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        }
      };

      const req = https.request(options, (res) => {
        console.log(`   Status: ${res.statusCode}`);
        
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          if (res.statusCode === 200 || res.statusCode === 201) {
            console.log('   🎉 SUCCESS! This endpoint works');
            console.log('   Response:', data.substring(0, 100) + '...');
          } else if (res.statusCode === 404) {
            console.log('   ❌ Endpoint not found');
          } else if (res.statusCode === 401) {
            console.log('   ❌ Auth required');
          } else {
            console.log('   ❌ Failed');
          }
          resolve();
        });
      });

      req.on('error', () => {
        console.log('   ❌ Network error');
        resolve();
      });

      req.write(postData);
      req.end();
    });
  }
  
  console.log('');
}

// Main execution
async function main() {
  console.log('🚀 Starting Basic Auth Payment Creation Tests');
  console.log('');

  await testBasicAuthApproaches();
  await testAlternativeEndpoints();

  console.log('📋 SUMMARY:');
  console.log('');
  console.log('If any approach shows SUCCESS, we can use Basic Auth for payments!');
  console.log('If all fail with 401, then OAuth2 is definitely required.');
  console.log('If all fail with 400, we need to adjust the payload format.');
  console.log('');
  console.log('💡 The TypeScript service now tries Basic Auth first,');
  console.log('   then falls back to OAuth2 if Basic Auth fails.');
  console.log('');
}

main();
