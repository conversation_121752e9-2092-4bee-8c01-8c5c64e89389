/**
 * Test Viva Wallet Supported Currencies
 * 
 * Test different currencies to find which ones are supported by your account
 * Run with: node test-viva-currencies.js
 */

const https = require('https');
const querystring = require('querystring');

// Your Viva Wallet credentials
const VIVA_CLIENT_ID = '00pp9ggt8otvtzyfy3sv7y0d5u56oleukdkd7mma293z8.apps.vivapayments.com';
const VIVA_CLIENT_SECRET = 'QMusPDEy8xF34j9r5V9yOqS7yeM2pM';

// Production environment endpoints
const OAUTH_ENDPOINT = 'accounts.vivapayments.com';
const API_ENDPOINT = 'api.vivapayments.com';

console.log('🔐 Testing Viva Wallet Supported Currencies');
console.log('=' .repeat(60));
console.log('');

// Get OAuth2 token
async function getAccessToken() {
  return new Promise((resolve) => {
    const credentials = `${VIVA_CLIENT_ID}:${VIVA_CLIENT_SECRET}`;
    const base64Credentials = Buffer.from(credentials).toString('base64');
    
    const postData = querystring.stringify({
      grant_type: 'client_credentials'
    });

    const options = {
      hostname: OAUTH_ENDPOINT,
      port: 443,
      path: '/connect/token',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${base64Credentials}`,
        'Accept': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        if (res.statusCode === 200) {
          const tokenResponse = JSON.parse(data);
          resolve(tokenResponse.access_token);
        } else {
          console.log('❌ Failed to get access token');
          resolve(null);
        }
      });
    });

    req.on('error', () => {
      resolve(null);
    });

    req.write(postData);
    req.end();
  });
}

// Test a specific currency
async function testCurrency(accessToken, currencyCode, currencyName) {
  return new Promise((resolve) => {
    console.log(`🔍 Testing ${currencyName} (${currencyCode})`);

    const paymentData = {
      amount: 100,
      customerTrns: `Currency test: ${currencyName}`,
      customer: {
        email: '<EMAIL>',
        fullName: 'Currency Test Customer',
        requestLang: 'en-GB'
      },
      sourceCode: 'Default',
      merchantTrns: `CURR_TEST_${currencyCode}_${Date.now()}`,
      currencyCode: currencyCode,
      paymentTimeout: 1800
    };

    const postData = JSON.stringify(paymentData);

    const options = {
      hostname: API_ENDPOINT,
      port: 443,
      path: '/checkout/v2/orders',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log(`   ✅ ${currencyName} (${currencyCode}): SUPPORTED`);
          try {
            const response = JSON.parse(data);
            const orderCode = response.orderCode || response.OrderCode || Object.values(response)[0];
            console.log(`   📝 Order Code: ${orderCode}`);
          } catch (e) {
            console.log(`   📝 Payment created successfully`);
          }
          resolve({ currency: currencyName, code: currencyCode, supported: true });
        } else {
          try {
            const errorResponse = JSON.parse(data);
            if (errorResponse.message && errorResponse.message.includes('Currency')) {
              console.log(`   ❌ ${currencyName} (${currencyCode}): NOT SUPPORTED`);
              console.log(`   💡 Error: ${errorResponse.message}`);
            } else {
              console.log(`   ⚠️ ${currencyName} (${currencyCode}): OTHER ERROR`);
              console.log(`   💡 Error: ${errorResponse.message || 'Unknown error'}`);
            }
          } catch (e) {
            console.log(`   ❌ ${currencyName} (${currencyCode}): FAILED (${res.statusCode})`);
          }
          resolve({ currency: currencyName, code: currencyCode, supported: false });
        }
        console.log('');
      });
    });

    req.on('error', () => {
      console.log(`   ❌ ${currencyName} (${currencyCode}): NETWORK ERROR`);
      console.log('');
      resolve({ currency: currencyName, code: currencyCode, supported: false });
    });

    req.write(postData);
    req.end();
  });
}

// Main execution
async function main() {
  console.log('🚀 Getting OAuth2 access token...');
  const accessToken = await getAccessToken();
  
  if (!accessToken) {
    console.log('❌ Failed to get access token. Exiting.');
    return;
  }
  
  console.log('✅ Access token obtained successfully');
  console.log('');
  
  console.log('🔍 Testing Currency Support...');
  console.log('─'.repeat(40));
  
  // Test common currencies
  const currencies = [
    { code: '978', name: 'EUR' },
    { code: '826', name: 'GBP' },
    { code: '840', name: 'USD' },
    { code: '756', name: 'CHF' },
    { code: '752', name: 'SEK' },
    { code: '578', name: 'NOK' },
    { code: '208', name: 'DKK' },
    { code: '985', name: 'PLN' },
    { code: '203', name: 'CZK' },
    { code: '348', name: 'HUF' },
    { code: '946', name: 'RON' },
    { code: '975', name: 'BGN' },
    { code: '191', name: 'HRK' }
  ];
  
  const supportedCurrencies = [];
  
  for (const currency of currencies) {
    const result = await testCurrency(accessToken, currency.code, currency.name);
    if (result.supported) {
      supportedCurrencies.push(result);
    }
    
    // Small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('📋 SUMMARY');
  console.log('=' .repeat(60));
  
  if (supportedCurrencies.length > 0) {
    console.log('✅ SUPPORTED CURRENCIES:');
    supportedCurrencies.forEach(curr => {
      console.log(`   • ${curr.currency} (${curr.code})`);
    });
    console.log('');
    console.log('💡 RECOMMENDATION:');
    console.log(`   Use ${supportedCurrencies[0].currency} as your default currency`);
    console.log(`   Update your backend to use currency code: ${supportedCurrencies[0].code}`);
  } else {
    console.log('❌ NO SUPPORTED CURRENCIES FOUND');
    console.log('💡 This might indicate an account configuration issue');
    console.log('💡 Contact Viva support to enable currency support');
  }
  
  console.log('');
  console.log('📞 Support: https://developer.viva.com/');
  console.log('🌐 Dashboard: https://www.vivapayments.com/');
  console.log('=' .repeat(60));
}

main();
