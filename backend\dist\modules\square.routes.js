"use strict";
/**
 * Square Payment Routes
 *
 * Handles Square Payment Gateway processing endpoints
 * Provides API for creating payments, getting QR codes, and checking payment status
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = squareRoutes;
const zod_1 = require("zod");
const squarePaymentService_1 = require("../services/squarePaymentService");
const Transaction_mongo_1 = __importDefault(require("../models/Transaction.mongo"));
const logger_1 = require("../config/logger");
const env_1 = require("../config/env");
const squareLogger = (0, logger_1.createChildLogger)({ module: 'square-payment' });
// Helper function to get default currency based on environment
const getDefaultCurrency = () => {
    // Sandbox environment only supports USD, production supports EUR
    return env_1.env.SQUARE_ENVIRONMENT === 'sandbox' ? 'USD' : 'EUR';
};
// Validation schemas
const createPaymentSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
    amount: zod_1.z.number().positive(),
    currency: zod_1.z.string().default(getDefaultCurrency()),
    payerName: zod_1.z.string().min(1),
    payerEmail: zod_1.z.string().email().optional(),
    successUrl: zod_1.z.string().url(),
    cancelUrl: zod_1.z.string().url(),
    languageCode: zod_1.z.string().default('en'),
    type: zod_1.z.enum(['qr', 'url']).default('qr'),
});
const processPaymentTokenSchema = zod_1.z.object({
    sourceId: zod_1.z.string().min(1),
    orderId: zod_1.z.string().min(1),
    amount: zod_1.z.number().positive(),
    currency: zod_1.z.string().default('USD'),
    payerName: zod_1.z.string().min(1),
    payerEmail: zod_1.z.string().email().optional(),
    locationId: zod_1.z.string().optional(),
});
const orderIdParamsSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
});
async function squareRoutes(fastify) {
    /**
     * Create Square payment
     */
    fastify.post('/square/payment', async (request, reply) => {
        try {
            console.log('=== RAW REQUEST BODY ===', JSON.stringify(request.body, null, 2));
            const validatedBody = createPaymentSchema.parse(request.body);
            squareLogger.info('Creating Square payment', {
                orderId: validatedBody.orderId,
                amount: validatedBody.amount,
                currency: validatedBody.currency,
                payerName: validatedBody.payerName,
            });
            // Check for existing payment (idempotency)
            const existingTransaction = await Transaction_mongo_1.default.findOne({
                'metadata.order_id': validatedBody.orderId,
                'metadata.payment_provider': 'square',
            });
            if (existingTransaction) {
                squareLogger.info('Returning existing Square payment', {
                    orderId: validatedBody.orderId,
                    transactionId: existingTransaction._id,
                });
                // Return existing payment data
                return reply.send({
                    success: true,
                    data: {
                        transaction_id: existingTransaction._id,
                        order_id: validatedBody.orderId,
                        payment_id: existingTransaction.metadata.square_payment_id,
                        checkout_url: existingTransaction.metadata.checkout_url,
                        amount: validatedBody.amount,
                        currency: validatedBody.currency,
                        type: validatedBody.type,
                    },
                });
            }
            // Create payment with Square
            const squareResponse = await squarePaymentService_1.squarePaymentService.createPayment({
                orderId: validatedBody.orderId,
                amount: validatedBody.amount,
                currency: validatedBody.currency,
                successUrl: validatedBody.successUrl,
                cancelUrl: validatedBody.cancelUrl,
                payerName: validatedBody.payerName,
                payerEmail: validatedBody.payerEmail,
                languageCode: validatedBody.languageCode,
            });
            // Log the complete Square service response
            console.log('=== SQUARE SERVICE RESPONSE ===');
            console.log('Success:', squareResponse.success);
            console.log('Data:', squareResponse.data ? JSON.stringify(squareResponse.data, null, 2) : null);
            console.log('Error:', squareResponse.error);
            console.log('Message:', squareResponse.message);
            console.log('Details:', squareResponse.details);
            console.log('==============================');
            if (!squareResponse.success) {
                squareLogger.warn('Square payment creation failed', {
                    orderId: validatedBody.orderId,
                    error: squareResponse.error,
                    message: squareResponse.message,
                });
                return reply.status(400).send({
                    success: false,
                    error: squareResponse.error || 'PAYMENT_CREATION_FAILED',
                    message: squareResponse.message || 'Failed to create Square payment',
                    details: squareResponse.details,
                });
            }
            // Create transaction record
            console.log('=== CREATING TRANSACTION ===');
            console.log('Square Response Data ID:', squareResponse.data?.id);
            console.log('Square Response Data checkout_url:', squareResponse.data?.checkout_url);
            console.log('Square Response Data payment_url:', squareResponse.data?.payment_url);
            console.log('===========================');
            const transaction = new Transaction_mongo_1.default({
                amount: validatedBody.amount,
                currency: validatedBody.currency.toLowerCase(), // Convert to lowercase for database validation
                status: 'pending',
                payment_method: 'square',
                metadata: {
                    order_id: validatedBody.orderId,
                    payment_provider: 'square',
                    square_payment_id: squareResponse.data.id,
                    checkout_url: squareResponse.data.checkout_url || squareResponse.data.payment_url,
                    payer_name: validatedBody.payerName,
                    payer_email: validatedBody.payerEmail,
                    success_url: validatedBody.successUrl,
                    cancel_url: validatedBody.cancelUrl,
                    language_code: validatedBody.languageCode,
                    created_at: new Date().toISOString(),
                },
            });
            console.log('=== SAVING TRANSACTION ===');
            try {
                const savedTransaction = await transaction.save();
                console.log('Transaction saved successfully:', savedTransaction._id);
                squareLogger.info('Transaction saved to database', {
                    transactionId: savedTransaction._id,
                    orderId: validatedBody.orderId,
                });
                // Return response based on type
                if (validatedBody.type === 'url' && squareResponse.data.checkout_url) {
                    // For URL type, return the payment URL for opening in new tab
                    return reply.send({
                        success: true,
                        data: {
                            transaction_id: savedTransaction._id,
                            order_id: validatedBody.orderId,
                            payment_id: squareResponse.data.id,
                            redirect_url: squareResponse.data.checkout_url,
                            amount: validatedBody.amount,
                            currency: validatedBody.currency,
                            type: 'url',
                        },
                    });
                }
                // Default QR code response (or fallback if URL generation fails)
                return reply.send({
                    success: true,
                    data: {
                        transaction_id: savedTransaction._id,
                        order_id: validatedBody.orderId,
                        payment_id: squareResponse.data.id,
                        checkout_url: squareResponse.data.checkout_url,
                        qr_code_data: squareResponse.data.checkout_url,
                        amount: validatedBody.amount,
                        currency: validatedBody.currency,
                        type: validatedBody.type || 'qr',
                    },
                });
            }
            catch (dbError) {
                console.log('=== DATABASE ERROR ===');
                console.log('Error saving transaction:', dbError);
                console.log('=====================');
                squareLogger.error('Failed to save transaction to database', dbError);
                return reply.status(500).send({
                    success: false,
                    error: 'DATABASE_ERROR',
                    message: 'Failed to save transaction to database',
                });
            }
        }
        catch (error) {
            squareLogger.error('Square payment creation failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid request data',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Process Square payment token from Web Payments SDK
     */
    fastify.post('/square/payment/process', async (request, reply) => {
        try {
            console.log('=== RAW PAYMENT TOKEN REQUEST ===', JSON.stringify(request.body, null, 2));
            const validatedBody = processPaymentTokenSchema.parse(request.body);
            squareLogger.info('Processing Square payment token', {
                orderId: validatedBody.orderId,
                amount: validatedBody.amount,
                currency: validatedBody.currency,
                payerName: validatedBody.payerName,
            });
            // Process payment token with Square
            const squareResponse = await squarePaymentService_1.squarePaymentService.processPaymentToken({
                sourceId: validatedBody.sourceId,
                orderId: validatedBody.orderId,
                amount: validatedBody.amount,
                currency: validatedBody.currency,
                payerName: validatedBody.payerName,
                payerEmail: validatedBody.payerEmail,
                locationId: validatedBody.locationId,
            });
            if (!squareResponse.success) {
                squareLogger.warn('Square payment token processing failed', {
                    orderId: validatedBody.orderId,
                    error: squareResponse.error,
                    message: squareResponse.message,
                });
                return reply.status(400).send({
                    success: false,
                    error: squareResponse.error || 'PAYMENT_PROCESSING_FAILED',
                    message: squareResponse.message || 'Failed to process Square payment',
                    details: squareResponse.details,
                });
            }
            // Create transaction record
            const transaction = new Transaction_mongo_1.default({
                amount: validatedBody.amount,
                currency: validatedBody.currency.toLowerCase(), // Convert to lowercase for database validation
                status: 'success',
                payment_method: 'square',
                metadata: {
                    order_id: validatedBody.orderId,
                    payment_provider: 'square',
                    square_payment_id: squareResponse.data.id,
                    payer_name: validatedBody.payerName,
                    payer_email: validatedBody.payerEmail,
                    receipt_url: squareResponse.data.receipt_url,
                    created_at: new Date().toISOString(),
                },
            });
            const savedTransaction = await transaction.save();
            squareLogger.info('Square payment processed and transaction saved', {
                transactionId: savedTransaction._id,
                orderId: validatedBody.orderId,
                paymentId: squareResponse.data.id,
            });
            return reply.send({
                success: true,
                data: {
                    transaction_id: savedTransaction._id,
                    order_id: validatedBody.orderId,
                    payment_id: squareResponse.data.id,
                    status: squareResponse.data.status,
                    amount: validatedBody.amount,
                    currency: validatedBody.currency,
                    receipt_url: squareResponse.data.receipt_url,
                },
            });
        }
        catch (error) {
            squareLogger.error('Square payment token processing failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid request data',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Get Square payment status
     */
    fastify.get('/square/payment/:orderId/status', async (request, reply) => {
        try {
            const { orderId } = orderIdParamsSchema.parse(request.params);
            squareLogger.info('Getting Square payment status', { orderId });
            // Get payment status from Square
            const squareResponse = await squarePaymentService_1.squarePaymentService.getPaymentStatus(orderId);
            if (!squareResponse.success) {
                squareLogger.warn('Square payment status retrieval failed', {
                    orderId,
                    error: squareResponse.error,
                    message: squareResponse.message,
                });
                return reply.status(400).send({
                    success: false,
                    error: squareResponse.error || 'STATUS_RETRIEVAL_FAILED',
                    message: squareResponse.message || 'Failed to get payment status',
                    details: squareResponse.details,
                });
            }
            return reply.send({
                success: true,
                data: squareResponse.data,
            });
        }
        catch (error) {
            squareLogger.error('Square payment status retrieval failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid request parameters',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Get Square configuration for frontend
     */
    fastify.get('/square/config', async (request, reply) => {
        try {
            // Get locations to find the default location ID
            const { locationsApi } = squarePaymentService_1.squarePaymentService['client'];
            const locationsResponse = await locationsApi.listLocations();
            if (locationsResponse.result.errors && locationsResponse.result.errors.length > 0) {
                return reply.status(500).send({
                    success: false,
                    error: 'LOCATIONS_FETCH_FAILED',
                    message: 'Failed to fetch Square locations',
                    details: locationsResponse.result.errors,
                });
            }
            const locations = locationsResponse.result.locations || [];
            const defaultLocation = locations.find(loc => loc.status === 'ACTIVE') || locations[0];
            if (!defaultLocation) {
                return reply.status(500).send({
                    success: false,
                    error: 'NO_LOCATIONS_FOUND',
                    message: 'No active Square locations found',
                });
            }
            return reply.send({
                success: true,
                data: {
                    applicationId: env_1.env.SQUARE_APPLICATION_ID,
                    locationId: defaultLocation.id,
                    environment: env_1.env.SQUARE_ENVIRONMENT,
                    enabled: env_1.env.SQUARE_ENABLED,
                    currency: getDefaultCurrency(), // Add currency based on environment
                },
            });
        }
        catch (error) {
            squareLogger.error('Failed to get Square configuration', error);
            return reply.status(500).send({
                success: false,
                error: 'CONFIG_FETCH_FAILED',
                message: 'Failed to get Square configuration',
            });
        }
    });
    /**
     * Get Square locations to find the correct location ID
     */
    fastify.get('/square/locations', async (request, reply) => {
        try {
            squareLogger.info('Fetching Square locations');
            const { locationsApi } = squarePaymentService_1.squarePaymentService['client'];
            const response = await locationsApi.listLocations();
            if (response.result.errors && response.result.errors.length > 0) {
                squareLogger.error('Square locations fetch failed', {
                    errors: response.result.errors,
                });
                return reply.status(500).send({
                    success: false,
                    error: 'LOCATIONS_FETCH_FAILED',
                    message: response.result.errors[0].detail || 'Failed to fetch locations',
                    details: response.result.errors,
                });
            }
            const locations = response.result.locations || [];
            squareLogger.info('Square locations fetched successfully', {
                count: locations.length,
                locations: locations.map(loc => ({
                    id: loc.id,
                    name: loc.name,
                    status: loc.status,
                    type: loc.type,
                })),
            });
            return reply.send({
                success: true,
                data: {
                    locations: locations.map(location => ({
                        id: location.id,
                        name: location.name,
                        status: location.status,
                        type: location.type,
                        address: location.address,
                        timezone: location.timezone,
                        capabilities: location.capabilities,
                    })),
                    current_location_id: env_1.env.SQUARE_LOCATION_ID,
                },
            });
        }
        catch (error) {
            squareLogger.error('Failed to fetch Square locations', error);
            return reply.status(500).send({
                success: false,
                error: 'LOCATIONS_FETCH_FAILED',
                message: 'Failed to fetch Square locations',
            });
        }
    });
    /**
     * Square health check
     */
    fastify.get('/square/health', async (request, reply) => {
        try {
            squareLogger.info('Square health check requested');
            const healthResponse = await squarePaymentService_1.squarePaymentService.healthCheck();
            if (!healthResponse.success) {
                return reply.status(503).send({
                    success: false,
                    error: healthResponse.error || 'HEALTH_CHECK_FAILED',
                    message: healthResponse.message || 'Square service is not healthy',
                });
            }
            return reply.send({
                success: true,
                data: healthResponse.data,
            });
        }
        catch (error) {
            squareLogger.error('Square health check failed', error);
            return reply.status(500).send({
                success: false,
                error: 'HEALTH_CHECK_FAILED',
                message: 'Square health check failed',
            });
        }
    });
    /**
     * Square webhook handler
     */
    fastify.post('/square/webhook', async (request, reply) => {
        try {
            squareLogger.info('Square webhook received', { body: request.body });
            // Handle Square webhook events here
            // This is a placeholder implementation
            return reply.send({
                success: true,
                message: 'Webhook received',
            });
        }
        catch (error) {
            squareLogger.error('Square webhook processing failed', error);
            return reply.status(500).send({
                success: false,
                error: 'WEBHOOK_PROCESSING_FAILED',
                message: 'Failed to process webhook',
            });
        }
    });
}
//# sourceMappingURL=square.routes.js.map