import { FastifyRequest, FastifyReply } from 'fastify';
interface ReceiptParams {
    transactionId: string;
}
interface PrintReceiptBody {
    transactionId: string;
    copies?: number;
    customerCopy?: boolean;
}
export declare const receiptController: {
    generateReceipt(request: FastifyRequest<{
        Params: ReceiptParams;
    }>, reply: FastifyReply): Promise<never>;
    printReceipt(request: FastifyRequest<{
        Body: PrintReceiptBody;
    }>, reply: FastifyReply): Promise<never>;
    getReceiptFormats(request: FastifyRequest<{
        Params: ReceiptParams;
    }>, reply: FastifyReply): Promise<never>;
    emailReceipt(request: FastifyRequest<{
        Body: {
            transactionId: string;
            email: string;
            customerCopy?: boolean;
        };
    }>, reply: FastifyReply): Promise<never>;
    convertToHTML(textReceipt: string): string;
};
export {};
//# sourceMappingURL=receiptController.d.ts.map