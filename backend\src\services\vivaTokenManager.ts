import fs from 'fs/promises';
import path from 'path';
import * as cron from 'node-cron';
import { env } from '../config/env';
import { logger } from '../config/logger';

interface VivaTokenData {
  access_token: string;
  token_type: string;
  expires_in: number;
  expires_at: number;
  scope: string;
  created_at: number;
  environment: string;
  client_id: string;
}

interface VivaTokenFile {
  tokens: {
    [environment: string]: VivaTokenData;
  };
  last_updated: number;
}

export class VivaTokenManager {
  private tokenFilePath: string;
  private cronJob: cron.ScheduledTask | null = null;
  private isRefreshing = false;

  constructor() {
    // Create tokens directory under backend/apiaccess
    const tokensDir = path.join(process.cwd(), 'apiaccess');
    this.tokenFilePath = path.join(tokensDir, 'viva-tokens.json');
    this.ensureTokensDirectory();
    this.startTokenRefreshCron();
  }

  /**
   * Ensure the tokens directory exists
   */
  private async ensureTokensDirectory(): Promise<void> {
    try {
      const tokensDir = path.dirname(this.tokenFilePath);
      await fs.mkdir(tokensDir, { recursive: true });
      logger.info('Viva tokens directory ensured', { path: tokensDir });
    } catch (error) {
      logger.error('Failed to create tokens directory', error);
    }
  }

  /**
   * Load tokens from JSON file
   */
  private async loadTokensFromFile(): Promise<VivaTokenFile> {
    try {
      const fileContent = await fs.readFile(this.tokenFilePath, 'utf-8');
      const tokenFile: VivaTokenFile = JSON.parse(fileContent);
      return tokenFile;
    } catch (error) {
      // File doesn't exist or is invalid, return empty structure
      logger.info('No existing token file found, creating new one');
      return {
        tokens: {},
        last_updated: Date.now(),
      };
    }
  }

  /**
   * Save tokens to JSON file
   */
  private async saveTokensToFile(tokenFile: VivaTokenFile): Promise<void> {
    try {
      tokenFile.last_updated = Date.now();
      const fileContent = JSON.stringify(tokenFile, null, 2);
      await fs.writeFile(this.tokenFilePath, fileContent, 'utf-8');
      logger.info('Viva tokens saved to file', { 
        path: this.tokenFilePath,
        environments: Object.keys(tokenFile.tokens)
      });
    } catch (error) {
      logger.error('Failed to save tokens to file', error);
      throw error;
    }
  }

  /**
   * Get stored token for current environment
   */
  async getStoredToken(): Promise<VivaTokenData | null> {
    try {
      const tokenFile = await this.loadTokensFromFile();
      const environment = env.VIVA_ENVIRONMENT;
      const token = tokenFile.tokens[environment];

      if (!token) {
        logger.info('No stored token found for environment', { environment });
        return null;
      }

      // Check if token is still valid (with 5 minute buffer)
      const now = Date.now();
      const bufferTime = 5 * 60 * 1000; // 5 minutes
      
      if (token.expires_at <= now + bufferTime) {
        logger.info('Stored token is expired or expiring soon', {
          environment,
          expires_at: new Date(token.expires_at),
          now: new Date(now)
        });
        return null;
      }

      logger.info('Valid stored token found', {
        environment,
        expires_at: new Date(token.expires_at),
        scope: token.scope
      });

      return token;
    } catch (error) {
      logger.error('Failed to get stored token', error);
      return null;
    }
  }

  /**
   * Store new token
   */
  async storeToken(tokenData: {
    access_token: string;
    token_type: string;
    expires_in: number;
    scope: string;
  }): Promise<void> {
    try {
      const tokenFile = await this.loadTokensFromFile();
      const environment = env.VIVA_ENVIRONMENT;
      const now = Date.now();

      const vivaToken: VivaTokenData = {
        access_token: tokenData.access_token,
        token_type: tokenData.token_type,
        expires_in: tokenData.expires_in,
        expires_at: now + (tokenData.expires_in * 1000) - 60000, // Subtract 1 minute for safety
        scope: tokenData.scope,
        created_at: now,
        environment,
        client_id: env.VIVA_CLIENT_ID,
      };

      tokenFile.tokens[environment] = vivaToken;
      await this.saveTokensToFile(tokenFile);

      logger.info('New Viva token stored', {
        environment,
        expires_at: new Date(vivaToken.expires_at),
        scope: vivaToken.scope
      });
    } catch (error) {
      logger.error('Failed to store token', error);
      throw error;
    }
  }

  /**
   * Request new token from Viva API
   */
  async requestNewToken(): Promise<VivaTokenData | null> {
    if (this.isRefreshing) {
      logger.info('Token refresh already in progress, waiting...');
      return null;
    }

    this.isRefreshing = true;

    try {
      logger.info('Requesting new Viva OAuth2 token', {
        environment: env.VIVA_ENVIRONMENT,
        client_id: env.VIVA_CLIENT_ID
      });

      // Determine URLs based on environment
      const accountsUrl = env.VIVA_ACCOUNTS_URL || (env.VIVA_ENVIRONMENT === 'production' 
        ? 'https://accounts.vivapayments.com' 
        : 'https://demo-accounts.vivapayments.com');

      const credentials = `${env.VIVA_CLIENT_ID}:${env.VIVA_CLIENT_SECRET}`;
      const base64Credentials = Buffer.from(credentials).toString('base64');

      const response = await fetch(`${accountsUrl}/connect/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${base64Credentials}`,
          'Accept': 'application/json',
        },
        body: 'grant_type=client_credentials',
      });

      if (!response.ok) {
        const errorData = await response.text();
        logger.error('Failed to get OAuth2 token', {
          status: response.status,
          statusText: response.statusText,
          error: errorData
        });
        return null;
      }

      const tokenData: any = await response.json();

      // Validate token data structure
      if (!tokenData.access_token || !tokenData.token_type || !tokenData.expires_in || !tokenData.scope) {
        logger.error('Invalid token response structure', { tokenData });
        return null;
      }

      // Store the new token
      await this.storeToken(tokenData);

      // Return the stored token data
      return await this.getStoredToken();

    } catch (error) {
      logger.error('Error requesting new token', error);
      return null;
    } finally {
      this.isRefreshing = false;
    }
  }

  /**
   * Get valid token (from storage or request new one)
   */
  async getValidToken(): Promise<string | null> {
    try {
      // Try to get stored token first
      let token = await this.getStoredToken();

      // If no valid stored token, request a new one
      if (!token) {
        logger.info('No valid stored token, requesting new one');
        token = await this.requestNewToken();
      }

      if (!token) {
        logger.error('Failed to get valid token');
        return null;
      }

      return token.access_token;
    } catch (error) {
      logger.error('Error getting valid token', error);
      return null;
    }
  }

  /**
   * Start cron job to refresh tokens every 40 minutes
   */
  private startTokenRefreshCron(): void {
    // Run every 40 minutes: 0 */40 * * * *
    this.cronJob = cron.schedule('0 */40 * * * *', async () => {
      logger.info('Starting scheduled token refresh');
      try {
        await this.requestNewToken();
        logger.info('Scheduled token refresh completed successfully');
      } catch (error) {
        logger.error('Scheduled token refresh failed', error);
      }
    }, {
      timezone: 'UTC'
    });

    this.cronJob.start();

    logger.info('Viva token refresh cron job started (every 40 minutes)');
  }

  /**
   * Stop the cron job
   */
  stopTokenRefreshCron(): void {
    if (this.cronJob) {
      this.cronJob.stop();
      this.cronJob = null;
      logger.info('Viva token refresh cron job stopped');
    }
  }

  /**
   * Get token file status for debugging
   */
  async getTokenStatus(): Promise<{
    file_exists: boolean;
    environments: string[];
    tokens: Array<{
      environment: string;
      expires_at: string;
      is_valid: boolean;
      scope: string;
    }>;
  }> {
    try {
      const tokenFile = await this.loadTokensFromFile();
      const now = Date.now();
      const bufferTime = 5 * 60 * 1000; // 5 minutes

      const tokens = Object.entries(tokenFile.tokens).map(([env, token]) => ({
        environment: env,
        expires_at: new Date(token.expires_at).toISOString(),
        is_valid: token.expires_at > now + bufferTime,
        scope: token.scope
      }));

      return {
        file_exists: true,
        environments: Object.keys(tokenFile.tokens),
        tokens
      };
    } catch (error) {
      return {
        file_exists: false,
        environments: [],
        tokens: []
      };
    }
  }
}

// Export singleton instance
export const vivaTokenManager = new VivaTokenManager();
