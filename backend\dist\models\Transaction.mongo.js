"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const TransactionSchema = new mongoose_1.Schema({
    amount: {
        type: Number,
        required: true,
        min: [0.01, 'Amount must be greater than 0']
    },
    status: {
        type: String,
        enum: {
            values: ['success', 'failure', 'pending', 'failed', 'refunded', 'cancelled'],
            message: 'Status must be success, failure, pending, failed, refunded, or cancelled'
        },
        required: true,
        default: 'pending'
    },
    currency: {
        type: String,
        default: 'usd',
        match: [/^[a-z]{3}$/, 'Currency must be a 3-letter lowercase code']
    },
    paymentMethod: {
        type: String,
        enum: ['card', 'cash', 'other', 'move_payment', 'skrill_payment', 'nuvei_payment', 'xmoney_payment', 'payoneer_payment', 'square_payment', 'vivid_payment', 'viva_wallet'],
        default: 'card'
    },
    paymentProvider: {
        type: String,
        enum: ['stripe', 'novalnet', 'pax', 'move', 'skrill', 'nuvei', 'xmoney', 'payoneer', 'square', 'vivid', 'viva'],
        default: 'stripe'
    },
    novalnetTid: {
        type: String,
        match: [/^[0-9]{17}$/, 'Novalnet TID must be 17 digits'],
        sparse: true
    },
    novalnetToken: {
        type: String,
        sparse: true
    },
    threeDSecureStatus: {
        type: String,
        enum: ['authenticated', 'not_authenticated', 'attempted', 'unavailable'],
        sparse: true
    },
    protocolCode: {
        type: String,
        match: [/^101\.[1358]$/, 'Protocol code must be 101.1, 101.3, 101.5, or 101.8']
    },
    stripePaymentIntentId: {
        type: String,
        match: [/^pi_[a-zA-Z0-9_]+$/, 'Invalid Stripe payment intent ID format']
    },
    receiptUrl: {
        type: String,
        validate: {
            validator: function (v) {
                return !v || /^https?:\/\/.+/.test(v);
            },
            message: 'Receipt URL must be a valid HTTP/HTTPS URL'
        }
    },
    metadata: {
        type: mongoose_1.Schema.Types.Mixed,
        default: {}
    },
    createdAt: {
        type: String,
        required: true,
        default: () => new Date().toISOString()
    },
    updatedAt: {
        type: String,
        required: true,
        default: () => new Date().toISOString()
    },
}, {
    timestamps: false,
    versionKey: false,
});
TransactionSchema.index({ createdAt: -1 });
TransactionSchema.index({ status: 1 });
TransactionSchema.index({ stripePaymentIntentId: 1 }, { sparse: true });
TransactionSchema.index({ novalnetTid: 1 }, { sparse: true });
TransactionSchema.index({ paymentProvider: 1 });
TransactionSchema.index({ 'metadata.order_no': 1 }, { sparse: true });
TransactionSchema.index({ 'metadata.order_id': 1 }, { sparse: true });
TransactionSchema.index({ 'metadata.novalnet_tid': 1 }, { sparse: true });
TransactionSchema.index({ 'metadata.payment_provider': 1 }, { sparse: true });
TransactionSchema.index({ 'metadata.transaction_id': 1 }, { sparse: true });
TransactionSchema.index({ 'metadata.external_id': 1 }, { sparse: true });
TransactionSchema.pre('save', function (next) {
    this.updatedAt = new Date().toISOString();
    next();
});
exports.default = mongoose_1.default.model('Transaction', TransactionSchema);
//# sourceMappingURL=Transaction.mongo.js.map