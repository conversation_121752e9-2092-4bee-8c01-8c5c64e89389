/**
 * Vivid Money Payment Service
 *
 * Service for handling Vivid Money Payment Gateway integration
 * Supports payment creation, QR code generation, and payment status tracking
 */
export interface VividPaymentRequest {
    orderId: string;
    amount: number;
    currency?: string;
    successUrl: string;
    cancelUrl: string;
    payerName: string;
    payerEmail?: string;
    languageCode?: string;
}
export interface VividPaymentResponse {
    id: string;
    status: string;
    amount: number;
    currency: string;
    payment_url: string;
    qr_code?: string;
}
export interface VividServiceResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
    details?: any;
}
/**
 * Vivid Money Payment Service Class
 */
declare class VividPaymentService {
    private client;
    private apiKey;
    private merchantId;
    private baseUrl;
    constructor();
    /**
     * Create a new Vivid Money payment
     */
    createPayment(params: VividPaymentRequest): Promise<VividServiceResponse<VividPaymentResponse>>;
    /**
     * Get payment status
     */
    getPaymentStatus(orderId: string): Promise<VividServiceResponse<any>>;
    /**
     * Health check for Vivid Money service
     */
    healthCheck(): Promise<VividServiceResponse<{
        status: string;
    }>>;
    /**
     * Generate QR code data for payment
     */
    generateQRCodeData(paymentUrl: string): string;
    /**
     * Get supported payment methods
     */
    getSupportedPaymentMethods(): string[];
    /**
     * Get supported currencies
     */
    getSupportedCurrencies(): string[];
    /**
     * Check if Vivid Money Payment is enabled
     */
    isEnabled(): boolean;
}
export declare const vividPaymentService: VividPaymentService;
export {};
//# sourceMappingURL=vividPaymentService.d.ts.map