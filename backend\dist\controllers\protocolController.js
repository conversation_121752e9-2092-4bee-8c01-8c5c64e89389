"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.triggerProtocol = void 0;
const protocolService_1 = require("../services/protocolService");
const zod_1 = require("zod");
const logger_1 = require("../config/logger");
const controllerLogger = logger_1.logger.child({ module: 'protocol-controller' });
const triggerProtocolSchema = zod_1.z.object({
    protocolEventCode: zod_1.z.enum(['101.1', '101.3', '101.5', '101.8']),
    transactionId: zod_1.z.string(),
    stripePaymentIntentId: zod_1.z.string().optional(),
    amount: zod_1.z.number().positive(),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
});
const triggerProtocol = async (request, reply) => {
    try {
        const validatedBody = triggerProtocolSchema.parse(request.body);
        controllerLogger.info({
            protocolCode: validatedBody.protocolEventCode,
            transactionId: validatedBody.transactionId
        }, 'Triggering protocol message');
        const response = await protocolService_1.protocolService.sendProtocolMessage(validatedBody);
        return reply.send({
            success: true,
            data: {
                protocolCode: validatedBody.protocolEventCode,
                description: protocolService_1.protocolService.getProtocolDescription(validatedBody.protocolEventCode),
                bankResponse: response,
                transactionId: validatedBody.transactionId
            }
        });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            controllerLogger.warn({ error: error.errors }, 'Invalid protocol trigger request');
            return reply.status(400).send({
                success: false,
                error: 'VALIDATION_ERROR',
                message: 'Invalid request parameters',
                details: error.errors
            });
        }
        controllerLogger.error({ error }, 'Failed to trigger protocol');
        return reply.status(500).send({
            success: false,
            error: 'PROTOCOL_ERROR',
            message: 'Failed to trigger protocol message'
        });
    }
};
exports.triggerProtocol = triggerProtocol;
//# sourceMappingURL=protocolController.js.map