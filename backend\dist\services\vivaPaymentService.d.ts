/**
 * Viva Wallet Payment Service
 *
 * Service for handling Viva Wallet payment integration
 * Supports OAuth2 authentication, payment order creation, and payment status tracking
 */
export interface VivaPaymentRequest {
    orderId: string;
    amount: number;
    currency?: string;
    customerTrns?: string;
    payerName: string;
    payerEmail?: string;
    successUrl: string;
    cancelUrl: string;
    sourceCode?: string;
}
export interface VivaPaymentResponse {
    orderCode: number;
    checkout_url: string;
    payment_url: string;
    amount: number;
    currency: string;
    status: 'pending' | 'completed' | 'failed' | 'expired' | 'cancelled';
}
export interface VivaPaymentStatusResponse {
    orderCode: number;
    status: 'pending' | 'completed' | 'failed' | 'expired' | 'cancelled';
    amount: number;
    currency: string;
    stateId: number;
    expirationDate: string;
    merchantTrns?: string;
    customerTrns?: string;
}
export interface VivaServiceResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
    details?: any;
}
export interface VivaOAuthToken {
    access_token: string;
    token_type: string;
    expires_in: number;
    expires_at: number;
    scope?: string;
}
declare class VivaPaymentService {
    private client;
    private accountsClient;
    private merchantId;
    private apiKey;
    private clientId;
    private clientSecret;
    private sourceCode;
    private environment;
    private apiUrl;
    private accountsUrl;
    private checkoutUrl;
    private accessToken;
    constructor();
    /**
     * Get OAuth2 access token for API authentication using client credentials flow
     */
    private getAccessToken;
    /**
     * Get Basic Auth credentials for API authentication
     */
    private getBasicAuthHeader;
    /**
     * Create a new Viva Wallet payment order
     *
     * Note: Viva Wallet requires OAuth2 for payment creation.
     * Basic Auth only works for payment status, cancellation, and refunds.
     */
    createPayment(params: VivaPaymentRequest): Promise<VivaServiceResponse<VivaPaymentResponse>>;
    /**
     * Create payment using OAuth2 (following working JavaScript implementation)
     */
    private createPaymentWithOAuth2;
    /**
     * Get payment order details and status
     */
    getPaymentStatus(orderCode: number): Promise<VivaServiceResponse<VivaPaymentStatusResponse>>;
    /**
     * Cancel a payment order
     */
    cancelPayment(orderCode: number): Promise<VivaServiceResponse<{
        orderCode: number;
        cancelled: boolean;
    }>>;
    /**
     * Health check for Viva Wallet service
     */
    healthCheck(): Promise<VivaServiceResponse<{
        status: string;
        environment: string;
    }>>;
    /**
     * Map Viva Wallet StateId to our standard status
     */
    private mapStateIdToStatus;
    /**
     * Get numeric currency code for Viva Wallet API
     */
    private getCurrencyCode;
    /**
     * Generate order ID for payments
     */
    generateOrderId(): string;
    /**
     * Format amount for display (convert from cents to currency units)
     */
    formatAmount(amount: number, currency?: string): string;
    /**
     * Check if payment is expired
     */
    isPaymentExpired(expirationDate: string): boolean;
    /**
     * Get time remaining until payment expires
     */
    getTimeRemaining(expirationDate: string): number;
    /**
     * Format time remaining for display
     */
    formatTimeRemaining(expirationDate: string): string;
    /**
     * Get success URL for payment completion
     */
    getSuccessUrl(): string;
    /**
     * Get cancel URL for payment cancellation
     */
    getCancelUrl(): string;
}
export declare const vivaPaymentService: VivaPaymentService;
export {};
//# sourceMappingURL=vivaPaymentService.d.ts.map