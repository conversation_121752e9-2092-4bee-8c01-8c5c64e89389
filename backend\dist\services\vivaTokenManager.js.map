{"version": 3, "file": "vivaTokenManager.js", "sourceRoot": "", "sources": ["../../src/services/vivaTokenManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2DAA6B;AAC7B,gDAAwB;AACxB,gDAAkC;AAClC,uCAAoC;AACpC,6CAA0C;AAoB1C,MAAa,gBAAgB;IAK3B;QAHQ,YAAO,GAA8B,IAAI,CAAC;QAC1C,iBAAY,GAAG,KAAK,CAAC;QAG3B,kDAAkD;QAClD,MAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;QACxD,IAAI,CAAC,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;QAC9D,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB;QACjC,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACnD,MAAM,kBAAE,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAC/C,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;QACpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;YACnE,MAAM,SAAS,GAAkB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YACzD,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,2DAA2D;YAC3D,eAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YAC9D,OAAO;gBACL,MAAM,EAAE,EAAE;gBACV,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;aACzB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,SAAwB;QACrD,IAAI,CAAC;YACH,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACpC,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YACvD,MAAM,kBAAE,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;YAC7D,eAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACvC,IAAI,EAAE,IAAI,CAAC,aAAa;gBACxB,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;aAC5C,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAClD,MAAM,WAAW,GAAG,SAAG,CAAC,gBAAgB,CAAC;YACzC,MAAM,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAE5C,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,eAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;gBACtE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,uDAAuD;YACvD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvB,MAAM,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY;YAE9C,IAAI,KAAK,CAAC,UAAU,IAAI,GAAG,GAAG,UAAU,EAAE,CAAC;gBACzC,eAAM,CAAC,IAAI,CAAC,0CAA0C,EAAE;oBACtD,WAAW;oBACX,UAAU,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;oBACtC,GAAG,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC;iBACnB,CAAC,CAAC;gBACH,OAAO,IAAI,CAAC;YACd,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE;gBACtC,WAAW;gBACX,UAAU,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;gBACtC,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,SAKhB;QACC,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAClD,MAAM,WAAW,GAAG,SAAG,CAAC,gBAAgB,CAAC;YACzC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAEvB,MAAM,SAAS,GAAkB;gBAC/B,YAAY,EAAE,SAAS,CAAC,YAAY;gBACpC,UAAU,EAAE,SAAS,CAAC,UAAU;gBAChC,UAAU,EAAE,SAAS,CAAC,UAAU;gBAChC,UAAU,EAAE,GAAG,GAAG,CAAC,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,KAAK,EAAE,+BAA+B;gBACxF,KAAK,EAAE,SAAS,CAAC,KAAK;gBACtB,UAAU,EAAE,GAAG;gBACf,WAAW;gBACX,SAAS,EAAE,SAAG,CAAC,cAAc;aAC9B,CAAC;YAEF,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC;YAC1C,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAEvC,eAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBACnC,WAAW;gBACX,UAAU,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;gBAC1C,KAAK,EAAE,SAAS,CAAC,KAAK;aACvB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QACnB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,eAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE;gBAC9C,WAAW,EAAE,SAAG,CAAC,gBAAgB;gBACjC,SAAS,EAAE,SAAG,CAAC,cAAc;aAC9B,CAAC,CAAC;YAEH,sCAAsC;YACtC,MAAM,WAAW,GAAG,SAAG,CAAC,iBAAiB,IAAI,CAAC,SAAG,CAAC,gBAAgB,KAAK,YAAY;gBACjF,CAAC,CAAC,mCAAmC;gBACrC,CAAC,CAAC,wCAAwC,CAAC,CAAC;YAE9C,MAAM,WAAW,GAAG,GAAG,SAAG,CAAC,cAAc,IAAI,SAAG,CAAC,kBAAkB,EAAE,CAAC;YACtE,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEtE,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,WAAW,gBAAgB,EAAE;gBAC3D,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,mCAAmC;oBACnD,eAAe,EAAE,SAAS,iBAAiB,EAAE;oBAC7C,QAAQ,EAAE,kBAAkB;iBAC7B;gBACD,IAAI,EAAE,+BAA+B;aACtC,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACxC,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;oBACzC,MAAM,EAAE,QAAQ,CAAC,MAAM;oBACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,KAAK,EAAE,SAAS;iBACjB,CAAC,CAAC;gBACH,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,SAAS,GAAQ,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAE7C,gCAAgC;YAChC,IAAI,CAAC,SAAS,CAAC,YAAY,IAAI,CAAC,SAAS,CAAC,UAAU,IAAI,CAAC,SAAS,CAAC,UAAU,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;gBAClG,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;gBAChE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,sBAAsB;YACtB,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAEjC,+BAA+B;YAC/B,OAAO,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAErC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC;YACH,gCAAgC;YAChC,IAAI,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAExC,8CAA8C;YAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,eAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;gBACzD,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YACvC,CAAC;YAED,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,eAAM,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;gBAC1C,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,KAAK,CAAC,YAAY,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACjD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,uCAAuC;QACvC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;YACxD,eAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAChD,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC7B,eAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YAChE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,EAAE;YACD,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QAErB,eAAM,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,eAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAUlB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAClD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvB,MAAM,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY;YAE9C,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;gBACrE,WAAW,EAAE,GAAG;gBAChB,UAAU,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE;gBACpD,QAAQ,EAAE,KAAK,CAAC,UAAU,GAAG,GAAG,GAAG,UAAU;gBAC7C,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC,CAAC;YAEJ,OAAO;gBACL,WAAW,EAAE,IAAI;gBACjB,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC3C,MAAM;aACP,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,WAAW,EAAE,KAAK;gBAClB,YAAY,EAAE,EAAE;gBAChB,MAAM,EAAE,EAAE;aACX,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAjTD,4CAiTC;AAED,4BAA4B;AACf,QAAA,gBAAgB,GAAG,IAAI,gBAAgB,EAAE,CAAC"}