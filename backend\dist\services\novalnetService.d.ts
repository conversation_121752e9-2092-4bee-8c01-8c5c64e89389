/**
 * Novalnet Payment Service
 *
 * Handles Novalnet API communication for payment processing
 * Supports inline payment form integration, 3D Secure, and tokenization
 */
export interface NovalnetPaymentRequest {
    amount: number;
    currency?: string;
    order_no: string;
    return_url: string;
    error_return_url: string;
    hook_url?: string;
    customer: {
        first_name: string;
        last_name: string;
        email: string;
        customer_ip: string;
        customer_no?: string;
        billing: {
            street: string;
            house_no: string;
            city: string;
            zip: string;
            country_code: string;
        };
    };
    test_mode?: boolean;
}
export interface NovalnetTokenPaymentRequest {
    token: string;
    amount: number;
    currency?: string;
    order_no: string;
    customer: {
        first_name: string;
        last_name: string;
        email: string;
        customer_ip: string;
    };
    test_mode?: boolean;
}
export interface NovalnetPaymentResponse {
    result: {
        status: string;
        status_code: number;
        status_text: string;
    };
    transaction?: {
        tid: string;
        test_mode: number;
        amount: number;
        currency: string;
        payment_type: string;
        status: string;
        status_code: number;
    };
    hosted_page?: {
        payment_url: string;
        token: string;
    };
    instalment?: any;
    merchant?: any;
    customer?: any;
    billing?: any;
    shipping?: any;
}
export interface NovalnetWebhookEvent {
    event: {
        type: string;
        checksum: string;
        tid: string;
        parent_tid?: string;
    };
    result: {
        status: string;
        status_code: number;
        status_text: string;
    };
    transaction: {
        tid: string;
        test_mode: number;
        amount: number;
        currency: string;
        payment_type: string;
        status: string;
        status_code: number;
        order_no: string;
    };
    custom?: any;
}
declare class NovalnetService {
    private client;
    private accessKey;
    private activationKey;
    private tariffId;
    constructor();
    /**
     * Create a hosted payment page for overlay integration (Step 1)
     */
    createPayment(params: NovalnetPaymentRequest): Promise<NovalnetPaymentResponse>;
    /**
     * Verify overlay response (Step 3)
     */
    verifyOverlayResponse(params: {
        tid?: string;
        txn_secret: string;
        status: string;
        checksum?: string;
    }): Promise<{
        isValid: boolean;
        message: string;
    }>;
    /**
     * Process payment with saved token (for repeat customers)
     */
    processTokenPayment(params: NovalnetTokenPaymentRequest): Promise<NovalnetPaymentResponse>;
    /**
     * Get transaction details
     */
    getTransactionDetails(tid: string): Promise<NovalnetPaymentResponse>;
    /**
     * Verify webhook signature
     */
    verifyWebhookSignature(payload: string, receivedSignature: string): boolean;
}
export declare const novalnetService: NovalnetService;
export {};
//# sourceMappingURL=novalnetService.d.ts.map