"use strict";
/**
 * Enhanced Payment Routes
 *
 * Handles Stripe Terminal payment processing with webhook integration
 * Provides endpoints for payment creation, status checking, and management
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = enhancedPaymentRoutes;
const enhancedPaymentService_1 = require("../services/enhancedPaymentService");
async function enhancedPaymentRoutes(fastify) {
    /**
     * Create payment with PAX terminal data
     */
    fastify.post('/payments/terminal', async (request, reply) => {
        try {
            const { amount, currency = 'usd', description, metadata, terminalId, merchantName, cardData } = request.body;
            // Validate required fields
            if (!amount || !terminalId || !merchantName) {
                return reply.status(400).send({
                    success: false,
                    error: 'Missing required fields: amount, terminalId, merchantName'
                });
            }
            if (amount <= 0) {
                return reply.status(400).send({
                    success: false,
                    error: 'Amount must be greater than 0'
                });
            }
            if (!cardData?.encryptedPan) {
                return reply.status(400).send({
                    success: false,
                    error: 'Encrypted card data is required'
                });
            }
            // Create payment request
            const paymentRequest = {
                amount,
                currency,
                description: description || `Stripe Terminal Payment - ${terminalId}`,
                metadata: {
                    source: 'stripe_terminal',
                    terminal_id: terminalId,
                    merchant_name: merchantName,
                    ...metadata
                },
                terminalId,
                merchantName,
                cardData
            };
            // Process payment
            const result = await enhancedPaymentService_1.enhancedPaymentService.processTerminalPayment(paymentRequest);
            if (!result.success) {
                return reply.status(400).send({
                    success: false,
                    error: result.error || 'Payment processing failed'
                });
            }
            fastify.log.info('Terminal payment processed successfully', {
                paymentIntentId: result.paymentIntent?.id,
                terminalId,
                amount
            });
            reply.send({
                success: true,
                data: {
                    paymentIntentId: result.paymentIntent?.id,
                    paymentMethodId: result.paymentMethod?.id,
                    status: result.paymentIntent?.status,
                    amount,
                    currency,
                    transactionId: result.transactionId,
                    receiptData: result.receiptData,
                    clientSecret: result.paymentIntent?.client_secret
                }
            });
        }
        catch (error) {
            fastify.log.error('Terminal payment processing failed', error);
            reply.status(500).send({
                success: false,
                error: error instanceof Error ? error.message : 'Internal server error'
            });
        }
    });
    /**
     * Get payment status (Enhanced Payment API)
     */
    fastify.get('/payments/terminal/:paymentIntentId/status', async (request, reply) => {
        try {
            const { paymentIntentId } = request.params;
            if (!paymentIntentId) {
                return reply.status(400).send({
                    success: false,
                    error: 'Payment Intent ID is required'
                });
            }
            const status = await enhancedPaymentService_1.enhancedPaymentService.getPaymentStatus(paymentIntentId);
            reply.send({
                success: true,
                data: {
                    paymentIntentId,
                    ...status
                }
            });
        }
        catch (error) {
            fastify.log.error('Failed to get payment status', error);
            reply.status(500).send({
                success: false,
                error: error instanceof Error ? error.message : 'Failed to get payment status'
            });
        }
    });
    /**
     * Cancel payment (Enhanced Payment API)
     */
    fastify.post('/payments/terminal/:paymentIntentId/cancel', async (request, reply) => {
        try {
            const { paymentIntentId } = request.params;
            const { reason } = request.body;
            if (!paymentIntentId) {
                return reply.status(400).send({
                    success: false,
                    error: 'Payment Intent ID is required'
                });
            }
            const canceled = await enhancedPaymentService_1.enhancedPaymentService.cancelPayment(paymentIntentId);
            if (!canceled) {
                return reply.status(400).send({
                    success: false,
                    error: 'Failed to cancel payment'
                });
            }
            fastify.log.info('Payment canceled', {
                paymentIntentId,
                reason: reason || 'No reason provided'
            });
            reply.send({
                success: true,
                data: {
                    paymentIntentId,
                    status: 'canceled',
                    reason: reason || 'Canceled by request'
                }
            });
        }
        catch (error) {
            fastify.log.error('Failed to cancel payment', error);
            reply.status(500).send({
                success: false,
                error: error instanceof Error ? error.message : 'Failed to cancel payment'
            });
        }
    });
    /**
     * Process manual payment (fallback)
     */
    fastify.post('/payments/manual', async (request, reply) => {
        try {
            const { amount, currency = 'usd', cardNumber, expiryMonth, expiryYear, cvc, cardholderName, terminalId, merchantName, description, metadata } = request.body;
            // Validate required fields
            if (!amount || !cardNumber || !expiryMonth || !expiryYear || !cvc || !terminalId || !merchantName) {
                return reply.status(400).send({
                    success: false,
                    error: 'Missing required fields for manual payment'
                });
            }
            // Create manual payment request (simulate encrypted data)
            const paymentRequest = {
                amount,
                currency,
                description: description || `Manual Payment - ${terminalId}`,
                metadata: {
                    source: 'manual_entry',
                    terminal_id: terminalId,
                    merchant_name: merchantName,
                    ...metadata
                },
                terminalId,
                merchantName,
                cardData: {
                    encryptedPan: Buffer.from(cardNumber).toString('base64'), // Simple encoding for demo
                    maskedPan: `****${cardNumber.slice(-4)}`,
                    expiryDate: `${expiryMonth.toString().padStart(2, '0')}${expiryYear.toString().slice(-2)}`,
                    cardholderName: cardholderName || 'Manual Entry',
                    cardType: 'manual',
                    cardBrand: 'unknown'
                }
            };
            const result = await enhancedPaymentService_1.enhancedPaymentService.processTerminalPayment(paymentRequest);
            if (!result.success) {
                return reply.status(400).send({
                    success: false,
                    error: result.error || 'Manual payment processing failed'
                });
            }
            fastify.log.info('Manual payment processed successfully', {
                paymentIntentId: result.paymentIntent?.id,
                terminalId,
                amount
            });
            reply.send({
                success: true,
                data: {
                    paymentIntentId: result.paymentIntent?.id,
                    paymentMethodId: result.paymentMethod?.id,
                    status: result.paymentIntent?.status,
                    amount,
                    currency,
                    transactionId: result.transactionId,
                    receiptData: result.receiptData
                }
            });
        }
        catch (error) {
            fastify.log.error('Manual payment processing failed', error);
            reply.status(500).send({
                success: false,
                error: error instanceof Error ? error.message : 'Internal server error'
            });
        }
    });
    /**
     * Health check for enhanced payment service
     */
    fastify.get('/payments/terminal/health', async (_request, reply) => {
        reply.send({
            success: true,
            message: 'Enhanced payment service is healthy',
            timestamp: new Date().toISOString(),
            services: {
                stripe: 'connected',
                webhook: 'configured',
                encryption: 'available'
            }
        });
    });
}
//# sourceMappingURL=enhanced-payment.routes.js.map