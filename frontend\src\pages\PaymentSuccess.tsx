import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Check<PERSON>ircle, XCircle, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

export const PaymentSuccess: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [isVerifying, setIsVerifying] = useState(true);
  const [verificationResult, setVerificationResult] = useState<{
    isValid: boolean;
    message: string;
  } | null>(null);

  // Extract parameters from URL
  // Novalnet parameters
  const tid = searchParams.get('tid');
  const txnSecret = searchParams.get('txn_secret');
  const status = searchParams.get('status');
  const statusText = searchParams.get('status_text');
  const statusCode = searchParams.get('status_code');
  const checksum = searchParams.get('checksum');

  // Viva Wallet parameters (if any)
  const orderCode = searchParams.get('orderCode');
  const vivaStatus = searchParams.get('status');

  // Determine payment provider
  const isNovalnetPayment = tid && txnSecret;
  const isVivaPayment = orderCode || (!tid && !txnSecret);

  useEffect(() => {
    const verifyPayment = async () => {
      // Handle Novalnet payments
      if (isNovalnetPayment) {
        // Handle cases where payment was abandoned or failed
        if (status === 'FAILURE' && statusCode === '94') {
          setVerificationResult({
            isValid: false,
            message: 'Customer has abandoned the transaction'
          });
          setIsVerifying(false);
          toast.error('Payment was cancelled by customer');
          return;
        }

        if (!txnSecret || !status) {
          setVerificationResult({
            isValid: false,
            message: 'Missing required payment parameters'
          });
          setIsVerifying(false);
          return;
        }
      }

      // Handle Viva Wallet payments
      if (isVivaPayment) {
        // For Viva payments, we just show success since they redirected to success URL
        setVerificationResult({
          isValid: true,
          message: 'Payment completed successfully'
        });
        setIsVerifying(false);
        toast.success('Payment completed successfully!');
        return;
      }

      // Only verify Novalnet payments
      if (isNovalnetPayment) {
        try {
          const response = await fetch('/api/v1/novalnet/verify', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              tid,
              txn_secret: txnSecret,
              status,
              checksum,
            }),
          });

          const result = await response.json();

          if (result.success) {
            setVerificationResult(result.data);
            if (result.data.isValid && status === 'SUCCESS') {
              toast.success('Payment completed successfully!');
            } else if (status === 'FAILURE') {
              toast.error(`Payment failed: ${statusText || 'Unknown error'}`);
            }
          } else {
            throw new Error(result.message || 'Verification failed');
          }
        } catch (error) {
          console.error('Payment verification error:', error);
          setVerificationResult({
            isValid: false,
            message: 'Failed to verify payment'
          });
          toast.error('Failed to verify payment');
        } finally {
          setIsVerifying(false);
        }
      }
    };

    verifyPayment();
  }, [tid, txnSecret, status, statusText, checksum, orderCode, isNovalnetPayment, isVivaPayment]);

  const handleReturnToPOS = () => {
    navigate('/');
  };

  const isSuccess = isVivaPayment
    ? verificationResult?.isValid
    : (status === 'SUCCESS' && verificationResult?.isValid);
  const isFailure = isVivaPayment
    ? (verificationResult && !verificationResult.isValid)
    : (status === 'FAILURE' || (verificationResult && !verificationResult.isValid));

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-center">
            {isVerifying ? (
              <>
                <Loader2 className="h-6 w-6 animate-spin" />
                Verifying {isVivaPayment ? 'Viva Wallet' : 'Novalnet'} Payment...
              </>
            ) : isSuccess ? (
              <>
                <CheckCircle className="h-6 w-6 text-green-600" />
                {isVivaPayment ? 'Viva Wallet' : 'Novalnet'} Payment Successful
              </>
            ) : (
              <>
                <XCircle className="h-6 w-6 text-red-600" />
                {isVivaPayment ? 'Viva Wallet' : 'Novalnet'} Payment {status === 'FAILURE' ? 'Failed' : 'Error'}
              </>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {isVerifying ? (
            <div className="text-center py-8">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Please wait while we verify your payment...</p>
            </div>
          ) : (
            <>
              {isSuccess && (
                <Alert className="border-green-200 bg-green-50">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-green-800">
                    Your payment has been processed successfully and verified.
                  </AlertDescription>
                </Alert>
              )}

              {isFailure && (
                <Alert className="border-red-200 bg-red-50">
                  <XCircle className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-800">
                    {status === 'FAILURE' 
                      ? `Payment failed: ${statusText || 'Unknown error'}`
                      : verificationResult?.message || 'Payment verification failed'
                    }
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-2 text-sm text-gray-600">
                {tid && (
                  <div className="flex justify-between">
                    <span>Transaction ID:</span>
                    <span className="font-mono">{tid}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span>Status:</span>
                  <span className={`font-semibold ${
                    isSuccess ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {status}
                  </span>
                </div>
                {statusText && (
                  <div className="flex justify-between">
                    <span>Details:</span>
                    <span>{statusText}</span>
                  </div>
                )}
                {verificationResult && (
                  <div className="flex justify-between">
                    <span>Verification:</span>
                    <span className={`font-semibold ${
                      verificationResult.isValid ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {verificationResult.isValid ? 'Valid' : 'Invalid'}
                    </span>
                  </div>
                )}
              </div>

              <Button 
                onClick={handleReturnToPOS} 
                className="w-full"
                variant={isSuccess ? "default" : "outline"}
              >
                Return to POS
              </Button>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
