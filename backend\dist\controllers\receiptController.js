"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.receiptController = void 0;
const receiptService_1 = require("../services/receiptService");
const Transaction_mongo_1 = __importDefault(require("../models/Transaction.mongo"));
const logger_1 = require("../config/logger");
exports.receiptController = {
    // Generate receipt for a transaction
    async generateReceipt(request, reply) {
        try {
            const { transactionId } = request.params;
            const transaction = await Transaction_mongo_1.default.findById(transactionId);
            if (!transaction) {
                return reply.status(404).send({
                    success: false,
                    error: 'Transaction not found'
                });
            }
            const customerReceipt = receiptService_1.ReceiptService.generateCustomerReceipt(transaction);
            const merchantReceipt = receiptService_1.ReceiptService.generateMerchantReceipt(transaction);
            logger_1.logger.info(`Receipt generated for transaction ${transactionId}`);
            return reply.send({
                success: true,
                data: {
                    transactionId,
                    customerReceipt,
                    merchantReceipt,
                    timestamp: new Date().toISOString()
                }
            });
        }
        catch (error) {
            logger_1.logger.error('Error generating receipt:', error);
            return reply.status(500).send({
                success: false,
                error: 'Failed to generate receipt'
            });
        }
    },
    // Print receipt (simulate printer commands)
    async printReceipt(request, reply) {
        try {
            const { transactionId, copies = 1, customerCopy = true } = request.body;
            const transaction = await Transaction_mongo_1.default.findById(transactionId);
            if (!transaction) {
                return reply.status(404).send({
                    success: false,
                    error: 'Transaction not found'
                });
            }
            const receipt = customerCopy
                ? receiptService_1.ReceiptService.generateCustomerReceipt(transaction)
                : receiptService_1.ReceiptService.generateMerchantReceipt(transaction);
            // Simulate printing process
            const printJobs = [];
            for (let i = 0; i < copies; i++) {
                printJobs.push({
                    jobId: `print_${Date.now()}_${i}`,
                    receipt,
                    status: 'queued',
                    timestamp: new Date().toISOString()
                });
            }
            logger_1.logger.info(`Print job created for transaction ${transactionId}, copies: ${copies}`);
            return reply.send({
                success: true,
                data: {
                    transactionId,
                    printJobs,
                    message: `${copies} receipt(s) sent to printer`
                }
            });
        }
        catch (error) {
            logger_1.logger.error('Error printing receipt:', error);
            return reply.status(500).send({
                success: false,
                error: 'Failed to print receipt'
            });
        }
    },
    // Get receipt in different formats
    async getReceiptFormats(request, reply) {
        try {
            const { transactionId } = request.params;
            const transaction = await Transaction_mongo_1.default.findById(transactionId);
            if (!transaction) {
                return reply.status(404).send({
                    success: false,
                    error: 'Transaction not found'
                });
            }
            const customerReceipt = receiptService_1.ReceiptService.generateCustomerReceipt(transaction);
            const merchantReceipt = receiptService_1.ReceiptService.generateMerchantReceipt(transaction);
            return reply.send({
                success: true,
                data: {
                    transactionId,
                    formats: {
                        text: {
                            customer: customerReceipt,
                            merchant: merchantReceipt
                        },
                        html: {
                            customer: this.convertToHTML(customerReceipt),
                            merchant: this.convertToHTML(merchantReceipt)
                        },
                        json: {
                            transactionId: transaction._id,
                            amount: transaction.amount,
                            status: transaction.status,
                            timestamp: transaction.createdAt,
                            protocolCode: transaction.protocolCode
                        }
                    }
                }
            });
        }
        catch (error) {
            logger_1.logger.error('Error getting receipt formats:', error);
            return reply.status(500).send({
                success: false,
                error: 'Failed to get receipt formats'
            });
        }
    },
    // Email receipt (placeholder for future implementation)
    async emailReceipt(request, reply) {
        try {
            const { transactionId, email } = request.body;
            const transaction = await Transaction_mongo_1.default.findById(transactionId);
            if (!transaction) {
                return reply.status(404).send({
                    success: false,
                    error: 'Transaction not found'
                });
            }
            // Placeholder for email service integration
            logger_1.logger.info(`Email receipt requested for transaction ${transactionId} to ${email}`);
            return reply.send({
                success: true,
                data: {
                    transactionId,
                    email,
                    message: 'Receipt email queued for delivery',
                    timestamp: new Date().toISOString()
                }
            });
        }
        catch (error) {
            logger_1.logger.error('Error emailing receipt:', error);
            return reply.status(500).send({
                success: false,
                error: 'Failed to email receipt'
            });
        }
    },
    // Helper method to convert text receipt to HTML
    convertToHTML(textReceipt) {
        return `
      <div style="font-family: 'Courier New', monospace; white-space: pre-wrap; background: white; padding: 20px; max-width: 400px; margin: 0 auto; border: 1px solid #ccc;">
        ${textReceipt.replace(/\n/g, '<br>')}
      </div>
    `;
    }
};
//# sourceMappingURL=receiptController.js.map