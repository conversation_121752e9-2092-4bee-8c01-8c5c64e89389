/**
 * Test Viva Wallet Payment Creation with Basic Auth
 * 
 * Try alternative endpoints that might work with Basic Auth
 * Run with: node test-viva-basic-payment.js
 */

const https = require('https');

// Your Viva Wallet credentials
const VIVA_MERCHANT_ID = '30481af3-63d9-42cd-93ea-1937a972b76d';
const VIVA_API_KEY = 'SothunZ2FxVRMkq666sbxbxB6VNbJG';

console.log('🔐 Testing Viva Wallet Payment Creation with Basic Auth');
console.log('=' .repeat(60));
console.log('');

// Test different payment endpoints with Basic Auth
async function testBasicAuthPaymentEndpoints() {
  const credentials = Buffer.from(`${VIVA_MERCHANT_ID}:${VIVA_API_KEY}`).toString('base64');
  
  const endpoints = [
    {
      name: 'Smart Checkout v2 (Basic Auth)',
      hostname: 'demo-api.vivapayments.com',
      path: '/checkout/v2/orders',
      method: 'POST'
    },
    {
      name: 'Legacy Orders API',
      hostname: 'demo-api.vivapayments.com', 
      path: '/api/orders',
      method: 'POST'
    },
    {
      name: 'Alternative Demo API',
      hostname: 'demo.vivapayments.com',
      path: '/api/orders',
      method: 'POST'
    }
  ];

  const paymentData = {
    amount: 100, // £1.00 in pence
    customerTrns: 'Test payment with Basic Auth',
    sourceCode: 'Default',
    merchantTrns: 'BASIC_TEST_' + Date.now(),
    currencyCode: '826' // GBP
  };

  for (const endpoint of endpoints) {
    console.log(`🔵 Testing: ${endpoint.name}`);
    console.log(`📍 URL: https://${endpoint.hostname}${endpoint.path}`);
    console.log('');

    await new Promise((resolve) => {
      const postData = JSON.stringify(paymentData);

      const options = {
        hostname: endpoint.hostname,
        port: 443,
        path: endpoint.path,
        method: endpoint.method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`,
          'Accept': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        }
      };

      console.log('📤 Request:', JSON.stringify(options, null, 2));
      console.log('📤 Data:', JSON.stringify(paymentData, null, 2));
      console.log('');

      const req = https.request(options, (res) => {
        console.log(`📥 Response Status: ${res.statusCode}`);
        console.log('📥 Response Headers:', JSON.stringify(res.headers, null, 2));
        console.log('');

        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          console.log('📥 Response Body:', data || '(empty)');
          console.log('');

          if (res.statusCode === 200 || res.statusCode === 201) {
            console.log('🎉 SUCCESS! This endpoint works with Basic Auth');
            try {
              const response = JSON.parse(data);
              if (response.orderCode || response.OrderCode) {
                const orderCode = response.orderCode || response.OrderCode;
                console.log('✅ Order Code:', orderCode);
                console.log('✅ Payment URL: https://demo.vivapayments.com/web/checkout?ref=' + orderCode);
              }
            } catch (e) {
              console.log('✅ Response received but not JSON');
            }
          } else if (res.statusCode === 401) {
            console.log('❌ Authentication failed');
          } else if (res.statusCode === 403) {
            console.log('❌ OAuth2 required for this endpoint');
          } else if (res.statusCode === 404) {
            console.log('❌ Endpoint not found');
          } else {
            console.log('❌ Request failed');
            try {
              const response = JSON.parse(data);
              console.log('❌ Error:', response);
            } catch (e) {
              console.log('❌ Non-JSON error response');
            }
          }

          console.log('─'.repeat(60));
          console.log('');
          resolve();
        });
      });

      req.on('error', (error) => {
        console.log('❌ Network Error:', error.message);
        console.log('─'.repeat(60));
        console.log('');
        resolve();
      });

      req.write(postData);
      req.end();
    });
  }
}

// Test payment notification endpoint (might work with Basic Auth)
async function testPaymentNotificationEndpoint() {
  console.log('🔵 Testing: Payment Notification Endpoint');
  console.log('📍 This might be an alternative way to create payments');
  console.log('');

  const credentials = Buffer.from(`${VIVA_MERCHANT_ID}:${VIVA_API_KEY}`).toString('base64');

  const notificationData = {
    amount: 100,
    customerTrns: 'Test payment notification',
    sourceCode: 'Default',
    merchantTrns: 'NOTIFICATION_TEST_' + Date.now(),
    paymentNotification: true
  };

  await new Promise((resolve) => {
    const postData = JSON.stringify(notificationData);

    const options = {
      hostname: 'demo-api.vivapayments.com',
      port: 443,
      path: '/api/orders',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${credentials}`,
        'Accept': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log('📤 Testing payment notification approach...');
    console.log('');

    const req = https.request(options, (res) => {
      console.log(`📥 Response Status: ${res.statusCode}`);

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log('📥 Response:', data || '(empty)');
        console.log('');
        resolve();
      });
    });

    req.on('error', (error) => {
      console.log('❌ Network Error:', error.message);
      resolve();
    });

    req.write(postData);
    req.end();
  });
}

// Main execution
async function main() {
  console.log('🚀 Starting Basic Auth Payment Tests');
  console.log('');

  await testBasicAuthPaymentEndpoints();
  await testPaymentNotificationEndpoint();

  console.log('📋 SUMMARY:');
  console.log('');
  console.log('If none of these endpoints work with Basic Auth, then:');
  console.log('1. OAuth2 is definitely required for payment creation');
  console.log('2. We need to fix the OAuth2 credentials issue');
  console.log('3. Contact Viva support about the Smart Checkout setup');
  console.log('');
  console.log('💡 WORKAROUND OPTIONS:');
  console.log('1. Use Basic Auth for payment status/cancellation (already working)');
  console.log('2. Implement a fallback payment method');
  console.log('3. Contact Viva support to verify OAuth2 setup');
  console.log('');
}

main();
