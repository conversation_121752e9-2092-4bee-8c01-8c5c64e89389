import mongoose, { Document } from 'mongoose';
export interface ITransaction extends Document {
    amount: number;
    status: 'success' | 'failure' | 'pending' | 'failed' | 'refunded' | 'cancelled';
    protocolCode?: string;
    stripePaymentIntentId?: string;
    receiptUrl?: string;
    metadata?: Record<string, any>;
    currency?: string;
    paymentMethod?: string;
    paymentProvider?: 'stripe' | 'novalnet' | 'pax' | 'move' | 'skrill' | 'nuvei' | 'xmoney' | 'payoneer' | 'square' | 'vivid' | 'viva';
    novalnetTid?: string;
    novalnetToken?: string;
    threeDSecureStatus?: 'authenticated' | 'not_authenticated' | 'attempted' | 'unavailable';
    createdAt: string;
    updatedAt: string;
}
declare const _default: mongoose.Model<ITransaction, {}, {}, {}, mongoose.Document<unknown, {}, ITransaction, {}> & ITransaction & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=Transaction.mongo.d.ts.map