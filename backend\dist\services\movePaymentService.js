"use strict";
/**
 * Move Payment Service
 *
 * Service for handling Move Payment Gateway integration
 * Supports payment creation, QR code generation, and payment status tracking
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.movePaymentService = exports.MovePaymentService = void 0;
const axios_1 = __importDefault(require("axios"));
const zod_1 = require("zod");
const env_1 = require("../config/env");
const logger_1 = require("../config/logger");
const moveLogger = logger_1.logger.child({ module: 'move-payment-service' });
// Validation schemas
const createPaymentSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
    amount: zod_1.z.number().positive().int(),
    moveId: zod_1.z.string().email(),
    languageCode: zod_1.z.string().length(2).default('en'),
    successUrl: zod_1.z.string().url(),
    cancelUrl: zod_1.z.string().url(),
    payerName: zod_1.z.string().min(1),
});
const paymentResponseSchema = zod_1.z.object({
    expires_at: zod_1.z.string().nullable().optional(),
    redirect_url: zod_1.z.string().url(),
    token: zod_1.z.string(),
});
const paymentDetailsSchema = zod_1.z.object({
    orderId: zod_1.z.string(),
    amount: zod_1.z.number(),
    moveId: zod_1.z.string(),
    languageCode: zod_1.z.string(),
    data: zod_1.z.object({
        move_id: zod_1.z.string(),
        amount: zod_1.z.number(),
        type: zod_1.z.string(),
        avatar: zod_1.z.string().url().optional(),
        name: zod_1.z.string(),
        userExternalCode: zod_1.z.string().nullable(),
        paymentId: zod_1.z.string(),
        successUrl: zod_1.z.string().url(),
        cancelUrl: zod_1.z.string().url(),
    }),
});
class MovePaymentService {
    constructor() {
        this.apiUrl = env_1.env.MOVE_PAYMENT_API_URL;
        this.apiKey = env_1.env.MOVE_PAYMENT_API_KEY;
        this.defaultMoveId = env_1.env.MOVE_PAYMENT_MOVE_ID;
        // Create axios client
        this.client = axios_1.default.create({
            baseURL: this.apiUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'X-Api-Key': this.apiKey, // Move Payment uses X-Api-Key header
            },
        });
        // Add request interceptor for logging
        this.client.interceptors.request.use((config) => {
            moveLogger.info('Move Payment API request', {
                method: config.method?.toUpperCase(),
                url: config.url,
                baseURL: config.baseURL,
            });
            return config;
        }, (error) => {
            moveLogger.error('Move Payment API request error', error);
            return Promise.reject(error);
        });
        // Add response interceptor for logging
        this.client.interceptors.response.use((response) => {
            moveLogger.info('Move Payment API response received', {
                status: response.status,
                statusText: response.statusText,
            });
            return response;
        }, (error) => {
            moveLogger.error('Move Payment API error', {
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data,
            });
            return Promise.reject(error);
        });
    }
    /**
     * Create a new Move payment
     */
    async createPayment(params) {
        try {
            const validatedParams = createPaymentSchema.parse({
                ...params,
                moveId: params.moveId || this.defaultMoveId,
            });
            moveLogger.info('Creating Move payment', {
                orderId: validatedParams.orderId,
                amount: validatedParams.amount,
                payerName: validatedParams.payerName,
            });
            const requestData = {
                orderId: validatedParams.orderId,
                amount: validatedParams.amount,
                moveId: validatedParams.moveId,
                languageCode: validatedParams.languageCode,
                successUrl: validatedParams.successUrl,
                cancelUrl: validatedParams.cancelUrl,
                payerName: validatedParams.payerName,
            };
            const response = await this.client.post('/payment', requestData);
            // Log raw response for debugging
            console.log('=== MOVE PAYMENT API RESPONSE ===');
            console.log('Status:', response.status);
            console.log('Headers:', response.headers);
            console.log('Data:', JSON.stringify(response.data, null, 2));
            console.log('=== END MOVE PAYMENT RESPONSE ===');
            moveLogger.info('Raw Move Payment API response', {
                module: 'move-payment-service',
                response: response.data,
                status: response.status,
                headers: response.headers,
            });
            const validatedResponse = paymentResponseSchema.parse(response.data);
            moveLogger.info('Move payment created successfully', {
                orderId: validatedParams.orderId,
                token: validatedResponse.token,
                expires_at: validatedResponse.expires_at,
            });
            return {
                success: true,
                data: validatedResponse,
            };
        }
        catch (error) {
            console.log('=== MOVE PAYMENT ERROR ===');
            console.log('Error type:', error?.constructor?.name);
            console.log('Error message:', error instanceof Error ? error.message : String(error));
            console.log('Error stack:', error instanceof Error ? error.stack : undefined);
            console.log('=== END MOVE PAYMENT ERROR ===');
            moveLogger.error('Failed to create Move payment', {
                module: 'move-payment-service',
                error: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : undefined,
                errorType: error?.constructor?.name,
            });
            if (axios_1.default.isAxiosError(error)) {
                return {
                    success: false,
                    error: 'PAYMENT_CREATION_FAILED',
                    message: error.response?.data?.message || error.message,
                    details: error.response?.data,
                };
            }
            return {
                success: false,
                error: 'PAYMENT_CREATION_FAILED',
                message: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Get QR code for payment
     */
    async getPaymentQRCode(token) {
        try {
            moveLogger.info('Getting Move payment QR code', { token });
            const response = await this.client.get(`/payment/${token}/qr-code`);
            const validatedResponse = paymentResponseSchema.parse(response.data);
            moveLogger.info('Move payment QR code retrieved successfully', {
                token,
                expires_at: validatedResponse.expires_at,
            });
            return {
                success: true,
                data: validatedResponse,
            };
        }
        catch (error) {
            moveLogger.error('Failed to get Move payment QR code', error);
            if (axios_1.default.isAxiosError(error)) {
                return {
                    success: false,
                    error: 'QR_CODE_RETRIEVAL_FAILED',
                    message: error.response?.data?.message || error.message,
                    details: error.response?.data,
                };
            }
            return {
                success: false,
                error: 'QR_CODE_RETRIEVAL_FAILED',
                message: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Get payment URL for a payment
     * This creates a payment URL that can be used to redirect users to the payment page
     */
    async getPaymentUrl(token, returnUrl) {
        try {
            moveLogger.info('Getting Move payment URL', {
                module: 'move-payment-service',
                token,
                returnUrl,
            });
            const response = await this.client.get(`/payment/${token}/pay-url`, {
                params: { returnUrl }
            });
            const validatedResponse = paymentResponseSchema.parse(response.data);
            moveLogger.info('Move payment URL retrieved successfully', {
                module: 'move-payment-service',
                token,
                redirect_url: validatedResponse.redirect_url,
            });
            return {
                success: true,
                data: validatedResponse,
            };
        }
        catch (error) {
            moveLogger.error('Failed to get Move payment URL', error);
            if (axios_1.default.isAxiosError(error)) {
                return {
                    success: false,
                    error: 'PAYMENT_URL_FAILED',
                    message: error.response?.data?.message || 'Failed to get payment URL',
                };
            }
            return {
                success: false,
                error: 'PAYMENT_URL_FAILED',
                message: 'Failed to get payment URL',
            };
        }
    }
    /**
     * Get payment details and status
     */
    async getPaymentDetails(token) {
        try {
            moveLogger.info('Getting Move payment details', { token });
            const response = await this.client.get(`/payment/${token}`);
            const validatedResponse = paymentDetailsSchema.parse(response.data);
            moveLogger.info('Move payment details retrieved successfully', {
                token,
                orderId: validatedResponse.orderId,
                paymentId: validatedResponse.data.paymentId,
            });
            return {
                success: true,
                data: validatedResponse,
            };
        }
        catch (error) {
            moveLogger.error('Failed to get Move payment details', error);
            if (axios_1.default.isAxiosError(error)) {
                return {
                    success: false,
                    error: 'PAYMENT_DETAILS_RETRIEVAL_FAILED',
                    message: error.response?.data?.message || error.message,
                    details: error.response?.data,
                };
            }
            return {
                success: false,
                error: 'PAYMENT_DETAILS_RETRIEVAL_FAILED',
                message: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Check if Move Payment is enabled
     */
    isEnabled() {
        return env_1.env.MOVE_PAYMENT_ENABLED;
    }
}
exports.MovePaymentService = MovePaymentService;
// Export singleton instance
exports.movePaymentService = new MovePaymentService();
//# sourceMappingURL=movePaymentService.js.map