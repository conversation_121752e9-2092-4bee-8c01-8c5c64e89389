/**
 * Skrill Payment Service - Production Ready
 *
 * Production-ready service for handling Skrill Quick Checkout integration
 * Supports payment creation, QR code generation, and payment status tracking
 *
 * PRODUCTION CONFIGURATION REQUIREMENTS:
 * - SKRILL_API_URL: Production Skrill API endpoint (required)
 * - SKRILL_MERCHANT_EMAIL: Your verified Skrill merchant email (required)
 * - SKRILL_SECRET_WORD: Secret word for webhook validation (required for webhooks)
 * - SKRILL_MERCHANT_ID: Your Skrill merchant ID (optional, for advanced features)
 * - NODE_ENV: Set to 'production' for production deployment
 */
export interface SkrillPaymentRequest {
    orderId: string;
    amount: number;
    currency?: string;
    payerName: string;
    successUrl: string;
    cancelUrl: string;
    languageCode?: string;
}
export interface SkrillPaymentResponse {
    session_id: string;
    redirect_url: string;
    expires_at?: string | null;
}
export interface SkrillQRCodeResponse {
    session_id: string;
    redirect_url: string;
    expires_at?: string | null;
}
export interface SkrillPaymentDetails {
    session_id: string;
    status: string;
    transaction_id?: string;
    amount?: number;
    currency?: string;
    created_at: string;
    updated_at: string;
}
export interface SkrillServiceResponse<T = unknown> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
    details?: any;
}
declare class SkrillPaymentService {
    private client;
    private apiUrl;
    private merchantEmail;
    private secretWord;
    private merchantId;
    private isProduction;
    constructor();
    /**
     * Create a new Skrill payment session
     * Production-ready with enhanced validation and security
     */
    createPayment(params: SkrillPaymentRequest): Promise<SkrillServiceResponse<SkrillPaymentResponse>>;
    /**
     * Get QR code for Skrill payment
     * Returns the QR code URL directly
     */
    getQRCode(sessionId: string): Promise<SkrillServiceResponse<{
        qrCodeUrl: string;
    }>>;
    /**
     * Get payment URL for Skrill payment
     */
    getPaymentUrl(sessionId: string, returnUrl: string): Promise<SkrillServiceResponse<SkrillQRCodeResponse>>;
    /**
     * Extract session ID from Skrill response with enhanced security validation
     */
    private extractSessionId;
    /**
     * Get payment details and status
     * Note: Skrill primarily uses webhooks for status updates
     */
    getPaymentDetails(sessionId: string): Promise<SkrillServiceResponse<SkrillPaymentDetails>>;
    /**
     * Validate Skrill webhook signature for production security
     * This method should be used to verify webhook authenticity
     */
    validateWebhookSignature(webhookData: any): boolean;
}
export declare const skrillPaymentService: SkrillPaymentService;
export {};
/**
 * PRODUCTION DEPLOYMENT CHECKLIST:
 *
 * 1. Environment Variables (Required):
 *    - SKRILL_API_URL: https://www.skrill.com/app/payment.pl (production)
 *    - SKRILL_MERCHANT_EMAIL: Your verified Skrill merchant email
 *    - NODE_ENV: production
 *
 * 2. Environment Variables (Optional but Recommended):
 *    - SKRILL_SECRET_WORD: For webhook signature validation
 *    - SKRILL_MERCHANT_ID: For advanced features
 *    - SKRILL_LOGO_URL: Your company logo URL for payment pages
 *    - ALLOWED_CALLBACK_DOMAINS: Comma-separated list of allowed domains for callbacks
 *
 * 3. Security Considerations:
 *    - Ensure all callback URLs use HTTPS in production
 *    - Implement proper webhook endpoint with signature validation
 *    - Set up proper logging and monitoring
 *    - Configure rate limiting for payment endpoints
 *    - Implement proper error handling and user feedback
 *
 * 4. Monitoring and Logging:
 *    - Monitor payment success/failure rates
 *    - Set up alerts for API errors and timeouts
 *    - Log all payment attempts for audit purposes
 *    - Monitor webhook delivery and processing
 *
 * 5. Testing:
 *    - Test with small amounts first
 *    - Verify webhook processing
 *    - Test error scenarios and edge cases
 *    - Validate all callback URLs work correctly
 *
 * 6. Compliance:
 *    - Ensure PCI DSS compliance if handling card data
 *    - Implement proper data retention policies
 *    - Follow GDPR requirements for EU customers
 *    - Maintain transaction audit logs
 */
//# sourceMappingURL=skrillPaymentService.d.ts.map