"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.connectDatabase = connectDatabase;
exports.disconnectDatabase = disconnectDatabase;
const mongoose_1 = __importDefault(require("mongoose"));
const env_1 = require("./env");
const logger_1 = require("./logger");
const dbLogger = logger_1.logger.child({ module: 'database' });
async function connectDatabase() {
    try {
        const options = {
            maxPoolSize: 10,
            serverSelectionTimeoutMS: 5000,
            socketTimeoutMS: 45000,
            bufferCommands: false,
        };
        if (env_1.env.NODE_ENV === 'production') {
            options.retryWrites = true;
            options.w = 'majority';
        }
        await mongoose_1.default.connect(env_1.env.MONGO_URI, options);
        dbLogger.info('Database connected successfully');
    }
    catch (error) {
        dbLogger.error({ error }, 'Database connection failed');
        throw error;
    }
}
async function disconnectDatabase() {
    try {
        await mongoose_1.default.disconnect();
        dbLogger.info('Database disconnected successfully');
    }
    catch (error) {
        dbLogger.error({ error }, 'Database disconnection failed');
        throw error;
    }
}
mongoose_1.default.connection.on('error', (error) => {
    dbLogger.error({ error }, 'Database connection error');
});
mongoose_1.default.connection.on('disconnected', () => {
    dbLogger.warn('Database disconnected');
});
mongoose_1.default.connection.on('reconnected', () => {
    dbLogger.info('Database reconnected');
});
//# sourceMappingURL=database.js.map