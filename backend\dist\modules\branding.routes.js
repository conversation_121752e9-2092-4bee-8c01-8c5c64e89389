"use strict";
/**
 * Branding Routes
 *
 * Provides branding and configuration information for the POS terminal
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = brandingRoutes;
const env_1 = require("../config/env");
async function brandingRoutes(fastify) {
    /**
     * Get branding configuration
     */
    fastify.get('/branding', async (_request, reply) => {
        try {
            const brandingConfig = {
                appName: 'Enhanced POS Terminal',
                version: '2.0.0',
                description: 'Multi-provider payment processing terminal',
                theme: {
                    primaryColor: '#3B82F6', // Blue
                    secondaryColor: '#8B5CF6', // Purple
                    fontFamily: 'Poppins',
                },
                features: {
                    stripeEnabled: false, // Disabled
                    novalnetEnabled: false, // Disabled
                    paxEnabled: false, // Disabled
                    moveEnabled: true, // Active
                    squareEnabled: true, // Active
                    multiProvider: true,
                },
                providers: [
                    {
                        id: 'move',
                        name: 'Move Payment',
                        description: 'QR code-based mobile payment',
                        enabled: true,
                        priority: 1,
                    },
                    {
                        id: 'square',
                        name: 'Square Payment',
                        description: 'Secure payment processing',
                        enabled: true,
                        priority: 2,
                    },
                    {
                        id: 'stripe',
                        name: 'Stripe Terminal',
                        description: 'PAX hardware or card payment fallback (Disabled)',
                        enabled: false,
                        priority: 3,
                    },
                    {
                        id: 'novalnet',
                        name: 'Novalnet',
                        description: 'Web-based payment processing (Disabled)',
                        enabled: false,
                        priority: 4,
                    },
                ],
                ui: {
                    theme: env_1.env.UI_THEME,
                    language: env_1.env.UI_LANGUAGE,
                    fontSize: env_1.env.UI_FONT_SIZE,
                    orientation: env_1.env.UI_ORIENTATION,
                },
                terminal: {
                    id: env_1.env.TERMINAL_ID,
                    merchantId: env_1.env.MERCHANT_ID,
                    currency: env_1.env.DEFAULT_CURRENCY,
                },
            };
            reply.send({
                success: true,
                data: brandingConfig,
            });
        }
        catch (error) {
            fastify.log.error('Failed to get branding configuration', error);
            reply.status(500).send({
                success: false,
                error: 'Failed to get branding configuration',
            });
        }
    });
    /**
     * Health check for branding service
     */
    fastify.get('/branding/health', async (_request, reply) => {
        reply.send({
            success: true,
            message: 'Branding service is healthy',
            timestamp: new Date().toISOString(),
        });
    });
}
//# sourceMappingURL=branding.routes.js.map