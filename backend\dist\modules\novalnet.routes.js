"use strict";
/**
 * Novalnet Payment Routes
 *
 * Handles Novalnet payment processing endpoints
 * Provides API for creating payments, processing tokens, and managing transactions
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = novalnetRoutes;
const novalnetService_1 = require("../services/novalnetService");
const logger_1 = require("../config/logger");
const Transaction_mongo_1 = __importDefault(require("../models/Transaction.mongo"));
const zod_1 = require("zod");
const novalnetLogger = logger_1.logger.child({ module: 'novalnet-routes' });
// Request schemas for overlay integration
const createPaymentSchema = zod_1.z.object({
    amount: zod_1.z.number().positive().int(),
    currency: zod_1.z.string().length(3).default('USD'),
    order_no: zod_1.z.string(),
    return_url: zod_1.z.string().url(),
    error_return_url: zod_1.z.string().url(),
    hook_url: zod_1.z.string().url().optional(),
    customer: zod_1.z.object({
        first_name: zod_1.z.string().min(1),
        last_name: zod_1.z.string().min(1),
        email: zod_1.z.string().email(),
        customer_ip: zod_1.z.string().ip(),
        customer_no: zod_1.z.string().optional(),
        billing: zod_1.z.object({
            street: zod_1.z.string().min(1),
            house_no: zod_1.z.string().min(1),
            city: zod_1.z.string().min(1),
            zip: zod_1.z.string().min(1),
            country_code: zod_1.z.string().length(2),
        }),
    }),
    test_mode: zod_1.z.boolean().default(true),
});
const tokenPaymentSchema = zod_1.z.object({
    token: zod_1.z.string().min(1),
    amount: zod_1.z.number().positive().int(),
    currency: zod_1.z.string().length(3).default('USD'),
    order_no: zod_1.z.string(),
    customer: zod_1.z.object({
        first_name: zod_1.z.string().min(1),
        last_name: zod_1.z.string().min(1),
        email: zod_1.z.string().email(),
        customer_ip: zod_1.z.string().ip(),
    }),
    test_mode: zod_1.z.boolean().default(true),
});
const transactionDetailsSchema = zod_1.z.object({
    tid: zod_1.z.string().min(1),
});
async function novalnetRoutes(fastify) {
    /**
     * Create Novalnet payment (hosted page)
     */
    fastify.post('/novalnet/payment', async (request, reply) => {
        try {
            const validatedBody = createPaymentSchema.parse(request.body);
            novalnetLogger.info('Creating Novalnet payment', {
                amount: validatedBody.amount,
                currency: validatedBody.currency,
                order_no: validatedBody.order_no,
                customer_email: validatedBody.customer.email
            });
            // Create payment with Novalnet
            const novalnetResponse = await novalnetService_1.novalnetService.createPayment(validatedBody);
            if (novalnetResponse.result.status !== 'SUCCESS') {
                novalnetLogger.warn('Novalnet payment creation failed', {
                    status: novalnetResponse.result.status,
                    status_code: novalnetResponse.result.status_code,
                    status_text: novalnetResponse.result.status_text
                });
                return reply.status(400).send({
                    success: false,
                    error: 'PAYMENT_CREATION_FAILED',
                    message: novalnetResponse.result.status_text,
                    details: {
                        status_code: novalnetResponse.result.status_code,
                        status: novalnetResponse.result.status
                    }
                });
            }
            // Create transaction record in database
            const transaction = new Transaction_mongo_1.default({
                amount: validatedBody.amount / 100, // Convert from cents to dollars
                currency: validatedBody.currency.toLowerCase(),
                status: 'pending',
                paymentMethod: 'card',
                metadata: {
                    novalnet_tid: novalnetResponse.transaction?.tid,
                    novalnet_payment_type: novalnetResponse.transaction?.payment_type,
                    order_no: validatedBody.order_no,
                    test_mode: validatedBody.test_mode,
                    payment_provider: 'novalnet',
                    customer_email: validatedBody.customer.email,
                    hosted_page_token: novalnetResponse.hosted_page?.token
                }
            });
            await transaction.save();
            novalnetLogger.info('Novalnet payment created successfully', {
                transaction_id: transaction._id,
                novalnet_tid: novalnetResponse.transaction?.tid,
                payment_url: novalnetResponse.hosted_page?.payment_url
            });
            return reply.send({
                success: true,
                data: {
                    transaction_id: transaction._id,
                    novalnet_tid: novalnetResponse.transaction?.tid,
                    payment_url: novalnetResponse.hosted_page?.payment_url,
                    hosted_page_token: novalnetResponse.hosted_page?.token,
                    amount: validatedBody.amount,
                    currency: validatedBody.currency,
                    order_no: validatedBody.order_no,
                    status: novalnetResponse.transaction?.status
                }
            });
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                novalnetLogger.warn('Invalid Novalnet payment request', { error: error.errors });
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid request parameters',
                    details: error.errors
                });
            }
            novalnetLogger.error('Novalnet payment creation failed', error);
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Failed to create payment'
            });
        }
    });
    /**
     * Process payment with saved token
     */
    fastify.post('/novalnet/token-payment', async (request, reply) => {
        try {
            const validatedBody = tokenPaymentSchema.parse(request.body);
            novalnetLogger.info('Processing Novalnet token payment', {
                amount: validatedBody.amount,
                currency: validatedBody.currency,
                order_no: validatedBody.order_no,
                customer_email: validatedBody.customer.email
            });
            // Process token payment with Novalnet
            const novalnetResponse = await novalnetService_1.novalnetService.processTokenPayment(validatedBody);
            if (novalnetResponse.result.status !== 'SUCCESS') {
                novalnetLogger.warn('Novalnet token payment failed', {
                    status: novalnetResponse.result.status,
                    status_code: novalnetResponse.result.status_code,
                    status_text: novalnetResponse.result.status_text
                });
                return reply.status(400).send({
                    success: false,
                    error: 'TOKEN_PAYMENT_FAILED',
                    message: novalnetResponse.result.status_text,
                    details: {
                        status_code: novalnetResponse.result.status_code,
                        status: novalnetResponse.result.status
                    }
                });
            }
            // Create transaction record in database
            const transaction = new Transaction_mongo_1.default({
                amount: validatedBody.amount / 100, // Convert from cents to dollars
                currency: validatedBody.currency.toLowerCase(),
                status: novalnetResponse.transaction?.status === 'CONFIRMED' ? 'success' : 'pending',
                paymentMethod: 'card',
                metadata: {
                    novalnet_tid: novalnetResponse.transaction?.tid,
                    novalnet_payment_type: novalnetResponse.transaction?.payment_type,
                    order_no: validatedBody.order_no,
                    test_mode: validatedBody.test_mode,
                    payment_provider: 'novalnet',
                    customer_email: validatedBody.customer.email,
                    token_payment: true
                }
            });
            await transaction.save();
            novalnetLogger.info('Novalnet token payment processed successfully', {
                transaction_id: transaction._id,
                novalnet_tid: novalnetResponse.transaction?.tid,
                status: novalnetResponse.transaction?.status
            });
            return reply.send({
                success: true,
                data: {
                    transaction_id: transaction._id,
                    novalnet_tid: novalnetResponse.transaction?.tid,
                    amount: validatedBody.amount,
                    currency: validatedBody.currency,
                    order_no: validatedBody.order_no,
                    status: novalnetResponse.transaction?.status,
                    payment_type: novalnetResponse.transaction?.payment_type
                }
            });
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                novalnetLogger.warn('Invalid Novalnet token payment request', { error: error.errors });
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid request parameters',
                    details: error.errors
                });
            }
            novalnetLogger.error('Novalnet token payment failed', error);
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Failed to process token payment'
            });
        }
    });
    /**
     * Get transaction details
     */
    fastify.get('/novalnet/transaction/:tid', async (request, reply) => {
        try {
            const { tid } = transactionDetailsSchema.parse(request.params);
            novalnetLogger.info('Fetching Novalnet transaction details', { tid });
            // Get transaction details from Novalnet
            const novalnetResponse = await novalnetService_1.novalnetService.getTransactionDetails(tid);
            if (novalnetResponse.result.status !== 'SUCCESS') {
                novalnetLogger.warn('Failed to fetch Novalnet transaction details', {
                    tid,
                    status: novalnetResponse.result.status,
                    status_text: novalnetResponse.result.status_text
                });
                return reply.status(404).send({
                    success: false,
                    error: 'TRANSACTION_NOT_FOUND',
                    message: novalnetResponse.result.status_text
                });
            }
            // Also get local transaction record
            const localTransaction = await Transaction_mongo_1.default.findOne({
                'metadata.novalnet_tid': tid
            });
            novalnetLogger.info('Novalnet transaction details fetched successfully', {
                tid,
                status: novalnetResponse.transaction?.status,
                local_transaction_found: !!localTransaction
            });
            return reply.send({
                success: true,
                data: {
                    novalnet_transaction: novalnetResponse.transaction,
                    local_transaction: localTransaction,
                    result: novalnetResponse.result
                }
            });
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                novalnetLogger.warn('Invalid transaction details request', { error: error.errors });
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid transaction ID',
                    details: error.errors
                });
            }
            novalnetLogger.error('Failed to fetch transaction details', error);
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Failed to fetch transaction details'
            });
        }
    });
    /**
     * Get payment status by order number
     */
    fastify.get('/novalnet/payment-status/:orderNo', async (request, reply) => {
        try {
            const { orderNo } = request.params;
            novalnetLogger.info('Fetching payment status by order number', { orderNo });
            // Find transaction by order number
            const transaction = await Transaction_mongo_1.default.findOne({
                'metadata.order_no': orderNo,
                'metadata.payment_provider': 'novalnet'
            });
            if (!transaction) {
                return reply.status(404).send({
                    success: false,
                    error: 'TRANSACTION_NOT_FOUND',
                    message: 'Transaction not found for the given order number'
                });
            }
            novalnetLogger.info('Payment status fetched successfully', {
                orderNo,
                transaction_id: transaction._id,
                status: transaction.status
            });
            return reply.send({
                success: true,
                data: {
                    transaction_id: transaction._id,
                    order_no: orderNo,
                    status: transaction.status,
                    amount: transaction.amount,
                    currency: transaction.currency,
                    novalnet_tid: transaction.metadata?.novalnet_tid,
                    payment_type: transaction.metadata?.novalnet_payment_type,
                    created_at: transaction.createdAt,
                    updated_at: transaction.updatedAt
                }
            });
        }
        catch (error) {
            novalnetLogger.error('Failed to fetch payment status', error);
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Failed to fetch payment status'
            });
        }
    });
    /**
     * Verify overlay response (Step 3)
     */
    fastify.post('/novalnet/verify', async (request, reply) => {
        try {
            const { tid, txn_secret, status, checksum } = request.body;
            novalnetLogger.info('Verifying Novalnet overlay response', {
                tid,
                status,
                hasChecksum: !!checksum
            });
            const result = await novalnetService_1.novalnetService.verifyOverlayResponse({
                tid,
                txn_secret,
                status,
                checksum
            });
            reply.send({
                success: true,
                data: result
            });
        }
        catch (error) {
            novalnetLogger.error('Novalnet overlay verification failed', error);
            reply.status(400).send({
                success: false,
                error: 'VERIFICATION_FAILED',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
    /**
     * Health check for Novalnet service
     */
    fastify.get('/novalnet/health', async (_request, reply) => {
        reply.send({
            success: true,
            message: 'Novalnet service is healthy',
            timestamp: new Date().toISOString(),
            service: 'novalnet-payment-api'
        });
    });
}
//# sourceMappingURL=novalnet.routes.js.map