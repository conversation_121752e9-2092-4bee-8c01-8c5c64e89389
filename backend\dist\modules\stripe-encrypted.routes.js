"use strict";
/**
 * Stripe Encrypted Card Data Processing Routes
 *
 * Handles PCI-compliant processing of encrypted card data from PAX terminals
 * Creates Stripe payment methods from encrypted card data
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = stripeEncryptedRoutes;
const stripe_1 = __importDefault(require("stripe"));
const stripe = new stripe_1.default(process.env.STRIPE_SECRET_KEY, {
    apiVersion: '2024-04-10',
});
async function stripeEncryptedRoutes(fastify) {
    /**
     * Create payment method from encrypted card data
     */
    fastify.post('/create-payment-method-encrypted', async (request, reply) => {
        try {
            const { payment_method_data, encryption_type, terminal_type } = request.body;
            // Validate request
            if (!payment_method_data?.card?.encrypted_data) {
                return reply.status(400).send({
                    success: false,
                    error: 'Encrypted card data is required'
                });
            }
            // Decrypt card data (implement your decryption logic here)
            const decryptedCardData = await decryptCardData(payment_method_data.card.encrypted_data, payment_method_data.card.ksn, encryption_type);
            if (!decryptedCardData.success) {
                return reply.status(400).send({
                    success: false,
                    error: 'Failed to decrypt card data'
                });
            }
            // Create Stripe payment method
            const paymentMethod = await stripe.paymentMethods.create({
                type: 'card',
                card: {
                    number: decryptedCardData.pan,
                    exp_month: parseInt((decryptedCardData.expiryDate || '1225').substring(0, 2)),
                    exp_year: parseInt('20' + (decryptedCardData.expiryDate || '1225').substring(2, 4)),
                    cvc: decryptedCardData.cvc || '000' // CVC may not be available from card reader
                },
                billing_details: payment_method_data.billing_details || {},
                metadata: {
                    source: 'pax_terminal',
                    terminal_type,
                    encryption_type,
                    terminal_id: payment_method_data.metadata?.terminal_id || '',
                    ...payment_method_data.metadata
                }
            });
            // Log the transaction (without sensitive data)
            fastify.log.info('Payment method created from encrypted data', {
                payment_method_id: paymentMethod.id,
                terminal_type,
                terminal_id: payment_method_data.metadata?.terminal_id,
                card_brand: paymentMethod.card?.brand,
                card_last4: paymentMethod.card?.last4
            });
            reply.send({
                success: true,
                data: {
                    paymentMethod,
                    token: paymentMethod.id
                }
            });
        }
        catch (error) {
            fastify.log.error('Failed to create payment method from encrypted data', error);
            reply.status(500).send({
                success: false,
                error: error instanceof Error ? error.message : 'Payment method creation failed'
            });
        }
    });
    /**
     * Process EMV transaction with encrypted data
     */
    fastify.post('/process-emv-transaction', async (request, reply) => {
        try {
            const { amount, currency, encrypted_card_data, emv_data, terminal_id, ksn } = request.body;
            // Validate EMV data
            if (!emv_data.cryptogram || !emv_data.aid) {
                return reply.status(400).send({
                    success: false,
                    error: 'EMV cryptogram and AID are required'
                });
            }
            // Decrypt card data
            const decryptedCardData = await decryptCardData(encrypted_card_data, ksn, 'pax_terminal');
            if (!decryptedCardData.success) {
                return reply.status(400).send({
                    success: false,
                    error: 'Failed to decrypt EMV card data'
                });
            }
            // Create payment method with EMV data
            const paymentMethod = await stripe.paymentMethods.create({
                type: 'card',
                card: {
                    number: decryptedCardData.pan,
                    exp_month: parseInt((decryptedCardData.expiryDate || '1225').substring(0, 2)),
                    exp_year: parseInt('20' + (decryptedCardData.expiryDate || '1225').substring(2, 4))
                },
                metadata: {
                    source: 'emv_terminal',
                    terminal_id,
                    emv_aid: emv_data.aid,
                    emv_cryptogram: emv_data.cryptogram,
                    emv_cryptogram_type: emv_data.cryptogram_type || '',
                    emv_tvr: emv_data.tvr || '',
                    emv_tsi: emv_data.tsi || '',
                    emv_iad: emv_data.iad || ''
                }
            });
            // Create payment intent
            const paymentIntent = await stripe.paymentIntents.create({
                amount,
                currency,
                payment_method: paymentMethod.id,
                confirmation_method: 'manual',
                confirm: true,
                metadata: {
                    source: 'emv_terminal',
                    terminal_id,
                    emv_aid: emv_data.aid,
                    emv_cryptogram_type: emv_data.cryptogram_type || ''
                }
            });
            // Log EMV transaction
            fastify.log.info('EMV transaction processed', {
                payment_intent_id: paymentIntent.id,
                payment_method_id: paymentMethod.id,
                amount,
                currency,
                terminal_id,
                emv_aid: emv_data.aid,
                status: paymentIntent.status
            });
            reply.send({
                success: true,
                data: {
                    paymentIntent,
                    paymentMethod
                }
            });
        }
        catch (error) {
            fastify.log.error('EMV transaction processing failed', error);
            reply.status(500).send({
                success: false,
                error: error instanceof Error ? error.message : 'EMV transaction failed'
            });
        }
    });
    /**
     * Validate encrypted card data
     */
    fastify.post('/validate-encrypted-data', async (request, reply) => {
        try {
            const { encrypted_data, ksn, encryption_type } = request.body;
            // Validate encryption format
            const validation = validateEncryptedData(encrypted_data, ksn, encryption_type);
            reply.send({
                success: true,
                data: {
                    valid: validation.valid,
                    errors: validation.errors,
                    encryption_info: validation.info
                }
            });
        }
        catch (error) {
            fastify.log.error('Encrypted data validation failed', error);
            reply.status(500).send({
                success: false,
                error: 'Validation failed'
            });
        }
    });
}
/**
 * Decrypt card data from PAX terminal
 * This is a placeholder - implement actual decryption based on your encryption method
 */
async function decryptCardData(_encryptedData, _ksn, encryptionType) {
    try {
        // IMPORTANT: Implement actual decryption here
        // This is a placeholder for demonstration
        if (encryptionType === 'pax_terminal') {
            // For PAX terminals, you would typically use:
            // - DUKPT (Derived Unique Key Per Transaction) decryption
            // - Your encryption keys from PAX
            // - Proper cryptographic libraries
            // Placeholder decryption (DO NOT USE IN PRODUCTION)
            // In production, decrypt the actual card data here
            // Extract card data (this is simulated)
            const cardData = {
                pan: '****************', // This should come from actual decryption
                expiryDate: '1225',
                cardholderName: 'TEST CARDHOLDER',
                cvc: undefined // CVC typically not available from card readers
            };
            return {
                success: true,
                ...cardData
            };
        }
        return {
            success: false,
            error: 'Unsupported encryption type'
        };
    }
    catch (error) {
        console.error('Decryption failed:', error);
        return {
            success: false,
            error: 'Decryption failed'
        };
    }
}
/**
 * Validate encrypted data format
 */
function validateEncryptedData(encryptedData, ksn, encryptionType) {
    const errors = [];
    if (!encryptedData) {
        errors.push('Encrypted data is required');
    }
    if (encryptionType === 'pax_terminal' && !ksn) {
        errors.push('KSN (Key Serial Number) is required for PAX terminal encryption');
    }
    // Add more validation based on your encryption requirements
    return {
        valid: errors.length === 0,
        errors,
        info: {
            encryption_type: encryptionType,
            data_length: encryptedData?.length || 0,
            has_ksn: !!ksn
        }
    };
}
//# sourceMappingURL=stripe-encrypted.routes.js.map