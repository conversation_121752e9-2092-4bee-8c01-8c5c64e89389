/**
 * XMoney Payment Service
 *
 * Service for handling XMoney Payment Gateway integration
 * Supports cryptocurrency payment creation, QR code generation, and payment status tracking
 */
export interface XMoneyPaymentRequest {
    orderId: string;
    amount: number;
    currency?: string;
    successUrl: string;
    cancelUrl: string;
    payerName: string;
    payerEmail?: string;
    languageCode?: string;
}
export interface XMoneyPaymentResponse {
    id: string;
    status: string;
    amount: number;
    currency: string;
    payment_url: string;
    qr_code?: string;
}
export interface XMoneyServiceResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
    details?: any;
}
declare class XMoneyPaymentService {
    private client;
    private apiKey;
    private merchantId;
    private baseUrl;
    constructor();
    /**
     * Create a new XMoney payment
     */
    createPayment(params: XMoneyPaymentRequest): Promise<XMoneyServiceResponse<XMoneyPaymentResponse>>;
    /**
     * Get payment status
     */
    getPaymentStatus(orderId: string): Promise<XMoneyServiceResponse<any>>;
    /**
     * Health check for XMoney service
     */
    healthCheck(): Promise<XMoneyServiceResponse<{
        status: string;
    }>>;
    /**
     * Generate QR code data for payment
     */
    generateQRCodeData(paymentUrl: string): string;
    /**
     * Get supported cryptocurrencies
     */
    getSupportedCryptocurrencies(): string[];
    /**
     * Get supported fiat currencies
     */
    getSupportedFiatCurrencies(): string[];
}
export declare const xmoneyPaymentService: XMoneyPaymentService;
export {};
//# sourceMappingURL=xmoneyPaymentService.d.ts.map