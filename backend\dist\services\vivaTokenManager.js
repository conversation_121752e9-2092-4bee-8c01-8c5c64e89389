"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.vivaTokenManager = exports.VivaTokenManager = void 0;
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const cron = __importStar(require("node-cron"));
const env_1 = require("../config/env");
const logger_1 = require("../config/logger");
class VivaTokenManager {
    constructor() {
        this.cronJob = null;
        this.isRefreshing = false;
        // Create tokens directory under backend/apiaccess
        const tokensDir = path_1.default.join(process.cwd(), 'apiaccess');
        this.tokenFilePath = path_1.default.join(tokensDir, 'viva-tokens.json');
        this.ensureTokensDirectory();
        this.startTokenRefreshCron();
    }
    /**
     * Ensure the tokens directory exists
     */
    async ensureTokensDirectory() {
        try {
            const tokensDir = path_1.default.dirname(this.tokenFilePath);
            await promises_1.default.mkdir(tokensDir, { recursive: true });
            logger_1.logger.info('Viva tokens directory ensured', { path: tokensDir });
        }
        catch (error) {
            logger_1.logger.error('Failed to create tokens directory', error);
        }
    }
    /**
     * Load tokens from JSON file
     */
    async loadTokensFromFile() {
        try {
            const fileContent = await promises_1.default.readFile(this.tokenFilePath, 'utf-8');
            const tokenFile = JSON.parse(fileContent);
            return tokenFile;
        }
        catch (error) {
            // File doesn't exist or is invalid, return empty structure
            logger_1.logger.info('No existing token file found, creating new one');
            return {
                tokens: {},
                last_updated: Date.now(),
            };
        }
    }
    /**
     * Save tokens to JSON file
     */
    async saveTokensToFile(tokenFile) {
        try {
            tokenFile.last_updated = Date.now();
            const fileContent = JSON.stringify(tokenFile, null, 2);
            await promises_1.default.writeFile(this.tokenFilePath, fileContent, 'utf-8');
            logger_1.logger.info('Viva tokens saved to file', {
                path: this.tokenFilePath,
                environments: Object.keys(tokenFile.tokens)
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to save tokens to file', error);
            throw error;
        }
    }
    /**
     * Get stored token for current environment
     */
    async getStoredToken() {
        try {
            const tokenFile = await this.loadTokensFromFile();
            const environment = env_1.env.VIVA_ENVIRONMENT;
            const token = tokenFile.tokens[environment];
            if (!token) {
                logger_1.logger.info('No stored token found for environment', { environment });
                return null;
            }
            // Check if token is still valid (with 5 minute buffer)
            const now = Date.now();
            const bufferTime = 5 * 60 * 1000; // 5 minutes
            if (token.expires_at <= now + bufferTime) {
                logger_1.logger.info('Stored token is expired or expiring soon', {
                    environment,
                    expires_at: new Date(token.expires_at),
                    now: new Date(now)
                });
                return null;
            }
            logger_1.logger.info('Valid stored token found', {
                environment,
                expires_at: new Date(token.expires_at),
                scope: token.scope
            });
            return token;
        }
        catch (error) {
            logger_1.logger.error('Failed to get stored token', error);
            return null;
        }
    }
    /**
     * Store new token
     */
    async storeToken(tokenData) {
        try {
            const tokenFile = await this.loadTokensFromFile();
            const environment = env_1.env.VIVA_ENVIRONMENT;
            const now = Date.now();
            const vivaToken = {
                access_token: tokenData.access_token,
                token_type: tokenData.token_type,
                expires_in: tokenData.expires_in,
                expires_at: now + (tokenData.expires_in * 1000) - 60000, // Subtract 1 minute for safety
                scope: tokenData.scope,
                created_at: now,
                environment,
                client_id: env_1.env.VIVA_CLIENT_ID,
            };
            tokenFile.tokens[environment] = vivaToken;
            await this.saveTokensToFile(tokenFile);
            logger_1.logger.info('New Viva token stored', {
                environment,
                expires_at: new Date(vivaToken.expires_at),
                scope: vivaToken.scope
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to store token', error);
            throw error;
        }
    }
    /**
     * Request new token from Viva API
     */
    async requestNewToken() {
        if (this.isRefreshing) {
            logger_1.logger.info('Token refresh already in progress, waiting...');
            return null;
        }
        this.isRefreshing = true;
        try {
            logger_1.logger.info('Requesting new Viva OAuth2 token', {
                environment: env_1.env.VIVA_ENVIRONMENT,
                client_id: env_1.env.VIVA_CLIENT_ID
            });
            // Determine URLs based on environment
            const accountsUrl = env_1.env.VIVA_ACCOUNTS_URL || (env_1.env.VIVA_ENVIRONMENT === 'production'
                ? 'https://accounts.vivapayments.com'
                : 'https://demo-accounts.vivapayments.com');
            const credentials = `${env_1.env.VIVA_CLIENT_ID}:${env_1.env.VIVA_CLIENT_SECRET}`;
            const base64Credentials = Buffer.from(credentials).toString('base64');
            const response = await fetch(`${accountsUrl}/connect/token`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Authorization': `Basic ${base64Credentials}`,
                    'Accept': 'application/json',
                },
                body: 'grant_type=client_credentials',
            });
            if (!response.ok) {
                const errorData = await response.text();
                logger_1.logger.error('Failed to get OAuth2 token', {
                    status: response.status,
                    statusText: response.statusText,
                    error: errorData
                });
                return null;
            }
            const tokenData = await response.json();
            // Validate token data structure
            if (!tokenData.access_token || !tokenData.token_type || !tokenData.expires_in || !tokenData.scope) {
                logger_1.logger.error('Invalid token response structure', { tokenData });
                return null;
            }
            // Store the new token
            await this.storeToken(tokenData);
            // Return the stored token data
            return await this.getStoredToken();
        }
        catch (error) {
            logger_1.logger.error('Error requesting new token', error);
            return null;
        }
        finally {
            this.isRefreshing = false;
        }
    }
    /**
     * Get valid token (from storage or request new one)
     */
    async getValidToken() {
        try {
            // Try to get stored token first
            let token = await this.getStoredToken();
            // If no valid stored token, request a new one
            if (!token) {
                logger_1.logger.info('No valid stored token, requesting new one');
                token = await this.requestNewToken();
            }
            if (!token) {
                logger_1.logger.error('Failed to get valid token');
                return null;
            }
            return token.access_token;
        }
        catch (error) {
            logger_1.logger.error('Error getting valid token', error);
            return null;
        }
    }
    /**
     * Start cron job to refresh tokens every 40 minutes
     */
    startTokenRefreshCron() {
        // Run every 40 minutes: 0 */40 * * * *
        this.cronJob = cron.schedule('0 */40 * * * *', async () => {
            logger_1.logger.info('Starting scheduled token refresh');
            try {
                await this.requestNewToken();
                logger_1.logger.info('Scheduled token refresh completed successfully');
            }
            catch (error) {
                logger_1.logger.error('Scheduled token refresh failed', error);
            }
        }, {
            timezone: 'UTC'
        });
        this.cronJob.start();
        logger_1.logger.info('Viva token refresh cron job started (every 40 minutes)');
    }
    /**
     * Stop the cron job
     */
    stopTokenRefreshCron() {
        if (this.cronJob) {
            this.cronJob.stop();
            this.cronJob = null;
            logger_1.logger.info('Viva token refresh cron job stopped');
        }
    }
    /**
     * Get token file status for debugging
     */
    async getTokenStatus() {
        try {
            const tokenFile = await this.loadTokensFromFile();
            const now = Date.now();
            const bufferTime = 5 * 60 * 1000; // 5 minutes
            const tokens = Object.entries(tokenFile.tokens).map(([env, token]) => ({
                environment: env,
                expires_at: new Date(token.expires_at).toISOString(),
                is_valid: token.expires_at > now + bufferTime,
                scope: token.scope
            }));
            return {
                file_exists: true,
                environments: Object.keys(tokenFile.tokens),
                tokens
            };
        }
        catch (error) {
            return {
                file_exists: false,
                environments: [],
                tokens: []
            };
        }
    }
}
exports.VivaTokenManager = VivaTokenManager;
// Export singleton instance
exports.vivaTokenManager = new VivaTokenManager();
//# sourceMappingURL=vivaTokenManager.js.map