import { ITransaction } from '../models/Transaction.mongo';
export interface MerchantInfo {
    name: string;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    phone: string;
    email?: string;
    website?: string;
    taxId?: string;
}
export interface ReceiptData {
    transactionId: string;
    amount: number;
    status: string;
    timestamp: Date;
    paymentMethod?: string;
    cardLast4?: string;
    authCode?: string;
    protocolCode?: string;
    merchantInfo: MerchantInfo;
    customerCopy: boolean;
}
export interface PrinterConfig {
    width: number;
    paperType: 'thermal' | 'impact';
    encoding: 'utf8' | 'ascii';
}
export declare class ReceiptService {
    private static defaultMerchantInfo;
    private static printerConfig;
    static generateReceipt(transaction: ITransaction, customerCopy?: boolean): string;
    static generateMerchantReceipt(transaction: ITransaction): string;
    static generateCustomerReceipt(transaction: ITransaction): string;
    private static formatReceipt;
    private static centerText;
    private static leftRightText;
    private static formatDate;
    private static formatTime;
    private static generateAuthCode;
}
export declare const generateReceipt: (txn: any) => string;
//# sourceMappingURL=receiptService.d.ts.map