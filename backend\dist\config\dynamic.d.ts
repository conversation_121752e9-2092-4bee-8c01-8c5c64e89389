import { z } from 'zod';
export declare const PaymentRequestSchema: z.ZodObject<{
    amount: z.ZodNumber;
    currency: z.ZodDefault<z.ZodString>;
    payment_method_data: z.ZodOptional<z.ZodObject<{
        type: z.Zod<PERSON>iteral<"card">;
        card: z.ZodOptional<z.ZodObject<{
            number: z.ZodOptional<z.ZodString>;
            exp_month: z.ZodOptional<z.ZodString>;
            exp_year: z.ZodOptional<z.ZodString>;
            cvc: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            number?: string;
            cvc?: string;
            exp_month?: string;
            exp_year?: string;
        }, {
            number?: string;
            cvc?: string;
            exp_month?: string;
            exp_year?: string;
        }>>;
    }, "strip", z.ZodTypeAny, {
        type?: "card";
        card?: {
            number?: string;
            cvc?: string;
            exp_month?: string;
            exp_year?: string;
        };
    }, {
        type?: "card";
        card?: {
            number?: string;
            cvc?: string;
            exp_month?: string;
            exp_year?: string;
        };
    }>>;
    confirm: z.<PERSON>odDefault<z.ZodBoolean>;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    amount?: number;
    currency?: string;
    metadata?: Record<string, string>;
    payment_method_data?: {
        type?: "card";
        card?: {
            number?: string;
            cvc?: string;
            exp_month?: string;
            exp_year?: string;
        };
    };
    confirm?: boolean;
}, {
    amount?: number;
    currency?: string;
    metadata?: Record<string, string>;
    payment_method_data?: {
        type?: "card";
        card?: {
            number?: string;
            cvc?: string;
            exp_month?: string;
            exp_year?: string;
        };
    };
    confirm?: boolean;
}>;
export declare const RefundRequestSchema: z.ZodObject<{
    payment_intent_id: z.ZodString;
    amount: z.ZodOptional<z.ZodNumber>;
    reason: z.ZodOptional<z.ZodEnum<["duplicate", "fraudulent", "requested_by_customer"]>>;
}, "strip", z.ZodTypeAny, {
    amount?: number;
    reason?: "duplicate" | "fraudulent" | "requested_by_customer";
    payment_intent_id?: string;
}, {
    amount?: number;
    reason?: "duplicate" | "fraudulent" | "requested_by_customer";
    payment_intent_id?: string;
}>;
export declare const CancelRequestSchema: z.ZodObject<{
    payment_intent_id: z.ZodString;
    cancellation_reason: z.ZodOptional<z.ZodEnum<["duplicate", "fraudulent", "requested_by_customer", "abandoned"]>>;
}, "strip", z.ZodTypeAny, {
    payment_intent_id?: string;
    cancellation_reason?: "duplicate" | "fraudulent" | "requested_by_customer" | "abandoned";
}, {
    payment_intent_id?: string;
    cancellation_reason?: "duplicate" | "fraudulent" | "requested_by_customer" | "abandoned";
}>;
export interface DynamicConfig {
    terminal: {
        id: string;
        model: string;
        location: string;
        timezone: string;
    };
    merchant: {
        id: string;
        name: string;
        address: string;
        phone: string;
        email: string;
    };
    payment: {
        currency: string;
        timeout: number;
        retryAttempts: number;
        minimumAmount: number;
        maximumAmount: number;
    };
    receipt: {
        enabled: boolean;
        copies: number;
        includeSignature: boolean;
        footerText: string;
    };
    features: {
        cardReader: boolean;
        printer: boolean;
        audio: boolean;
        vibration: boolean;
        manualEntry: boolean;
    };
    ui: {
        theme: 'light' | 'dark' | 'auto';
        language: string;
        fontSize: 'small' | 'medium' | 'large';
        orientation: 'portrait' | 'landscape' | 'auto';
    };
}
declare const defaultConfig: DynamicConfig;
export declare class ConfigManager {
    static getConfig(): DynamicConfig;
    static updateConfig(updates: Partial<DynamicConfig>): void;
    static resetConfig(): void;
    static getTerminalInfo(): {
        id: string;
        model: string;
        location: string;
        timezone: string;
        merchantId: string;
        merchantName: string;
    };
    static getPaymentConfig(): {
        currency: string;
        timeout: number;
        retryAttempts: number;
        minimumAmount: number;
        maximumAmount: number;
    };
    static getReceiptConfig(): {
        enabled: boolean;
        copies: number;
        includeSignature: boolean;
        footerText: string;
    };
    static getFeatureFlags(): {
        cardReader: boolean;
        printer: boolean;
        audio: boolean;
        vibration: boolean;
        manualEntry: boolean;
    };
    static getUIConfig(): {
        theme: "light" | "dark" | "auto";
        language: string;
        fontSize: "small" | "medium" | "large";
        orientation: "portrait" | "landscape" | "auto";
    };
    static validatePaymentRequest(data: unknown): {
        amount?: number;
        currency?: string;
        metadata?: Record<string, string>;
        payment_method_data?: {
            type?: "card";
            card?: {
                number?: string;
                cvc?: string;
                exp_month?: string;
                exp_year?: string;
            };
        };
        confirm?: boolean;
    };
    static validateRefundRequest(data: unknown): {
        amount?: number;
        reason?: "duplicate" | "fraudulent" | "requested_by_customer";
        payment_intent_id?: string;
    };
    static validateCancelRequest(data: unknown): {
        payment_intent_id?: string;
        cancellation_reason?: "duplicate" | "fraudulent" | "requested_by_customer" | "abandoned";
    };
    static validateAmount(amount: number): {
        valid: boolean;
        error?: string;
    };
    static validateCurrency(currency: string): boolean;
    static generateTransactionMetadata(additionalData?: Record<string, any>): {
        terminal_id: string;
        merchant_id: string;
        location: string;
        timestamp: string;
    };
    private static mergeDeep;
    private static isObject;
    static validateConfig(config: Partial<DynamicConfig>): {
        valid: boolean;
        errors: string[];
    };
    static loadFromSource(source: 'database' | 'file' | 'api'): Promise<void>;
    static saveToSource(source: 'database' | 'file' | 'api'): Promise<void>;
}
export { defaultConfig };
export default ConfigManager;
//# sourceMappingURL=dynamic.d.ts.map