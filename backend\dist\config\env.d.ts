export declare const env: {
    NODE_ENV?: "production" | "test" | "development";
    PORT?: number;
    MONGO_URI?: string;
    STRIPE_SECRET_KEY?: string;
    STRIPE_PUBLISHABLE_KEY?: string;
    STRIPE_WEBHOOK_SECRET?: string;
    NOVALNET_PAYMENT_ACCESS_KEY?: string;
    NOVALNET_ACTIVATION_KEY?: string;
    NOVALNET_TARIFF_ID?: string;
    NOVALNET_WEBHOOK_SECRET?: string;
    NOVALNET_API_URL?: string;
    NOVALNET_ENABLED?: boolean;
    MOVE_PAYMENT_API_URL?: string;
    MOVE_PAYMENT_API_KEY?: string;
    MOVE_PAYMENT_MOVE_ID?: string;
    MOVE_PAYMENT_ENABLED?: boolean;
    NUVEI_MERCHANT_ID?: string;
    NUVEI_MERCHANT_SITE_ID?: string;
    NUVEI_SECRET_KEY?: string;
    NUVEI_API_URL?: string;
    NUVEI_ENVIRONMENT?: "production" | "sandbox";
    NUVEI_ENABLED?: boolean;
    XMONEY_API_KEY?: string;
    XMONEY_MERCHANT_ID?: string;
    XMONEY_API_URL?: string;
    XMONEY_ENABLED?: boolean;
    PAYONEER_API_KEY?: string;
    PAYONEER_MERCHANT_ID?: string;
    PAYONEER_API_URL?: string;
    PAYONEER_ENABLED?: boolean;
    SQUARE_ACCESS_TOKEN?: string;
    SQUARE_APPLICATION_ID?: string;
    SQUARE_LOCATION_ID?: string;
    SQUARE_ENVIRONMENT?: "production" | "sandbox";
    SQUARE_ENABLED?: boolean;
    VIVID_API_KEY?: string;
    VIVID_MERCHANT_ID?: string;
    VIVID_API_URL?: string;
    VIVID_ENABLED?: boolean;
    VIVA_MERCHANT_ID?: string;
    VIVA_API_KEY?: string;
    VIVA_CLIENT_ID?: string;
    VIVA_CLIENT_SECRET?: string;
    VIVA_SOURCE_CODE?: string;
    VIVA_ENVIRONMENT?: "production" | "demo";
    VIVA_API_URL?: string;
    VIVA_ACCOUNTS_URL?: string;
    VIVA_CHECKOUT_URL?: string;
    VIVA_HELP_URL?: string;
    VIVA_ENABLED?: boolean;
    BASE_URL?: string;
    MOCK_BANK_URL?: string;
    LOG_LEVEL?: "fatal" | "error" | "warn" | "info" | "debug" | "trace";
    JWT_SECRET?: string;
    CORS_ORIGIN?: string;
    RATE_LIMIT_MAX?: number;
    RATE_LIMIT_WINDOW?: number;
    TERMINAL_ID?: string;
    MERCHANT_ID?: string;
    DEFAULT_CURRENCY?: string;
    PAYMENT_TIMEOUT?: number;
    RETRY_ATTEMPTS?: number;
    MIN_AMOUNT?: number;
    MAX_AMOUNT?: number;
    CARD_READER_ENABLED?: boolean;
    PRINTER_ENABLED?: boolean;
    AUDIO_ENABLED?: boolean;
    VIBRATION_ENABLED?: boolean;
    MANUAL_ENTRY_ENABLED?: boolean;
    RECEIPT_ENABLED?: boolean;
    RECEIPT_COPIES?: number;
    RECEIPT_SIGNATURE?: boolean;
    RECEIPT_FOOTER?: string;
    UI_THEME?: "light" | "dark" | "auto";
    UI_LANGUAGE?: string;
    UI_FONT_SIZE?: "small" | "medium" | "large";
    UI_ORIENTATION?: "auto" | "portrait" | "landscape";
    PAX_TERMINAL_IP?: string;
    PAX_TERMINAL_PORT?: string;
    PAX_TIMEOUT?: string;
    PAX_MERCHANT_ID?: string;
    PAX_MERCHANT_NAME?: string;
    PAX_SIMULATION_MODE?: string;
    PAX_RECEIPT_ENABLED?: string;
    PAX_SIGNATURE_REQUIRED?: string;
};
export type Environment = typeof env;
//# sourceMappingURL=env.d.ts.map