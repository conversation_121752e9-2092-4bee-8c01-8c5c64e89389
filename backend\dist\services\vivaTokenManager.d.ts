interface VivaTokenData {
    access_token: string;
    token_type: string;
    expires_in: number;
    expires_at: number;
    scope: string;
    created_at: number;
    environment: string;
    client_id: string;
}
export declare class VivaTokenManager {
    private tokenFilePath;
    private cronJob;
    private isRefreshing;
    constructor();
    /**
     * Ensure the tokens directory exists
     */
    private ensureTokensDirectory;
    /**
     * Load tokens from JSON file
     */
    private loadTokensFromFile;
    /**
     * Save tokens to JSON file
     */
    private saveTokensToFile;
    /**
     * Get stored token for current environment
     */
    getStoredToken(): Promise<VivaTokenData | null>;
    /**
     * Store new token
     */
    storeToken(tokenData: {
        access_token: string;
        token_type: string;
        expires_in: number;
        scope: string;
    }): Promise<void>;
    /**
     * Request new token from Viva API
     */
    requestNewToken(): Promise<VivaTokenData | null>;
    /**
     * Get valid token (from storage or request new one)
     */
    getValidToken(): Promise<string | null>;
    /**
     * Start cron job to refresh tokens every 40 minutes
     */
    private startTokenRefreshCron;
    /**
     * Stop the cron job
     */
    stopTokenRefreshCron(): void;
    /**
     * Get token file status for debugging
     */
    getTokenStatus(): Promise<{
        file_exists: boolean;
        environments: string[];
        tokens: Array<{
            environment: string;
            expires_at: string;
            is_valid: boolean;
            scope: string;
        }>;
    }>;
}
export declare const vivaTokenManager: VivaTokenManager;
export {};
//# sourceMappingURL=vivaTokenManager.d.ts.map