"use strict";
/**
 * Nuvei Payment Routes
 *
 * Handles Nuvei Payment Gateway processing endpoints
 * Provides API for creating payments, getting QR codes, and checking payment status
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = nuveiRoutes;
const nuveiPaymentService_1 = require("../services/nuveiPaymentService");
const logger_1 = require("../config/logger");
const Transaction_mongo_1 = __importDefault(require("../models/Transaction.mongo"));
const zod_1 = require("zod");
const nuveiLogger = logger_1.logger.child({ module: 'nuvei-routes' });
// Validation schemas
const createPaymentSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
    amount: zod_1.z.number().positive().int(),
    currency: zod_1.z.string().length(3).default('GBP'),
    successUrl: zod_1.z.string().url(),
    cancelUrl: zod_1.z.string().url(),
    payerName: zod_1.z.string().min(1),
    payerEmail: zod_1.z.string().email().optional(),
    languageCode: zod_1.z.string().length(2).default('en'),
    type: zod_1.z.enum(['qr', 'url']).optional().default('qr'), // 'qr' for QR code, 'url' for payment URL
});
const paymentStatusSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
});
async function nuveiRoutes(fastify) {
    /**
     * Create Nuvei payment
     */
    fastify.post('/nuvei/payment', async (request, reply) => {
        try {
            console.log('=== RAW REQUEST BODY ===', JSON.stringify(request.body, null, 2));
            const validatedBody = createPaymentSchema.parse(request.body);
            nuveiLogger.info('Creating Nuvei payment', {
                orderId: validatedBody.orderId,
                amount: validatedBody.amount,
                currency: validatedBody.currency,
                payerName: validatedBody.payerName,
            });
            // Check for existing payment (idempotency)
            const existingTransaction = await Transaction_mongo_1.default.findOne({
                'metadata.order_id': validatedBody.orderId,
                'metadata.payment_provider': 'nuvei',
            });
            if (existingTransaction) {
                nuveiLogger.info('Returning existing Nuvei payment', {
                    orderId: validatedBody.orderId,
                    transactionId: existingTransaction._id,
                });
                return reply.send({
                    success: true,
                    data: {
                        transaction_id: existingTransaction._id,
                        order_id: validatedBody.orderId,
                        session_token: existingTransaction.metadata?.session_token,
                        redirect_url: existingTransaction.metadata?.redirect_url,
                        amount: validatedBody.amount,
                        currency: validatedBody.currency,
                        type: validatedBody.type || 'qr',
                    },
                });
            }
            // Create payment with Nuvei
            const nuveiResponse = await nuveiPaymentService_1.nuveiPaymentService.createPayment({
                orderId: validatedBody.orderId,
                amount: validatedBody.amount,
                currency: validatedBody.currency,
                successUrl: validatedBody.successUrl,
                cancelUrl: validatedBody.cancelUrl,
                payerName: validatedBody.payerName,
                payerEmail: validatedBody.payerEmail,
                languageCode: validatedBody.languageCode,
            });
            if (!nuveiResponse.success || !nuveiResponse.data) {
                nuveiLogger.warn('Nuvei payment creation failed', {
                    orderId: validatedBody.orderId,
                    error: nuveiResponse.error,
                    message: nuveiResponse.message,
                });
                return reply.status(400).send({
                    success: false,
                    error: nuveiResponse.error || 'PAYMENT_CREATION_FAILED',
                    message: nuveiResponse.message || 'Failed to create Nuvei payment',
                    details: nuveiResponse.details,
                });
            }
            nuveiLogger.info('Nuvei payment created successfully', {
                orderId: validatedBody.orderId,
                sessionToken: nuveiResponse.data.sessionToken,
                status: nuveiResponse.data.status,
            });
            // Save transaction to database
            console.log('=== SAVING TRANSACTION TO DATABASE ===');
            const transaction = new Transaction_mongo_1.default({
                amount: validatedBody.amount,
                currency: validatedBody.currency.toLowerCase(),
                status: 'pending',
                paymentMethod: 'nuvei_payment',
                paymentProvider: 'nuvei',
                metadata: {
                    payment_provider: 'nuvei',
                    order_id: validatedBody.orderId,
                    session_token: nuveiResponse.data.sessionToken,
                    payer_name: validatedBody.payerName,
                    payer_email: validatedBody.payerEmail,
                    language_code: validatedBody.languageCode,
                    success_url: validatedBody.successUrl,
                    cancel_url: validatedBody.cancelUrl,
                    redirect_url: nuveiResponse.data.redirectUrl,
                    qr_code_data: nuveiResponse.data.qrCodeData,
                    nuvei_status: nuveiResponse.data.status,
                },
            });
            const savedTransaction = await transaction.save();
            nuveiLogger.info('Transaction saved to database', {
                transactionId: savedTransaction._id,
                orderId: validatedBody.orderId,
            });
            // Return response based on type
            if (validatedBody.type === 'url' && nuveiResponse.data.redirectUrl) {
                // For URL type, return the redirect URL for opening in new tab
                return reply.send({
                    success: true,
                    data: {
                        transaction_id: savedTransaction._id,
                        order_id: validatedBody.orderId,
                        session_token: nuveiResponse.data.sessionToken,
                        redirect_url: nuveiResponse.data.redirectUrl,
                        amount: validatedBody.amount,
                        currency: validatedBody.currency,
                        type: 'url',
                    },
                });
            }
            // Default QR code response (or fallback if URL generation fails)
            return reply.send({
                success: true,
                data: {
                    transaction_id: savedTransaction._id,
                    order_id: validatedBody.orderId,
                    session_token: nuveiResponse.data.sessionToken,
                    redirect_url: nuveiResponse.data.redirectUrl,
                    qr_code_data: nuveiResponse.data.qrCodeData,
                    amount: validatedBody.amount,
                    currency: validatedBody.currency,
                    type: validatedBody.type || 'qr',
                },
            });
        }
        catch (error) {
            nuveiLogger.error('Nuvei payment creation failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid request data',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Get Nuvei payment status
     */
    fastify.get('/nuvei/payment/:orderId/status', async (request, reply) => {
        try {
            const { orderId } = request.params;
            nuveiLogger.info('Getting Nuvei payment status', { orderId });
            // Get status from Nuvei
            const statusResponse = await nuveiPaymentService_1.nuveiPaymentService.getPaymentStatus(orderId);
            if (!statusResponse.success) {
                return reply.status(400).send({
                    success: false,
                    error: statusResponse.error,
                    message: statusResponse.message,
                });
            }
            // Update transaction in database if needed
            const transaction = await Transaction_mongo_1.default.findOne({
                'metadata.order_id': orderId,
                'metadata.payment_provider': 'nuvei',
            });
            if (transaction && statusResponse.data) {
                const nuveiStatus = statusResponse.data.Status || statusResponse.data.status;
                let transactionStatus = transaction.status;
                // Map Nuvei status to our transaction status
                if (nuveiStatus === 'APPROVED' || nuveiStatus === 'SUCCESS') {
                    transactionStatus = 'success';
                }
                else if (nuveiStatus === 'DECLINED' || nuveiStatus === 'ERROR') {
                    transactionStatus = 'failed';
                }
                else if (nuveiStatus === 'PENDING') {
                    transactionStatus = 'pending';
                }
                if (transactionStatus !== transaction.status) {
                    transaction.status = transactionStatus;
                    transaction.metadata = {
                        ...transaction.metadata,
                        nuvei_status: nuveiStatus,
                        nuvei_transaction_id: statusResponse.data.TransactionID || statusResponse.data.transactionId,
                    };
                    await transaction.save();
                }
            }
            return reply.send({
                success: true,
                data: {
                    transaction_id: transaction?._id,
                    order_id: orderId,
                    status: transaction?.status || 'pending',
                    nuvei_status: statusResponse.data?.Status || statusResponse.data?.status,
                    amount: transaction?.amount,
                    currency: transaction?.currency,
                    created_at: transaction?.createdAt,
                    updated_at: transaction?.updatedAt,
                    metadata: statusResponse.data,
                },
            });
        }
        catch (error) {
            nuveiLogger.error('Failed to get Nuvei payment status', error);
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Failed to get payment status',
            });
        }
    });
    /**
     * Nuvei Payment Page webhook handler
     * Handles DMN (Direct Merchant Notification) from Nuvei Payment Page
     */
    fastify.post('/nuvei/webhook', async (request, reply) => {
        try {
            nuveiLogger.info('Nuvei webhook received', {
                body: request.body,
                query: request.query,
                headers: request.headers
            });
            // Nuvei sends webhook data as query parameters or form data
            const webhookData = {
                ...request.query,
                ...request.body
            };
            // Validate webhook authenticity using response checksum
            if (webhookData.advanceResponseChecksum) {
                // TODO: Implement checksum validation according to Nuvei documentation
                // const expectedChecksum = calculateResponseChecksum(webhookData);
                // if (webhookData.advanceResponseChecksum !== expectedChecksum) {
                //   return reply.status(400).send({ error: 'Invalid checksum' });
                // }
            }
            // Update transaction status based on webhook data
            if (webhookData.ppp_TransactionID || webhookData.TransactionID) {
                const transactionId = webhookData.ppp_TransactionID || webhookData.TransactionID;
                const status = webhookData.Status || webhookData.status;
                const orderId = webhookData.merchant_unique_id || webhookData.clientRequestId;
                nuveiLogger.info('Processing Nuvei webhook', {
                    transactionId,
                    status,
                    orderId,
                });
                // Find and update transaction
                if (orderId) {
                    const transaction = await Transaction_mongo_1.default.findOne({
                        'metadata.order_id': orderId,
                        'metadata.payment_provider': 'nuvei',
                    });
                    if (transaction) {
                        // Map Nuvei status to our status
                        let newStatus = 'pending';
                        if (status === 'APPROVED' || status === 'SUCCESS') {
                            newStatus = 'success';
                        }
                        else if (status === 'DECLINED' || status === 'ERROR' || status === 'FAIL') {
                            newStatus = 'failed';
                        }
                        transaction.status = newStatus;
                        transaction.metadata = {
                            ...transaction.metadata,
                            nuvei_transaction_id: transactionId,
                            nuvei_status: status,
                            webhook_received_at: new Date().toISOString(),
                            webhook_data: webhookData,
                        };
                        await transaction.save();
                        nuveiLogger.info('Transaction updated from webhook', {
                            transactionId: transaction._id,
                            orderId,
                            newStatus,
                        });
                    }
                }
            }
            // Respond with success (Nuvei expects 200 OK)
            return reply.send({ status: 'OK' });
        }
        catch (error) {
            nuveiLogger.error('Nuvei webhook processing failed', error);
            return reply.status(500).send({ error: 'Webhook processing failed' });
        }
    });
    /**
     * Nuvei health check
     */
    fastify.get('/nuvei/health', async (request, reply) => {
        try {
            const healthResponse = await nuveiPaymentService_1.nuveiPaymentService.healthCheck();
            if (healthResponse.success) {
                return reply.send({
                    success: true,
                    data: healthResponse.data,
                });
            }
            else {
                return reply.status(503).send({
                    success: false,
                    error: healthResponse.error,
                    message: healthResponse.message,
                });
            }
        }
        catch (error) {
            nuveiLogger.error('Health check failed', error);
            return reply.status(503).send({
                success: false,
                error: 'HEALTH_CHECK_FAILED',
                message: 'Nuvei service health check failed',
            });
        }
    });
}
//# sourceMappingURL=nuvei.routes.js.map