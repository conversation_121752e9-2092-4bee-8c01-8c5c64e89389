import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { ExternalLink } from 'lucide-react';
import { useViva, useVivaConfig } from '../hooks/useViva';
import { generateOrderId } from '../utils/paymentUtils';
import { AndroidNumericKeypad } from './AndroidNumericKeypad';

interface VivaPaymentFormProps {
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
  className?: string;
}

const VivaPaymentForm: React.FC<VivaPaymentFormProps> = ({
  onSuccess,
  onError,
  className = '',
}) => {
  const [amount, setAmount] = useState<number>(0);
  const [amountInput, setAmountInput] = useState(''); // Start empty like Move form
  const [payerName, setPayerName] = useState('');
  const [payerEmail, setPayerEmail] = useState('');

  const { createPayment, isLoading } = useViva();
  const { data: vivaConfig } = useVivaConfig();

  // Get currency from backend configuration (EUR for Viva)
  const selectedCurrency = vivaConfig?.currency || 'EUR';

  const getSuccessUrl = () => `${window.location.origin}/payment/success`;
  const getCancelUrl = () => `${window.location.origin}/payment/cancel`;

  // Helper function to handle amount changes from keyboard
  const handleAmountChange = (value: string) => {
    setAmountInput(value);
    const numericValue = parseFloat(value);
    if (!isNaN(numericValue) && numericValue >= 0) {
      setAmount(Math.round(numericValue * 100)); // Convert to cents
    }
  };

  const handleGetPaymentURL = async () => {
    if (amount <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (!payerName.trim()) {
      toast.error('Please enter payer name');
      return;
    }

    try {
      const orderId = generateOrderId();
      console.log('=== VIVA PAYMENT REQUEST ===');
      console.log('Order ID:', orderId);
      console.log('Amount:', amount);
      console.log('Currency:', selectedCurrency);
      console.log('Payer Name:', payerName.trim());
      console.log('=== END VIVA PAYMENT REQUEST ===');

      const result = await createPayment({
        orderId,
        amount: amount,
        currency: selectedCurrency,
        payerName: payerName.trim(),
        payerEmail: payerEmail.trim() || undefined,
        successUrl: getSuccessUrl(),
        cancelUrl: getCancelUrl(),
        type: 'url',
      });

      console.log('=== VIVA PAYMENT RESPONSE ===');
      console.log('Full result:', result);
      console.log('Success:', result.success);
      console.log('Data:', result.data);
      console.log('=== END VIVA PAYMENT RESPONSE ===');

      if (result.success && result.data) {
        console.log('=== VIVA REDIRECT ===');
        console.log('Redirect URL:', result.data.redirect_url);
        console.log('About to redirect...');

        // Use robust redirect function
        performRedirect(result.data.redirect_url);

        toast.success('Redirecting to payment page...');
      } else {
        console.log('=== VIVA PAYMENT FAILED ===');
        console.log('Error:', result.error);
        console.log('Message:', result.message);
        toast.error(result.message || 'Failed to generate payment URL');
      }
    } catch (error) {
      console.error('Payment URL generation error:', error);
      toast.error('Failed to generate payment URL');
    }
  };

  // Direct payment URL generation (like Square)
  const handleOpenPaymentPage = async () => {
    if (!payerName.trim()) {
      toast.error('Please enter payer name');
      return;
    }

    if (amount <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (isLoading) {
      console.log('Request already in progress, ignoring duplicate call');
      return;
    }

    try {
      const orderId = generateOrderId();
      const result = await createPayment({
        orderId,
        amount: amount,
        currency: selectedCurrency,
        payerName: payerName.trim(),
        payerEmail: payerEmail.trim() || undefined,
        successUrl: getSuccessUrl(),
        cancelUrl: getCancelUrl(),
        type: 'url',
      });

      console.log('=== VIVA PAYMENT URL RESPONSE ===');
      console.log('Full result:', result);
      console.log('Success:', result.success);
      console.log('Data:', result.data);
      console.log('=== END VIVA PAYMENT URL RESPONSE ===');

      if (result.success && result.data) {
        console.log('=== VIVA PAYMENT URL GENERATED ===');
        console.log('Payment URL:', result.data.redirect_url);
        console.log('Transaction ID:', result.data.transaction_id);
        console.log('Order ID:', result.data.order_id);
        console.log('Order Code:', result.data.orderCode);
        console.log('=== END VIVA PAYMENT URL ===');

        // Use robust redirect function (like Square)
        performRedirect(result.data.redirect_url);
        toast.success('Redirecting to payment page...');
      } else {
        console.log('=== VIVA PAYMENT URL FAILED ===');
        console.log('Error:', result.error);
        console.log('Message:', result.message);
        toast.error(result.message || 'Failed to generate payment URL');
      }
    } catch (error) {
      console.error('Payment URL generation error:', error);
      toast.error('Failed to generate payment URL');
    }
  };

  // Robust redirect function that tries multiple methods
  const performRedirect = (url: string) => {
    console.log('Attempting redirect to:', url);

    // Method 1: Direct assignment (most reliable)
    try {
      window.location.assign(url);
      return;
    } catch (e1) {
      console.warn('Method 1 failed:', e1);
    }

    // Method 2: Replace current page
    try {
      window.location.replace(url);
      return;
    } catch (e2) {
      console.warn('Method 2 failed:', e2);
    }

    // Method 3: Standard href assignment
    try {
      window.location.href = url;
      return;
    } catch (e3) {
      console.warn('Method 3 failed:', e3);
    }

    // Method 4: Create and click a link element
    try {
      const link = document.createElement('a');
      link.href = url;
      link.target = '_self';
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      return;
    } catch (e4) {
      console.warn('Method 4 failed:', e4);
    }

    // Method 5: Force navigation using form submission
    try {
      const form = document.createElement('form');
      form.method = 'GET';
      form.action = url;
      form.target = '_self';
      form.style.display = 'none';
      document.body.appendChild(form);
      form.submit();
      document.body.removeChild(form);
      return;
    } catch (e5) {
      console.warn('Method 5 failed:', e5);
    }

    // If all methods fail, show the URL to the user
    console.error('All redirect methods failed. Showing URL to user.');
    toast.error('Automatic redirect failed. Please click the link below to complete payment.');

    // Create a visible link for the user to click
    const linkElement = document.createElement('a');
    linkElement.href = url;
    linkElement.target = '_blank';
    linkElement.textContent = 'Click here to complete payment';
    linkElement.style.cssText = 'display: block; margin: 10px; padding: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; text-align: center;';

    // Find a container to add the link
    const container = document.querySelector('.space-y-6') || document.body;
    container.appendChild(linkElement);
  };



  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 font-['Poppins'] mb-2">
          Viva Wallet
        </h2>
        <p className="text-gray-600 font-['Poppins']">
          European payment processing ({selectedCurrency})
        </p>
      </div>

      {/* Main Form */}
        <div className="space-y-6">
          {/* Amount Input with Keyboard */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 font-['Poppins'] mb-2">
              Amount ({selectedCurrency}) *
            </label>
            <AndroidNumericKeypad
              value={amountInput}
              onChange={handleAmountChange}
              placeholder="0.00"
              maxLength={8}
              allowDecimal={true}
              className="w-full"
              currency={selectedCurrency}
            />
          </div>

          {/* Payer Name */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 font-['Poppins'] mb-2">
              Payer Name *
            </label>
            <input
              type="text"
              value={payerName}
              onChange={(e) => setPayerName(e.target.value)}
              placeholder="Enter payer name"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-['Poppins']"
              required
            />
          </div>

          {/* Payer Email */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 font-['Poppins'] mb-2">
              Payer Email (Optional)
            </label>
            <input
              type="email"
              value={payerEmail}
              onChange={(e) => setPayerEmail(e.target.value)}
              placeholder="Enter payer email"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-['Poppins']"
            />
          </div>

          <div className="grid grid-cols-1 gap-3">
            <button
              onClick={handleOpenPaymentPage}
              disabled={isLoading}
              className="flex items-center justify-center gap-3 w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed font-['Poppins'] font-medium"
            >
              <ExternalLink className="w-5 h-5" />
              {isLoading ? 'Opening...' : 'Open Payment Page'}
            </button>
          </div>
        </div>

      {/* Payment URL Display */}
      {showPaymentUrl && paymentData && (
        <div className="space-y-4">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 font-['Poppins'] mb-2">
              Payment Ready
            </h3>
            <p className="text-gray-600 font-['Poppins'] text-sm mb-4">
              Click the button below to complete your payment
            </p>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg border">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Amount:</span>
                <span className="font-medium">€{(paymentData.amount / 100).toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Order ID:</span>
                <span className="font-medium text-xs">{paymentData.order_id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Transaction ID:</span>
                <span className="font-medium text-xs">{paymentData.transaction_id}</span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-3">
            <button
              onClick={() => window.open(paymentData.redirect_url, '_blank')}
              className="flex items-center justify-center gap-3 w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 font-['Poppins'] font-medium"
            >
              <ExternalLink className="w-5 h-5" />
              Open Payment Page
            </button>

            <button
              onClick={() => {
                navigator.clipboard.writeText(paymentData.redirect_url);
                toast.success('Payment URL copied to clipboard!');
              }}
              className="w-full text-blue-600 py-2 px-4 border border-blue-600 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 font-['Poppins']"
            >
              Copy Payment URL
            </button>
          </div>

          <button
            onClick={() => {
              setShowPaymentUrl(false);
              setPaymentData(null);
            }}
            className="w-full text-gray-600 py-2 px-4 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 font-['Poppins']"
          >
            Create New Payment
          </button>
        </div>
      )}
    </div>
  );
};

export default VivaPaymentForm;
