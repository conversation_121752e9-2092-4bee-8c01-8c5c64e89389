"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = logsRoutes;
const SystemLog_1 = require("../models/SystemLog");
async function logsRoutes(fastify) {
    // Create a new system log
    fastify.post('/logs', async (request, reply) => {
        try {
            const logData = request.body;
            const systemLog = new SystemLog_1.SystemLog({
                ...logData,
                timestamp: new Date().toISOString()
            });
            const savedLog = await systemLog.save();
            const response = {
                success: true,
                data: {
                    _id: savedLog._id.toString(),
                    timestamp: savedLog.timestamp,
                    level: savedLog.level,
                    category: savedLog.category,
                    message: savedLog.message,
                    details: savedLog.details,
                    userId: savedLog.userId,
                    sessionId: savedLog.sessionId,
                    createdAt: savedLog.createdAt.toISOString(),
                    updatedAt: savedLog.updatedAt.toISOString()
                }
            };
            reply.status(201).send(response);
        }
        catch (error) {
            console.error('Error creating system log:', error);
            const response = {
                success: false,
                message: 'Failed to create system log',
                error: {
                    code: 'LOG_CREATE_ERROR',
                    details: error instanceof Error ? error.message : 'Unknown error'
                }
            };
            reply.status(500).send(response);
        }
    });
    // Get system logs with pagination and filtering
    fastify.get('/logs', async (request, reply) => {
        try {
            const query = request.query;
            const { limit = 50, offset = 0, level, category, userId, sessionId, startDate, endDate } = query;
            // Build filter query
            const filter = {};
            if (level)
                filter.level = level;
            if (category)
                filter.category = category;
            if (userId)
                filter.userId = userId;
            if (sessionId)
                filter.sessionId = sessionId;
            if (startDate || endDate) {
                filter.timestamp = {};
                if (startDate)
                    filter.timestamp.$gte = new Date(startDate);
                if (endDate)
                    filter.timestamp.$lte = new Date(endDate);
            }
            // Get total count
            const total = await SystemLog_1.SystemLog.countDocuments(filter);
            // Get logs with pagination
            const logs = await SystemLog_1.SystemLog.find(filter)
                .sort({ timestamp: -1 })
                .limit(Number(limit))
                .skip(Number(offset))
                .lean();
            const formattedLogs = logs.map(log => ({
                _id: log._id.toString(),
                timestamp: log.timestamp,
                level: log.level,
                category: log.category,
                message: log.message,
                details: log.details,
                userId: log.userId,
                sessionId: log.sessionId,
                createdAt: log.createdAt.toISOString(),
                updatedAt: log.updatedAt.toISOString()
            }));
            const response = {
                success: true,
                data: {
                    logs: formattedLogs,
                    pagination: {
                        limit: Number(limit),
                        offset: Number(offset),
                        count: logs.length,
                        total
                    }
                }
            };
            reply.send(response);
        }
        catch (error) {
            console.error('Error fetching system logs:', error);
            const response = {
                success: false,
                message: 'Failed to fetch system logs',
                error: {
                    code: 'LOG_FETCH_ERROR',
                    details: error instanceof Error ? error.message : 'Unknown error'
                }
            };
            reply.status(500).send(response);
        }
    });
    // Get log by ID
    fastify.get('/logs/:id', async (request, reply) => {
        try {
            const { id } = request.params;
            const log = await SystemLog_1.SystemLog.findById(id).lean();
            if (!log) {
                const response = {
                    success: false,
                    message: 'System log not found'
                };
                return reply.status(404).send(response);
            }
            const formattedLog = {
                _id: log._id.toString(),
                timestamp: log.timestamp,
                level: log.level,
                category: log.category,
                message: log.message,
                details: log.details,
                userId: log.userId,
                sessionId: log.sessionId,
                createdAt: log.createdAt.toISOString(),
                updatedAt: log.updatedAt.toISOString()
            };
            const response = {
                success: true,
                data: formattedLog
            };
            reply.send(response);
        }
        catch (error) {
            console.error('Error fetching system log:', error);
            const response = {
                success: false,
                message: 'Failed to fetch system log',
                error: {
                    code: 'LOG_FETCH_ERROR',
                    details: error instanceof Error ? error.message : 'Unknown error'
                }
            };
            reply.status(500).send(response);
        }
    });
    // Delete logs older than specified date
    fastify.delete('/logs/cleanup', async (request, reply) => {
        try {
            const { beforeDate } = request.query;
            if (!beforeDate) {
                const response = {
                    success: false,
                    message: 'beforeDate parameter is required'
                };
                return reply.status(400).send(response);
            }
            const result = await SystemLog_1.SystemLog.deleteMany({
                timestamp: { $lt: new Date(beforeDate) }
            });
            const response = {
                success: true,
                data: { deletedCount: result.deletedCount },
                message: `Deleted ${result.deletedCount} log entries`
            };
            reply.send(response);
        }
        catch (error) {
            console.error('Error cleaning up logs:', error);
            const response = {
                success: false,
                message: 'Failed to cleanup logs',
                error: {
                    code: 'LOG_CLEANUP_ERROR',
                    details: error instanceof Error ? error.message : 'Unknown error'
                }
            };
            reply.status(500).send(response);
        }
    });
}
//# sourceMappingURL=logs.routes.js.map