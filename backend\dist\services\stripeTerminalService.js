"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.stripeTerminalService = exports.StripeTerminalService = void 0;
const stripe_1 = __importDefault(require("stripe"));
const env_1 = require("../config/env");
const logger_1 = require("../config/logger");
const terminalLogger = logger_1.logger.child({ module: 'stripe-terminal' });
class StripeTerminalService {
    constructor() {
        this.stripe = new stripe_1.default(env_1.env.STRIPE_SECRET_KEY, {
            apiVersion: '2024-04-10',
            typescript: true,
        });
    }
    /**
     * Create a connection token for terminal SDK
     */
    async createConnectionToken(location) {
        try {
            terminalLogger.info('Creating connection token', { location });
            const connectionToken = await this.stripe.terminal.connectionTokens.create({
                location: location || this.defaultLocation?.id,
            });
            return {
                secret: connectionToken.secret,
                location: connectionToken.location || undefined,
            };
        }
        catch (error) {
            terminalLogger.error('Failed to create connection token', { error });
            throw new Error('Failed to create connection token');
        }
    }
    /**
     * Create or get default location
     */
    async ensureLocation() {
        try {
            if (this.defaultLocation) {
                return this.defaultLocation;
            }
            // Try to find existing location
            const locations = await this.stripe.terminal.locations.list({ limit: 1 });
            if (locations.data.length > 0) {
                this.defaultLocation = locations.data[0];
                terminalLogger.info('Using existing location', { locationId: this.defaultLocation.id });
                return this.defaultLocation;
            }
            // Create new location
            const location = await this.stripe.terminal.locations.create({
                display_name: 'Stripe Terminal POS',
                address: {
                    line1: '123 Main Street',
                    city: 'San Francisco',
                    state: 'CA',
                    postal_code: '94111',
                    country: 'US',
                },
            });
            this.defaultLocation = location;
            terminalLogger.info('Created new location', { locationId: this.defaultLocation.id });
            return this.defaultLocation;
        }
        catch (error) {
            terminalLogger.error('Failed to ensure location', { error });
            throw new Error('Failed to setup terminal location');
        }
    }
    /**
     * List all terminal readers
     */
    async listReaders() {
        try {
            const readers = await this.stripe.terminal.readers.list();
            return readers.data;
        }
        catch (error) {
            terminalLogger.error('Failed to list readers', { error });
            throw new Error('Failed to list terminal readers');
        }
    }
    /**
     * Get a specific terminal reader
     */
    async getReader(readerId) {
        try {
            const reader = await this.stripe.terminal.readers.retrieve(readerId);
            return reader;
        }
        catch (error) {
            terminalLogger.error('Failed to get reader', { error, readerId });
            throw new Error('Failed to get terminal reader');
        }
    }
    /**
     * Create a payment intent for terminal processing
     */
    async createPaymentIntent(data) {
        try {
            terminalLogger.info('Creating payment intent for terminal', {
                amount: data.amount,
                currency: data.currency
            });
            const paymentIntent = await this.stripe.paymentIntents.create({
                amount: data.amount,
                currency: data.currency,
                payment_method_types: data.payment_method_types,
                capture_method: data.capture_method,
                metadata: {
                    source: 'stripe_terminal',
                    ...data.metadata,
                },
            });
            terminalLogger.info('Payment intent created', {
                paymentIntentId: paymentIntent.id,
                amount: paymentIntent.amount
            });
            return paymentIntent;
        }
        catch (error) {
            terminalLogger.error('Failed to create payment intent', { error });
            throw new Error('Failed to create payment intent');
        }
    }
    /**
     * Process payment on terminal reader
     */
    async processPayment(readerId, paymentIntentId) {
        try {
            terminalLogger.info('Processing payment on terminal', {
                readerId,
                paymentIntentId
            });
            await this.stripe.terminal.readers.processPaymentIntent(readerId, {
                payment_intent: paymentIntentId,
            });
            // Wait for payment to complete
            let paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);
            // Poll for completion (in production, use webhooks instead)
            let attempts = 0;
            const maxAttempts = 30; // 30 seconds timeout
            while (paymentIntent.status === 'requires_payment_method' && attempts < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, 1000));
                paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);
                attempts++;
            }
            terminalLogger.info('Payment processing completed', {
                paymentIntentId,
                status: paymentIntent.status
            });
            return paymentIntent;
        }
        catch (error) {
            terminalLogger.error('Failed to process payment on terminal', {
                error,
                readerId,
                paymentIntentId
            });
            throw new Error('Failed to process payment on terminal');
        }
    }
    /**
     * Cancel payment intent
     */
    async cancelPaymentIntent(paymentIntentId) {
        try {
            terminalLogger.info('Cancelling payment intent', { paymentIntentId });
            const paymentIntent = await this.stripe.paymentIntents.cancel(paymentIntentId);
            terminalLogger.info('Payment intent cancelled', { paymentIntentId });
            return paymentIntent;
        }
        catch (error) {
            terminalLogger.error('Failed to cancel payment intent', { error, paymentIntentId });
            throw new Error('Failed to cancel payment intent');
        }
    }
    /**
     * Capture payment intent (for manual capture)
     */
    async capturePaymentIntent(paymentIntentId) {
        try {
            terminalLogger.info('Capturing payment intent', { paymentIntentId });
            const paymentIntent = await this.stripe.paymentIntents.capture(paymentIntentId);
            terminalLogger.info('Payment intent captured', { paymentIntentId });
            return paymentIntent;
        }
        catch (error) {
            terminalLogger.error('Failed to capture payment intent', { error, paymentIntentId });
            throw new Error('Failed to capture payment intent');
        }
    }
    /**
     * Create refund for a payment
     */
    async createRefund(paymentIntentId, amount) {
        try {
            terminalLogger.info('Creating refund', { paymentIntentId, amount });
            const refund = await this.stripe.refunds.create({
                payment_intent: paymentIntentId,
                amount: amount,
            });
            terminalLogger.info('Refund created', {
                refundId: refund.id,
                paymentIntentId,
                amount: refund.amount
            });
            return refund;
        }
        catch (error) {
            terminalLogger.error('Failed to create refund', { error, paymentIntentId });
            throw new Error('Failed to create refund');
        }
    }
    /**
     * Get terminal reader status
     */
    async getReaderStatus(readerId) {
        try {
            const reader = await this.getReader(readerId);
            return {
                status: reader.status,
                last_seen_at: reader.last_seen_at,
            };
        }
        catch (error) {
            terminalLogger.error('Failed to get reader status', { error, readerId });
            throw new Error('Failed to get reader status');
        }
    }
    /**
     * Simulate payment for testing (when no physical terminal available)
     */
    async simulatePayment(data) {
        try {
            terminalLogger.info('Simulating payment (test mode)', { amount: data.amount });
            // Create payment intent
            const paymentIntent = await this.createPaymentIntent(data);
            // Simulate successful payment with test payment method
            const confirmedPaymentIntent = await this.stripe.paymentIntents.confirm(paymentIntent.id, {
                payment_method: 'pm_card_visa', // Test payment method
                return_url: 'https://example.com/return',
            });
            terminalLogger.info('Payment simulation completed', {
                paymentIntentId: confirmedPaymentIntent.id,
                status: confirmedPaymentIntent.status
            });
            return confirmedPaymentIntent;
        }
        catch (error) {
            terminalLogger.error('Failed to simulate payment', { error });
            throw new Error('Failed to simulate payment');
        }
    }
    /**
     * Get payment method details from payment intent
     */
    async getPaymentMethodDetails(paymentIntentId) {
        try {
            const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId, {
                expand: ['payment_method'],
            });
            if (!paymentIntent.payment_method) {
                return null;
            }
            const paymentMethod = paymentIntent.payment_method;
            return {
                type: paymentMethod.type,
                card: paymentMethod.card ? {
                    brand: paymentMethod.card.brand,
                    last4: paymentMethod.card.last4,
                    exp_month: paymentMethod.card.exp_month,
                    exp_year: paymentMethod.card.exp_year,
                } : null,
            };
        }
        catch (error) {
            terminalLogger.error('Failed to get payment method details', { error, paymentIntentId });
            return null;
        }
    }
}
exports.StripeTerminalService = StripeTerminalService;
// Export singleton instance
exports.stripeTerminalService = new StripeTerminalService();
//# sourceMappingURL=stripeTerminalService.js.map