"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createApp = createApp;
exports.startServer = startServer;
const fastify_1 = __importDefault(require("fastify"));
const env_1 = require("./config/env");
const logger_1 = require("./config/logger");
const database_1 = require("./config/database");
const payment_routes_1 = __importDefault(require("./modules/payment.routes"));
const stripe_routes_1 = __importDefault(require("./modules/stripe.routes"));
const transaction_routes_1 = __importDefault(require("./modules/transaction.routes"));
const protocol_routes_1 = __importDefault(require("./modules/protocol.routes"));
const logs_routes_1 = __importDefault(require("./modules/logs.routes"));
const isDevelopment = env_1.env.NODE_ENV === 'development';
async function createApp() {
    const app = (0, fastify_1.default)({
        logger: logger_1.logger,
        trustProxy: true,
        disableRequestLogging: !isDevelopment,
    });
    await app.register(Promise.resolve().then(() => __importStar(require('@fastify/helmet'))), {
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                styleSrc: ["'self'", "'unsafe-inline'"],
                scriptSrc: ["'self'"],
                imgSrc: ["'self'", "data:", "https:"],
            },
        },
    });
    await app.register(Promise.resolve().then(() => __importStar(require('@fastify/cors'))), {
        origin: '*', // Allow all origins
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    });
    await app.register(Promise.resolve().then(() => __importStar(require('@fastify/rate-limit'))), {
        max: env_1.env.RATE_LIMIT_MAX,
        timeWindow: env_1.env.RATE_LIMIT_WINDOW,
    });
    app.addHook('onRequest', async (request) => {
        request.log.info({ url: request.url, method: request.method }, 'Incoming request');
    });
    app.addHook('onResponse', async (request, reply) => {
        request.log.info({
            url: request.url,
            method: request.method,
            statusCode: reply.statusCode,
            responseTime: reply.getResponseTime()
        }, 'Request completed');
    });
    app.setErrorHandler(async (error, request, reply) => {
        request.log.error({ error, url: request.url, method: request.method }, 'Request error');
        if (error.validation) {
            return reply.status(400).send({
                success: false,
                error: 'VALIDATION_ERROR',
                message: 'Request validation failed',
                details: error.validation,
            });
        }
        if (error.statusCode && error.statusCode < 500) {
            return reply.status(error.statusCode).send({
                success: false,
                error: error.code || 'CLIENT_ERROR',
                message: error.message,
            });
        }
        return reply.status(500).send({
            success: false,
            error: 'INTERNAL_SERVER_ERROR',
            message: isDevelopment ? error.message : 'An unexpected error occurred',
        });
    });
    app.setNotFoundHandler(async (request, reply) => {
        return reply.status(404).send({
            success: false,
            error: 'NOT_FOUND',
            message: `Route ${request.method} ${request.url} not found`,
        });
    });
    app.get('/health', async () => {
        return {
            success: true,
            data: {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                environment: env_1.env.NODE_ENV,
            }
        };
    });
    await app.register(payment_routes_1.default, { prefix: '/api/v1' });
    await app.register(stripe_routes_1.default, { prefix: '/api/v1' });
    await app.register(transaction_routes_1.default, { prefix: '/api/v1' });
    await app.register(protocol_routes_1.default, { prefix: '/api/v1' });
    await app.register(logs_routes_1.default, { prefix: '/api/v1' });
    const stripeEncryptedRoutes = await Promise.resolve().then(() => __importStar(require('./modules/stripe-encrypted.routes')));
    await app.register(stripeEncryptedRoutes.default, { prefix: '/api/v1/stripe' });
    const { receiptRoutes } = await Promise.resolve().then(() => __importStar(require('./modules/receipt.routes')));
    await app.register(receiptRoutes, { prefix: '/api/v1' });
    const { realTimePaymentRoutes } = await Promise.resolve().then(() => __importStar(require('./modules/realTimePayment.routes')));
    await app.register(realTimePaymentRoutes, { prefix: '/api/v1' });
    const paxRoutes = await Promise.resolve().then(() => __importStar(require('./modules/pax.routes')));
    await app.register(paxRoutes.default, { prefix: '/api/v1' });
    // Import and register webhook routes
    const webhookRoutes = await Promise.resolve().then(() => __importStar(require('./modules/webhook.routes')));
    await app.register(webhookRoutes.default, { prefix: '/api/v1' });
    // Import and register enhanced payment routes
    const enhancedPaymentRoutes = await Promise.resolve().then(() => __importStar(require('./modules/enhanced-payment.routes')));
    await app.register(enhancedPaymentRoutes.default, { prefix: '/api/v1' });
    // Import and register Stripe Terminal routes
    const stripeTerminalRoutes = await Promise.resolve().then(() => __importStar(require('./modules/stripe-terminal.routes')));
    await app.register(stripeTerminalRoutes.default, { prefix: '/api/v1' });
    // Import and register Novalnet routes
    const novalnetRoutes = await Promise.resolve().then(() => __importStar(require('./modules/novalnet.routes')));
    await app.register(novalnetRoutes.default, { prefix: '/api/v1' });
    // Import and register Novalnet webhook routes
    const novalnetWebhookRoutes = await Promise.resolve().then(() => __importStar(require('./modules/novalnet-webhook.routes')));
    await app.register(novalnetWebhookRoutes.default, { prefix: '/api/v1' });
    // Import and register Move Payment routes
    const moveRoutes = await Promise.resolve().then(() => __importStar(require('./modules/move.routes')));
    await app.register(moveRoutes.default, { prefix: '/api/v1' });
    // Import and register Skrill Payment routes
    const skrillRoutes = await Promise.resolve().then(() => __importStar(require('./modules/skrill.routes')));
    await app.register(skrillRoutes.skrillRoutes, { prefix: '/api/v1' });
    // Import and register Nuvei Payment routes
    const nuveiRoutes = await Promise.resolve().then(() => __importStar(require('./modules/nuvei.routes')));
    await app.register(nuveiRoutes.default, { prefix: '/api/v1' });
    // Import and register XMoney Payment routes
    const xmoneyRoutes = await Promise.resolve().then(() => __importStar(require('./modules/xmoney.routes')));
    await app.register(xmoneyRoutes.default, { prefix: '/api/v1' });
    // Import and register Payoneer Payment routes
    const payoneerRoutes = await Promise.resolve().then(() => __importStar(require('./modules/payoneer.routes')));
    await app.register(payoneerRoutes.default, { prefix: '/api/v1' });
    // Import and register Square Payment routes
    const squareRoutes = await Promise.resolve().then(() => __importStar(require('./modules/square.routes')));
    await app.register(squareRoutes.default, { prefix: '/api/v1' });
    // Import and register Vivid Money Payment routes
    const vividRoutes = await Promise.resolve().then(() => __importStar(require('./modules/vivid.routes')));
    await app.register(vividRoutes.default, { prefix: '/api/v1' });
    // Import and register Viva Wallet Payment routes
    const vivaRoutes = await Promise.resolve().then(() => __importStar(require('./modules/viva.routes')));
    await app.register(vivaRoutes.default, { prefix: '/api/v1' });
    // Import and register branding routes
    const brandingRoutes = await Promise.resolve().then(() => __importStar(require('./modules/branding.routes')));
    await app.register(brandingRoutes.default, { prefix: '/api' });
    return app;
}
async function startServer() {
    try {
        await (0, database_1.connectDatabase)();
        const app = await createApp();
        await app.listen({
            port: env_1.env.PORT,
            host: '0.0.0.0'
        });
        logger_1.logger.info(`Server listening on port ${env_1.env.PORT} in ${env_1.env.NODE_ENV} mode`);
        return app;
    }
    catch (error) {
        logger_1.logger.error({ error }, 'Failed to start server');
        process.exit(1);
    }
}
//# sourceMappingURL=app.js.map