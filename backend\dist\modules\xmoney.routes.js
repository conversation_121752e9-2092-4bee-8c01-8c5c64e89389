"use strict";
/**
 * XMoney Payment Routes
 *
 * Handles XMoney Payment Gateway processing endpoints
 * Provides API for creating payments, getting QR codes, and checking payment status
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = xmoneyRoutes;
const xmoneyPaymentService_1 = require("../services/xmoneyPaymentService");
const logger_1 = require("../config/logger");
const Transaction_mongo_1 = __importDefault(require("../models/Transaction.mongo"));
const zod_1 = require("zod");
const xmoneyLogger = logger_1.logger.child({ module: 'xmoney-routes' });
// Validation schemas
const createPaymentSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
    amount: zod_1.z.number().positive().int(),
    currency: zod_1.z.string().length(3).default('GBP'),
    successUrl: zod_1.z.string().url(),
    cancelUrl: zod_1.z.string().url(),
    payerName: zod_1.z.string().min(1),
    payerEmail: zod_1.z.string().email().optional(),
    languageCode: zod_1.z.string().length(2).default('en'),
    type: zod_1.z.enum(['qr', 'url']).optional().default('qr'), // 'qr' for QR code, 'url' for payment URL
});
const paymentStatusSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
});
const webhookSchema = zod_1.z.object({
    order_id: zod_1.z.string().optional(),
    status: zod_1.z.string().optional(),
    transaction_id: zod_1.z.string().optional(),
    // Add other webhook fields as needed
});
async function xmoneyRoutes(fastify) {
    /**
     * Create XMoney payment
     */
    fastify.post('/xmoney/payment', async (request, reply) => {
        try {
            console.log('=== RAW REQUEST BODY ===', JSON.stringify(request.body, null, 2));
            const validatedBody = createPaymentSchema.parse(request.body);
            xmoneyLogger.info('Creating XMoney payment', {
                orderId: validatedBody.orderId,
                amount: validatedBody.amount,
                currency: validatedBody.currency,
                payerName: validatedBody.payerName,
            });
            // Check for existing payment (idempotency)
            const existingTransaction = await Transaction_mongo_1.default.findOne({
                'metadata.order_id': validatedBody.orderId,
                'metadata.payment_provider': 'xmoney',
            });
            if (existingTransaction) {
                xmoneyLogger.info('Returning existing XMoney payment', {
                    orderId: validatedBody.orderId,
                    transactionId: existingTransaction._id,
                });
                return reply.send({
                    success: true,
                    data: {
                        transaction_id: existingTransaction._id,
                        order_id: validatedBody.orderId,
                        payment_id: existingTransaction.metadata?.payment_id,
                        payment_url: existingTransaction.metadata?.payment_url,
                        qr_code_data: existingTransaction.metadata?.qr_code_data,
                        amount: validatedBody.amount,
                        currency: validatedBody.currency,
                        type: validatedBody.type || 'qr',
                    },
                });
            }
            // Create payment with XMoney
            const xmoneyResponse = await xmoneyPaymentService_1.xmoneyPaymentService.createPayment({
                orderId: validatedBody.orderId,
                amount: validatedBody.amount,
                currency: validatedBody.currency,
                successUrl: validatedBody.successUrl,
                cancelUrl: validatedBody.cancelUrl,
                payerName: validatedBody.payerName,
                payerEmail: validatedBody.payerEmail,
                languageCode: validatedBody.languageCode,
            });
            if (!xmoneyResponse.success || !xmoneyResponse.data) {
                xmoneyLogger.warn('XMoney payment creation failed', {
                    orderId: validatedBody.orderId,
                    error: xmoneyResponse.error,
                    message: xmoneyResponse.message,
                });
                return reply.status(400).send({
                    success: false,
                    error: xmoneyResponse.error || 'PAYMENT_CREATION_FAILED',
                    message: xmoneyResponse.message || 'Failed to create XMoney payment',
                    details: xmoneyResponse.details,
                });
            }
            xmoneyLogger.info('XMoney payment created successfully', {
                orderId: validatedBody.orderId,
                paymentId: xmoneyResponse.data.id,
                status: xmoneyResponse.data.status,
            });
            // Save transaction to database
            console.log('=== SAVING TRANSACTION TO DATABASE ===');
            const transaction = new Transaction_mongo_1.default({
                amount: validatedBody.amount,
                currency: validatedBody.currency.toLowerCase(),
                status: 'pending',
                paymentMethod: 'xmoney_payment',
                paymentProvider: 'xmoney',
                metadata: {
                    payment_provider: 'xmoney',
                    order_id: validatedBody.orderId,
                    payment_id: xmoneyResponse.data.id,
                    payer_name: validatedBody.payerName,
                    payer_email: validatedBody.payerEmail,
                    language_code: validatedBody.languageCode,
                    success_url: validatedBody.successUrl,
                    cancel_url: validatedBody.cancelUrl,
                    payment_url: xmoneyResponse.data.payment_url,
                    qr_code_data: xmoneyResponse.data.qr_code || xmoneyResponse.data.payment_url,
                    xmoney_status: xmoneyResponse.data.status,
                },
            });
            const savedTransaction = await transaction.save();
            xmoneyLogger.info('Transaction saved to database', {
                transactionId: savedTransaction._id,
                orderId: validatedBody.orderId,
            });
            // Return response based on type
            if (validatedBody.type === 'url' && xmoneyResponse.data.payment_url) {
                // For URL type, return the payment URL for opening in new tab
                return reply.send({
                    success: true,
                    data: {
                        transaction_id: savedTransaction._id,
                        order_id: validatedBody.orderId,
                        payment_id: xmoneyResponse.data.id,
                        payment_url: xmoneyResponse.data.payment_url,
                        amount: validatedBody.amount,
                        currency: validatedBody.currency,
                        type: 'url',
                    },
                });
            }
            // Default QR code response (or fallback if URL generation fails)
            return reply.send({
                success: true,
                data: {
                    transaction_id: savedTransaction._id,
                    order_id: validatedBody.orderId,
                    payment_id: xmoneyResponse.data.id,
                    payment_url: xmoneyResponse.data.payment_url,
                    qr_code_data: xmoneyResponse.data.qr_code || xmoneyResponse.data.payment_url,
                    amount: validatedBody.amount,
                    currency: validatedBody.currency,
                    type: validatedBody.type || 'qr',
                },
            });
        }
        catch (error) {
            xmoneyLogger.error('XMoney payment creation failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid request data',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Get XMoney payment status
     */
    fastify.get('/xmoney/payment/:orderId/status', async (request, reply) => {
        try {
            const { orderId } = request.params;
            xmoneyLogger.info('Getting XMoney payment status', { orderId });
            // Get status from XMoney
            const statusResponse = await xmoneyPaymentService_1.xmoneyPaymentService.getPaymentStatus(orderId);
            if (!statusResponse.success) {
                return reply.status(400).send({
                    success: false,
                    error: statusResponse.error,
                    message: statusResponse.message,
                });
            }
            // Update transaction in database if needed
            const transaction = await Transaction_mongo_1.default.findOne({
                'metadata.order_id': orderId,
                'metadata.payment_provider': 'xmoney',
            });
            if (transaction && statusResponse.data) {
                const xmoneyStatus = statusResponse.data.status;
                let transactionStatus = transaction.status;
                // Map XMoney status to our transaction status
                if (xmoneyStatus === 'completed' || xmoneyStatus === 'confirmed') {
                    transactionStatus = 'success';
                }
                else if (xmoneyStatus === 'failed' || xmoneyStatus === 'cancelled') {
                    transactionStatus = 'failed';
                }
                else if (xmoneyStatus === 'pending' || xmoneyStatus === 'waiting') {
                    transactionStatus = 'pending';
                }
                if (transactionStatus !== transaction.status) {
                    transaction.status = transactionStatus;
                    transaction.metadata = {
                        ...transaction.metadata,
                        xmoney_status: xmoneyStatus,
                        xmoney_transaction_id: statusResponse.data.transaction_id,
                    };
                    await transaction.save();
                }
            }
            return reply.send({
                success: true,
                data: {
                    transaction_id: transaction?._id,
                    order_id: orderId,
                    status: transaction?.status || 'pending',
                    xmoney_status: statusResponse.data?.status,
                    amount: transaction?.amount,
                    currency: transaction?.currency,
                    created_at: transaction?.createdAt,
                    updated_at: transaction?.updatedAt,
                    metadata: statusResponse.data,
                },
            });
        }
        catch (error) {
            xmoneyLogger.error('Failed to get XMoney payment status', error);
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Failed to get payment status',
            });
        }
    });
    /**
     * XMoney webhook endpoint
     */
    fastify.post('/xmoney/webhook', async (request, reply) => {
        try {
            xmoneyLogger.info('Received XMoney webhook', { body: request.body });
            const webhookData = request.body;
            const orderId = webhookData.order_id;
            const status = webhookData.status;
            const transactionId = webhookData.transaction_id;
            if (!orderId) {
                xmoneyLogger.warn('Webhook missing order_id', { webhookData });
                return reply.status(400).send({ error: 'Missing order_id' });
            }
            // Find and update transaction
            const transaction = await Transaction_mongo_1.default.findOne({
                'metadata.order_id': orderId,
                'metadata.payment_provider': 'xmoney',
            });
            if (!transaction) {
                xmoneyLogger.warn('Transaction not found for webhook', { orderId });
                return reply.status(404).send({ error: 'Transaction not found' });
            }
            // Map XMoney status to our transaction status
            let transactionStatus = transaction.status;
            if (status === 'completed' || status === 'confirmed') {
                transactionStatus = 'success';
            }
            else if (status === 'failed' || status === 'cancelled') {
                transactionStatus = 'failed';
            }
            else if (status === 'pending' || status === 'waiting') {
                transactionStatus = 'pending';
            }
            // Update transaction
            transaction.status = transactionStatus;
            transaction.metadata = {
                ...transaction.metadata,
                xmoney_status: status,
                xmoney_transaction_id: transactionId,
                webhook_received_at: new Date().toISOString(),
            };
            await transaction.save();
            xmoneyLogger.info('Transaction updated from webhook', {
                transactionId: transaction._id,
                orderId,
                status: transactionStatus,
                xmoneyStatus: status,
            });
            return reply.send({ success: true });
        }
        catch (error) {
            xmoneyLogger.error('Webhook processing failed', error);
            return reply.status(500).send({ error: 'Webhook processing failed' });
        }
    });
    /**
     * XMoney health check
     */
    fastify.get('/xmoney/health', async (request, reply) => {
        try {
            const healthResponse = await xmoneyPaymentService_1.xmoneyPaymentService.healthCheck();
            if (healthResponse.success) {
                return reply.send({
                    success: true,
                    data: healthResponse.data,
                });
            }
            else {
                return reply.status(503).send({
                    success: false,
                    error: healthResponse.error,
                    message: healthResponse.message,
                });
            }
        }
        catch (error) {
            xmoneyLogger.error('Health check failed', error);
            return reply.status(503).send({
                success: false,
                error: 'HEALTH_CHECK_FAILED',
                message: 'XMoney service health check failed',
            });
        }
    });
}
//# sourceMappingURL=xmoney.routes.js.map