"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createProtocolMessage = void 0;
const ProtocolMessage_mongo_1 = __importDefault(require("../models/ProtocolMessage.mongo"));
const createProtocolMessage = async (request, reply) => {
    const { protocol, mti, amount, pan, stan, authCode, origStan, timestamp } = request.body;
    if (!protocol || !mti || !amount || !stan || !timestamp) {
        return reply.status(400).send({
            status: false,
            message: 'INVALID_INPUT',
            meta: {
                error: 'Missing required protocol message fields.',
                suggestions: ['Provide all required fields and try again.'],
            },
        });
    }
    try {
        const message = new ProtocolMessage_mongo_1.default({ protocol, mti, amount, pan, stan, authCode, origStan, timestamp });
        await message.save();
        return reply.status(201).send({ status: true, message: 'Protocol message created', data: message });
    }
    catch (err) {
        return reply.status(500).send({
            status: false,
            message: 'DB_ERROR',
            meta: { error: err.message },
        });
    }
};
exports.createProtocolMessage = createProtocolMessage;
//# sourceMappingURL=protocolController.mongo.js.map