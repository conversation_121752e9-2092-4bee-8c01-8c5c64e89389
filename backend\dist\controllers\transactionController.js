"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTxnById = exports.listTxns = exports.createTxn = void 0;
const transactionService_1 = require("../services/transactionService");
const receiptService_1 = require("../services/receiptService");
const zod_1 = require("zod");
const logger_1 = require("../config/logger");
const controllerLogger = logger_1.logger.child({ module: 'transaction-controller' });
const createTransactionSchema = zod_1.z.object({
    amount: zod_1.z.number().positive(),
    status: zod_1.z.enum(['success', 'failure', 'pending']),
    protocolCode: zod_1.z.string().optional(),
    stripePaymentIntentId: zod_1.z.string().optional(),
    receiptUrl: zod_1.z.string().url().optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
});
const listTransactionsSchema = zod_1.z.object({
    limit: zod_1.z.string().transform(val => parseInt(val, 10)).pipe(zod_1.z.number().min(1).max(100)).optional(),
    offset: zod_1.z.string().transform(val => parseInt(val, 10)).pipe(zod_1.z.number().min(0)).optional(),
});
const createTxn = async (request, reply) => {
    try {
        const validatedBody = createTransactionSchema.parse(request.body);
        controllerLogger.info({ amount: validatedBody.amount, status: validatedBody.status }, 'Creating transaction');
        const transaction = await transactionService_1.transactionService.createTransaction(validatedBody);
        const receipt = (0, receiptService_1.generateReceipt)(transaction);
        return reply.status(201).send({
            success: true,
            data: {
                transaction,
                receipt
            }
        });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            controllerLogger.warn({ error: error.errors }, 'Invalid transaction request');
            return reply.status(400).send({
                success: false,
                error: 'VALIDATION_ERROR',
                message: 'Invalid request parameters',
                details: error.errors
            });
        }
        controllerLogger.error({ error }, 'Failed to create transaction');
        return reply.status(500).send({
            success: false,
            error: 'TRANSACTION_CREATE_ERROR',
            message: 'Failed to create transaction'
        });
    }
};
exports.createTxn = createTxn;
const listTxns = async (request, reply) => {
    try {
        const validatedQuery = listTransactionsSchema.parse(request.query);
        const limit = validatedQuery.limit || 50;
        const offset = validatedQuery.offset || 0;
        controllerLogger.info({ limit, offset }, 'Fetching transactions');
        const transactions = await transactionService_1.transactionService.getTransactions(limit, offset);
        return reply.send({
            success: true,
            data: {
                transactions,
                pagination: {
                    limit,
                    offset,
                    count: transactions.length
                }
            }
        });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            controllerLogger.warn({ error: error.errors }, 'Invalid list transactions request');
            return reply.status(400).send({
                success: false,
                error: 'VALIDATION_ERROR',
                message: 'Invalid query parameters',
                details: error.errors
            });
        }
        controllerLogger.error({ error }, 'Failed to fetch transactions');
        return reply.status(500).send({
            success: false,
            error: 'TRANSACTION_FETCH_ERROR',
            message: 'Failed to fetch transactions'
        });
    }
};
exports.listTxns = listTxns;
const getTxnById = async (request, reply) => {
    try {
        const { id } = request.params;
        if (!id) {
            return reply.status(400).send({
                success: false,
                error: 'VALIDATION_ERROR',
                message: 'Transaction ID is required'
            });
        }
        controllerLogger.info({ transactionId: id }, 'Fetching transaction by ID');
        const transaction = await transactionService_1.transactionService.getTransactionById(id);
        if (!transaction) {
            return reply.status(404).send({
                success: false,
                error: 'NOT_FOUND',
                message: 'Transaction not found'
            });
        }
        return reply.send({
            success: true,
            data: transaction
        });
    }
    catch (error) {
        controllerLogger.error({ error }, 'Failed to fetch transaction by ID');
        return reply.status(500).send({
            success: false,
            error: 'TRANSACTION_FETCH_ERROR',
            message: 'Failed to fetch transaction'
        });
    }
};
exports.getTxnById = getTxnById;
//# sourceMappingURL=transactionController.js.map