"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.stripeService = exports.StripeService = void 0;
const stripe_1 = __importDefault(require("stripe"));
const env_1 = require("../config/env");
const logger_1 = require("../config/logger");
const zod_1 = require("zod");
const stripeLogger = logger_1.logger.child({ module: 'stripe' });
const stripe = new stripe_1.default(env_1.env.STRIPE_SECRET_KEY, {
    apiVersion: '2024-04-10',
    typescript: true,
});
const createPaymentIntentSchema = zod_1.z.object({
    amount: zod_1.z.number().positive().int(),
    currency: zod_1.z.string().length(3),
    payment_method_types: zod_1.z.array(zod_1.z.string()).optional().default(['card']),
    capture_method: zod_1.z.enum(['automatic', 'manual']).optional().default('automatic'),
    metadata: zod_1.z.record(zod_1.z.string()).optional(),
});
const capturePaymentIntentSchema = zod_1.z.object({
    paymentIntentId: zod_1.z.string().min(1),
    amountToCapture: zod_1.z.number().positive().int().optional(),
});
class StripeService {
    async createConnectionToken() {
        try {
            stripeLogger.info('Creating Stripe Terminal connection token');
            const token = await stripe.terminal.connectionTokens.create();
            stripeLogger.info('Connection token created successfully');
            return token;
        }
        catch (error) {
            stripeLogger.error({ error }, 'Failed to create connection token');
            throw new Error('Failed to create Stripe connection token');
        }
    }
    async createPaymentIntent(params) {
        try {
            const validatedParams = createPaymentIntentSchema.parse(params);
            stripeLogger.info({ amount: validatedParams.amount, currency: validatedParams.currency }, 'Creating payment intent');
            const paymentIntent = await stripe.paymentIntents.create({
                amount: validatedParams.amount,
                currency: validatedParams.currency,
                payment_method_types: validatedParams.payment_method_types,
                capture_method: validatedParams.capture_method,
                metadata: validatedParams.metadata || {},
            });
            stripeLogger.info({ paymentIntentId: paymentIntent.id }, 'Payment intent created successfully');
            return paymentIntent;
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                stripeLogger.error({ error: error.errors }, 'Invalid payment intent parameters');
                throw new Error('Invalid payment intent parameters');
            }
            stripeLogger.error({ error }, 'Failed to create payment intent');
            throw new Error('Failed to create payment intent');
        }
    }
    async capturePaymentIntent(params) {
        try {
            const validatedParams = capturePaymentIntentSchema.parse(params);
            stripeLogger.info({ paymentIntentId: validatedParams.paymentIntentId }, 'Capturing payment intent');
            const captureParams = {};
            if (validatedParams.amountToCapture) {
                captureParams.amount_to_capture = validatedParams.amountToCapture;
            }
            const paymentIntent = await stripe.paymentIntents.capture(validatedParams.paymentIntentId, captureParams);
            stripeLogger.info({ paymentIntentId: paymentIntent.id }, 'Payment intent captured successfully');
            return paymentIntent;
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                stripeLogger.error({ error: error.errors }, 'Invalid capture parameters');
                throw new Error('Invalid capture parameters');
            }
            stripeLogger.error({ error }, 'Failed to capture payment intent');
            throw new Error('Failed to capture payment intent');
        }
    }
    async retrievePaymentIntent(paymentIntentId) {
        try {
            stripeLogger.info({ paymentIntentId }, 'Retrieving payment intent');
            const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
            return paymentIntent;
        }
        catch (error) {
            stripeLogger.error({ error, paymentIntentId }, 'Failed to retrieve payment intent');
            throw new Error('Failed to retrieve payment intent');
        }
    }
}
exports.StripeService = StripeService;
exports.stripeService = new StripeService();
//# sourceMappingURL=stripeService.js.map