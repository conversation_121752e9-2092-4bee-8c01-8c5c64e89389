"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLogs = exports.addLog = void 0;
const logs = [];
const addLog = (type, message) => {
    const entry = {
        type,
        message,
        timestamp: new Date().toISOString(),
    };
    logs.push(entry);
    return entry;
};
exports.addLog = addLog;
const getLogs = () => logs;
exports.getLogs = getLogs;
//# sourceMappingURL=logService.js.map