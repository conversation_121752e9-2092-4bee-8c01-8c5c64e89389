{"version": 3, "file": "logger.js", "sourceRoot": "", "sources": ["../../src/lib/logger.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAwB;AACxB,uCAAoC;AAEpC,MAAM,WAAW,GAAuB;IACtC,KAAK,EAAE,SAAG,CAAC,SAAS,IAAI,MAAM;CAC/B,CAAC;AAEF,+DAA+D;AAC/D,IAAI,SAAG,CAAC,QAAQ,KAAK,YAAY,IAAI,SAAG,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;IAC7D,WAAW,CAAC,SAAS,GAAG;QACtB,MAAM,EAAE,aAAa;QACrB,OAAO,EAAE;YACP,QAAQ,EAAE,IAAI;YACd,aAAa,EAAE,cAAc,EAAE,0BAA0B;YACzD,MAAM,EAAE,cAAc,EAAE,6CAA6C;SACtE;KACF,CAAC;AACJ,CAAC;AAEY,QAAA,MAAM,GAAG,IAAA,cAAI,EAAC,WAAW,CAAC,CAAC;AAExC,mEAAmE;AACnE,6EAA6E;AAE7E,+CAA+C;AAC/C,kBAAe,cAAM,CAAC"}