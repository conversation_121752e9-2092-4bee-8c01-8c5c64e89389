import { ITransaction } from '../models/Transaction.mongo';
import { z } from 'zod';
declare const createTransactionSchema: z.ZodObject<{
    amount: z.ZodNumber;
    status: z.ZodEnum<["success", "failure", "pending"]>;
    protocolCode: z.ZodOptional<z.ZodString>;
    stripePaymentIntentId: z.ZodOptional<z.ZodString>;
    receiptUrl: z.ZodOptional<z.ZodString>;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    status?: "success" | "failure" | "pending";
    amount?: number;
    protocolCode?: string;
    stripePaymentIntentId?: string;
    receiptUrl?: string;
    metadata?: Record<string, any>;
}, {
    status?: "success" | "failure" | "pending";
    amount?: number;
    protocolCode?: string;
    stripePaymentIntentId?: string;
    receiptUrl?: string;
    metadata?: Record<string, any>;
}>;
export declare class TransactionService {
    createTransaction(data: z.infer<typeof createTransactionSchema>): Promise<ITransaction>;
    getTransactions(limit?: number, offset?: number): Promise<ITransaction[]>;
    getTransactionById(id: string): Promise<ITransaction | null>;
    updateTransaction(id: string, updates: Partial<ITransaction>): Promise<ITransaction | null>;
}
export declare const transactionService: TransactionService;
export {};
//# sourceMappingURL=transactionService.d.ts.map