"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.realTimePaymentController = void 0;
const stripe_1 = __importDefault(require("stripe"));
const Transaction_mongo_1 = __importDefault(require("../models/Transaction.mongo"));
const receiptService_1 = require("../services/receiptService");
const logger_1 = require("../config/logger");
const env_1 = require("../config/env");
const dynamic_1 = __importDefault(require("../config/dynamic"));
// Initialize Stripe with dynamic configuration
const stripe = new stripe_1.default(env_1.env.STRIPE_SECRET_KEY, {
    apiVersion: '2024-04-10',
});
exports.realTimePaymentController = {
    // Process real-time payment
    async processPayment(request, reply) {
        try {
            // Get dynamic configuration
            const paymentConfig = dynamic_1.default.getPaymentConfig();
            // const terminalInfo = ConfigManager.getTerminalInfo();
            const { amount, currency = paymentConfig.currency, payment_method_types = ['card'], confirm = false, return_url, customer_email, description } = request.body;
            // Validate amount using dynamic configuration
            const amountValidation = dynamic_1.default.validateAmount(amount);
            if (!amountValidation.valid) {
                return reply.status(400).send({
                    success: false,
                    error: amountValidation.error
                });
            }
            // Validate currency
            if (!dynamic_1.default.validateCurrency(currency)) {
                return reply.status(400).send({
                    success: false,
                    error: `Unsupported currency: ${currency}`
                });
            }
            // Generate dynamic metadata
            const metadata = dynamic_1.default.generateTransactionMetadata({
                source: 'pax_a920_pro_pos',
                customer_email,
                description
            });
            // Create payment intent with dynamic configuration
            const paymentIntentParams = {
                amount,
                currency,
                payment_method_types,
                confirm,
                metadata
            };
            if (return_url) {
                paymentIntentParams.return_url = return_url;
            }
            if (customer_email) {
                paymentIntentParams.receipt_email = customer_email;
            }
            if (description) {
                paymentIntentParams.description = description;
            }
            const paymentIntent = await stripe.paymentIntents.create(paymentIntentParams);
            // Create transaction record
            const transaction = new Transaction_mongo_1.default({
                amount,
                currency,
                status: paymentIntent.status === 'succeeded' ? 'success' : 'pending',
                stripePaymentIntentId: paymentIntent.id,
                paymentMethod: 'card',
                metadata: {
                    stripe_payment_intent: paymentIntent.id,
                    stripe_status: paymentIntent.status,
                    customer_email,
                    description
                }
            });
            await transaction.save();
            logger_1.logger.info(`Payment intent created: ${paymentIntent.id} for amount: ${amount}`);
            // If payment is confirmed and succeeded, generate receipt
            let receiptData = null;
            if (paymentIntent.status === 'succeeded') {
                try {
                    receiptData = {
                        customerReceipt: receiptService_1.ReceiptService.generateCustomerReceipt(transaction),
                        merchantReceipt: receiptService_1.ReceiptService.generateMerchantReceipt(transaction)
                    };
                }
                catch (receiptError) {
                    logger_1.logger.error('Receipt generation failed:', receiptError);
                }
            }
            return reply.send({
                success: true,
                data: {
                    id: paymentIntent.id,
                    status: paymentIntent.status,
                    amount: paymentIntent.amount,
                    currency: paymentIntent.currency,
                    client_secret: paymentIntent.client_secret,
                    transaction_id: transaction._id,
                    payment_method: paymentIntent.payment_method,
                    receipt: receiptData
                }
            });
        }
        catch (error) {
            logger_1.logger.error('Payment processing error:', error);
            if (error instanceof stripe_1.default.errors.StripeError) {
                return reply.status(400).send({
                    success: false,
                    error: error.message,
                    type: error.type,
                    code: error.code
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'Payment processing failed'
            });
        }
    },
    // Confirm payment (for card present transactions)
    async confirmPayment(request, reply) {
        try {
            const { payment_intent_id, payment_method } = request.body;
            const confirmParams = {};
            if (payment_method) {
                confirmParams.payment_method = payment_method;
            }
            const paymentIntent = await stripe.paymentIntents.confirm(payment_intent_id, confirmParams);
            // Update transaction record
            const transaction = await Transaction_mongo_1.default.findOne({
                stripePaymentIntentId: payment_intent_id
            });
            if (transaction) {
                transaction.status = paymentIntent.status === 'succeeded' ? 'success' : 'failed';
                transaction.metadata = {
                    ...transaction.metadata,
                    stripe_status: paymentIntent.status,
                    confirmed_at: new Date().toISOString()
                };
                await transaction.save();
            }
            logger_1.logger.info(`Payment confirmed: ${payment_intent_id} - Status: ${paymentIntent.status}`);
            return reply.send({
                success: true,
                data: {
                    id: paymentIntent.id,
                    status: paymentIntent.status,
                    amount: paymentIntent.amount,
                    currency: paymentIntent.currency,
                    payment_method: paymentIntent.payment_method
                }
            });
        }
        catch (error) {
            logger_1.logger.error('Payment confirmation error:', error);
            if (error instanceof stripe_1.default.errors.StripeError) {
                return reply.status(400).send({
                    success: false,
                    error: error.message,
                    type: error.type
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'Payment confirmation failed'
            });
        }
    },
    // Refund payment
    async refundPayment(request, reply) {
        try {
            const { payment_intent_id, amount, reason = 'requested_by_customer' } = request.body;
            const refundParams = {
                payment_intent: payment_intent_id,
                reason
            };
            if (amount) {
                refundParams.amount = amount;
            }
            const refund = await stripe.refunds.create(refundParams);
            // Update transaction record
            const transaction = await Transaction_mongo_1.default.findOne({
                stripePaymentIntentId: payment_intent_id
            });
            if (transaction) {
                transaction.status = 'refunded';
                transaction.metadata = {
                    ...transaction.metadata,
                    refund_id: refund.id,
                    refund_amount: refund.amount,
                    refund_reason: reason,
                    refunded_at: new Date().toISOString()
                };
                await transaction.save();
            }
            logger_1.logger.info(`Refund processed: ${refund.id} for payment: ${payment_intent_id}`);
            return reply.send({
                success: true,
                data: {
                    refund_id: refund.id,
                    amount: refund.amount,
                    currency: refund.currency,
                    status: refund.status,
                    reason: refund.reason
                }
            });
        }
        catch (error) {
            logger_1.logger.error('Refund processing error:', error);
            if (error instanceof stripe_1.default.errors.StripeError) {
                return reply.status(400).send({
                    success: false,
                    error: error.message,
                    type: error.type
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'Refund processing failed'
            });
        }
    },
    // Get payment status
    async getPaymentStatus(request, reply) {
        try {
            const { payment_intent_id } = request.params;
            const paymentIntent = await stripe.paymentIntents.retrieve(payment_intent_id);
            const transaction = await Transaction_mongo_1.default.findOne({
                stripePaymentIntentId: payment_intent_id
            });
            return reply.send({
                success: true,
                data: {
                    payment_intent: {
                        id: paymentIntent.id,
                        status: paymentIntent.status,
                        amount: paymentIntent.amount,
                        currency: paymentIntent.currency,
                        payment_method: paymentIntent.payment_method
                    },
                    transaction: transaction ? {
                        id: transaction._id,
                        status: transaction.status,
                        created_at: transaction.createdAt
                    } : null
                }
            });
        }
        catch (error) {
            logger_1.logger.error('Payment status retrieval error:', error);
            if (error instanceof stripe_1.default.errors.StripeError) {
                return reply.status(400).send({
                    success: false,
                    error: error.message,
                    type: error.type
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'Failed to retrieve payment status'
            });
        }
    },
    // Cancel payment intent
    async cancelPayment(request, reply) {
        try {
            const { payment_intent_id } = request.body;
            const paymentIntent = await stripe.paymentIntents.cancel(payment_intent_id);
            // Update transaction record
            const transaction = await Transaction_mongo_1.default.findOne({
                stripePaymentIntentId: payment_intent_id
            });
            if (transaction) {
                transaction.status = 'cancelled';
                transaction.metadata = {
                    ...transaction.metadata,
                    cancelled_at: new Date().toISOString()
                };
                await transaction.save();
            }
            logger_1.logger.info(`Payment cancelled: ${payment_intent_id}`);
            return reply.send({
                success: true,
                data: {
                    id: paymentIntent.id,
                    status: paymentIntent.status
                }
            });
        }
        catch (error) {
            logger_1.logger.error('Payment cancellation error:', error);
            if (error instanceof stripe_1.default.errors.StripeError) {
                return reply.status(400).send({
                    success: false,
                    error: error.message,
                    type: error.type
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'Payment cancellation failed'
            });
        }
    }
};
//# sourceMappingURL=realTimePaymentController.js.map