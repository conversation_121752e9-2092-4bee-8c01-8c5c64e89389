"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const app_1 = require("./app");
const logger_1 = require("./config/logger");
process.on('unhandledRejection', (reason, promise) => {
    logger_1.logger.error({ reason, promise }, 'Unhandled Rejection');
    process.exit(1);
});
process.on('uncaughtException', (error) => {
    logger_1.logger.error({ error }, 'Uncaught Exception');
    process.exit(1);
});
const gracefulShutdown = (signal) => {
    logger_1.logger.info(`Received ${signal}, shutting down gracefully`);
    process.exit(0);
};
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
(0, app_1.startServer)().catch((error) => {
    logger_1.logger.error({ error }, 'Failed to start server');
    process.exit(1);
});
//# sourceMappingURL=server.js.map