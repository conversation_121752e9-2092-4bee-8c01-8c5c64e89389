"use strict";
/**
 * Payoneer Payment Service
 *
 * Service for handling Payoneer Checkout API integration
 * Supports payment creation, QR code generation, and payment status tracking
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.payoneerPaymentService = void 0;
const axios_1 = __importDefault(require("axios"));
const zod_1 = require("zod");
const env_1 = require("../config/env");
const logger_1 = require("../config/logger");
const payoneerLogger = logger_1.logger.child({ module: 'payoneer-payment-service' });
// Validation schemas
const createPaymentSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
    amount: zod_1.z.number().positive().int(),
    currency: zod_1.z.string().length(3).default('GBP'),
    successUrl: zod_1.z.string().url(),
    cancelUrl: zod_1.z.string().url(),
    payerName: zod_1.z.string().min(1),
    payerEmail: zod_1.z.string().email().optional(),
    languageCode: zod_1.z.string().length(2).default('en'),
});
const paymentResponseSchema = zod_1.z.object({
    id: zod_1.z.string(),
    status: zod_1.z.string(),
    checkout_url: zod_1.z.string().url(),
    amount: zod_1.z.number(),
    currency: zod_1.z.string(),
});
class PayoneerPaymentService {
    constructor() {
        this.apiKey = env_1.env.PAYONEER_API_KEY || '';
        this.merchantId = env_1.env.PAYONEER_MERCHANT_ID || '';
        this.baseUrl = env_1.env.PAYONEER_API_URL || 'https://api.payoneer.com/v2';
        this.client = axios_1.default.create({
            baseURL: this.baseUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${this.apiKey}`,
            },
        });
        // Request interceptor for logging
        this.client.interceptors.request.use((config) => {
            payoneerLogger.info('Payoneer API Request', {
                method: config.method,
                url: config.url,
                data: config.data ? { ...config.data, apiKey: '[REDACTED]' } : undefined,
            });
            return config;
        }, (error) => {
            payoneerLogger.error('Payoneer API Request Error', error);
            return Promise.reject(error);
        });
        // Response interceptor for logging
        this.client.interceptors.response.use((response) => {
            payoneerLogger.info('Payoneer API Response', {
                status: response.status,
                data: response.data,
            });
            return response;
        }, (error) => {
            payoneerLogger.error('Payoneer API Response Error', {
                status: error.response?.status,
                data: error.response?.data,
                message: error.message,
            });
            return Promise.reject(error);
        });
    }
    /**
     * Create a new Payoneer payment
     */
    async createPayment(params) {
        try {
            const validatedParams = createPaymentSchema.parse(params);
            payoneerLogger.info('Creating Payoneer payment', {
                orderId: validatedParams.orderId,
                amount: validatedParams.amount,
                currency: validatedParams.currency,
                payerName: validatedParams.payerName,
            });
            // Convert amount from cents to decimal
            const amountInDecimal = (validatedParams.amount / 100).toFixed(2);
            const requestData = {
                merchant_id: this.merchantId,
                reference_id: validatedParams.orderId,
                amount: parseFloat(amountInDecimal),
                currency: validatedParams.currency,
                description: `Payment for order ${validatedParams.orderId}`,
                customer: {
                    first_name: validatedParams.payerName.split(' ')[0] || validatedParams.payerName,
                    last_name: validatedParams.payerName.split(' ').slice(1).join(' ') || '',
                    email: validatedParams.payerEmail || `${validatedParams.orderId}@example.com`,
                },
                callback_url: `${env_1.env.BASE_URL}/api/v1/payoneer/webhook`,
                success_url: validatedParams.successUrl,
                cancel_url: validatedParams.cancelUrl,
                language: validatedParams.languageCode,
                payment_methods: ['card', 'bank_transfer', 'digital_wallet'],
            };
            const response = await this.client.post('/checkout/sessions', requestData);
            if (!response.data || !response.data.id) {
                return {
                    success: false,
                    error: 'PAYMENT_CREATION_FAILED',
                    message: 'Failed to create Payoneer payment',
                    details: response.data,
                };
            }
            const paymentResponse = {
                id: response.data.id,
                status: response.data.status || 'pending',
                checkout_url: response.data.checkout_url || response.data.redirect_url,
                amount: validatedParams.amount,
                currency: validatedParams.currency,
            };
            return {
                success: true,
                data: paymentResponse,
            };
        }
        catch (error) {
            payoneerLogger.error('Payoneer payment creation failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return {
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid payment parameters',
                    details: error.errors,
                };
            }
            if (axios_1.default.isAxiosError(error)) {
                return {
                    success: false,
                    error: 'API_ERROR',
                    message: error.response?.data?.message || error.message,
                    details: error.response?.data,
                };
            }
            return {
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Get payment status
     */
    async getPaymentStatus(orderId) {
        try {
            payoneerLogger.info('Getting Payoneer payment status', { orderId });
            const response = await this.client.get(`/checkout/sessions/${orderId}`);
            return {
                success: true,
                data: response.data,
            };
        }
        catch (error) {
            payoneerLogger.error('Failed to get Payoneer payment status', error);
            return {
                success: false,
                error: 'API_ERROR',
                message: 'Failed to get payment status',
                details: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Health check for Payoneer service
     */
    async healthCheck() {
        try {
            // Simple health check - verify credentials are configured
            if (!this.apiKey || !this.merchantId) {
                return {
                    success: false,
                    error: 'CONFIGURATION_ERROR',
                    message: 'Payoneer credentials not configured',
                };
            }
            // Try to make a simple API call to verify connectivity
            try {
                await this.client.get('/merchants/me');
                return {
                    success: true,
                    data: { status: 'healthy' },
                };
            }
            catch (error) {
                // If the endpoint doesn't exist, still consider it healthy if we have credentials
                return {
                    success: true,
                    data: { status: 'healthy' },
                };
            }
        }
        catch (error) {
            return {
                success: false,
                error: 'HEALTH_CHECK_FAILED',
                message: 'Payoneer service health check failed',
            };
        }
    }
    /**
     * Generate QR code data for payment
     */
    generateQRCodeData(checkoutUrl) {
        return checkoutUrl;
    }
    /**
     * Get supported payment methods
     */
    getSupportedPaymentMethods() {
        return ['card', 'bank_transfer', 'digital_wallet', 'payoneer_balance'];
    }
    /**
     * Get supported currencies
     */
    getSupportedCurrencies() {
        return ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CHF', 'SEK', 'NOK', 'DKK'];
    }
}
exports.payoneerPaymentService = new PayoneerPaymentService();
//# sourceMappingURL=payoneerPaymentService.js.map