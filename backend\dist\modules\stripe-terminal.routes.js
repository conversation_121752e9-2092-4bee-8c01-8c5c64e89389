"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = stripeTerminalRoutes;
const stripeTerminalService_1 = require("../services/stripeTerminalService");
const logger_1 = require("../config/logger");
const Transaction_mongo_1 = __importDefault(require("../models/Transaction.mongo"));
const terminalLogger = logger_1.logger.child({ module: 'stripe-terminal-routes' });
async function stripeTerminalRoutes(fastify) {
    // Create connection token for terminal SDK
    fastify.post('/terminal/connection-token', async (_, reply) => {
        try {
            terminalLogger.info('Creating connection token');
            // Ensure location exists
            await stripeTerminalService_1.stripeTerminalService.ensureLocation();
            const connectionToken = await stripeTerminalService_1.stripeTerminalService.createConnectionToken();
            reply.send({
                success: true,
                data: connectionToken,
            });
        }
        catch (error) {
            terminalLogger.error('Failed to create connection token', { error });
            reply.status(500).send({
                success: false,
                error: 'Failed to create connection token',
            });
        }
    });
    // List terminal readers
    fastify.get('/terminal/readers', async (_, reply) => {
        try {
            terminalLogger.info('Listing terminal readers');
            const readers = await stripeTerminalService_1.stripeTerminalService.listReaders();
            reply.send({
                success: true,
                data: readers,
            });
        }
        catch (error) {
            terminalLogger.error('Failed to list readers', { error });
            reply.status(500).send({
                success: false,
                error: 'Failed to list terminal readers',
            });
        }
    });
    // Get specific terminal reader
    fastify.get('/terminal/readers/:readerId', async (request, reply) => {
        try {
            const { readerId } = request.params;
            terminalLogger.info('Getting terminal reader', { readerId });
            const reader = await stripeTerminalService_1.stripeTerminalService.getReader(readerId);
            reply.send({
                success: true,
                data: reader,
            });
        }
        catch (error) {
            terminalLogger.error('Failed to get reader', { error });
            reply.status(500).send({
                success: false,
                error: 'Failed to get terminal reader',
            });
        }
    });
    // Create payment intent for terminal
    fastify.post('/terminal/payment-intents', async (request, reply) => {
        try {
            const { amount, currency = 'usd', capture_method = 'automatic', metadata } = request.body;
            terminalLogger.info('Creating payment intent for terminal', { amount, currency });
            // Validate amount
            if (!amount || amount <= 0) {
                return reply.status(400).send({
                    success: false,
                    error: 'Invalid amount',
                });
            }
            const paymentIntent = await stripeTerminalService_1.stripeTerminalService.createPaymentIntent({
                amount,
                currency,
                payment_method_types: ['card_present'],
                capture_method,
                metadata,
            });
            // Create transaction record
            const transaction = new Transaction_mongo_1.default({
                id: `TXN_${Date.now()}`,
                amount,
                currency,
                status: 'pending',
                paymentIntentId: paymentIntent.id,
                metadata: {
                    source: 'stripe_terminal',
                    ...metadata,
                },
            });
            await transaction.save();
            reply.send({
                success: true,
                data: {
                    payment_intent: paymentIntent,
                    transaction_id: transaction.id,
                },
            });
        }
        catch (error) {
            terminalLogger.error('Failed to create payment intent', { error });
            reply.status(500).send({
                success: false,
                error: 'Failed to create payment intent',
            });
        }
    });
    // Process payment on terminal
    fastify.post('/terminal/process-payment', async (request, reply) => {
        try {
            const { payment_intent_id, reader_id } = request.body;
            terminalLogger.info('Processing payment on terminal', { payment_intent_id, reader_id });
            if (!payment_intent_id || !reader_id) {
                return reply.status(400).send({
                    success: false,
                    error: 'Payment intent ID and reader ID are required',
                });
            }
            const paymentIntent = await stripeTerminalService_1.stripeTerminalService.processPayment(reader_id, payment_intent_id);
            // Update transaction record
            await Transaction_mongo_1.default.findOneAndUpdate({ paymentIntentId: payment_intent_id }, {
                status: paymentIntent.status === 'succeeded' ? 'success' : 'failed',
                updatedAt: new Date(),
            });
            reply.send({
                success: true,
                data: paymentIntent,
            });
        }
        catch (error) {
            terminalLogger.error('Failed to process payment', { error });
            // Update transaction as failed
            if (request.body.payment_intent_id) {
                await Transaction_mongo_1.default.findOneAndUpdate({ paymentIntentId: request.body.payment_intent_id }, {
                    status: 'failed',
                    updatedAt: new Date(),
                });
            }
            reply.status(500).send({
                success: false,
                error: 'Failed to process payment',
            });
        }
    });
    // Simulate payment (for testing without physical terminal)
    fastify.post('/terminal/simulate-payment', async (request, reply) => {
        try {
            const { amount, currency = 'usd', metadata } = request.body;
            terminalLogger.info('Simulating payment', { amount, currency });
            if (!amount || amount <= 0) {
                return reply.status(400).send({
                    success: false,
                    error: 'Invalid amount',
                });
            }
            const paymentIntent = await stripeTerminalService_1.stripeTerminalService.simulatePayment({
                amount,
                currency,
                payment_method_types: ['card'],
                capture_method: 'automatic',
                metadata,
            });
            // Create transaction record
            const transaction = new Transaction_mongo_1.default({
                id: `TXN_${Date.now()}`,
                amount,
                currency,
                status: paymentIntent.status === 'succeeded' ? 'success' : 'failed',
                paymentIntentId: paymentIntent.id,
                metadata: {
                    source: 'stripe_terminal_simulation',
                    ...metadata,
                },
            });
            await transaction.save();
            reply.send({
                success: true,
                data: {
                    payment_intent: paymentIntent,
                    transaction_id: transaction.id,
                },
            });
        }
        catch (error) {
            terminalLogger.error('Failed to simulate payment', { error });
            reply.status(500).send({
                success: false,
                error: 'Failed to simulate payment',
            });
        }
    });
    // Cancel payment intent
    fastify.post('/terminal/cancel-payment/:paymentIntentId', async (request, reply) => {
        try {
            const { paymentIntentId } = request.params;
            terminalLogger.info('Cancelling payment intent', { paymentIntentId });
            const paymentIntent = await stripeTerminalService_1.stripeTerminalService.cancelPaymentIntent(paymentIntentId);
            // Update transaction record
            await Transaction_mongo_1.default.findOneAndUpdate({ paymentIntentId }, {
                status: 'cancelled',
                updatedAt: new Date(),
            });
            reply.send({
                success: true,
                data: paymentIntent,
            });
        }
        catch (error) {
            terminalLogger.error('Failed to cancel payment intent', { error });
            reply.status(500).send({
                success: false,
                error: 'Failed to cancel payment intent',
            });
        }
    });
    // Create refund
    fastify.post('/terminal/refunds/:paymentIntentId', async (request, reply) => {
        try {
            const { paymentIntentId } = request.params;
            const { amount, reason } = request.body;
            terminalLogger.info('Creating refund', { paymentIntentId, amount, reason });
            const refund = await stripeTerminalService_1.stripeTerminalService.createRefund(paymentIntentId, amount);
            // Update transaction record
            await Transaction_mongo_1.default.findOneAndUpdate({ paymentIntentId }, {
                status: 'refunded',
                updatedAt: new Date(),
            });
            reply.send({
                success: true,
                data: refund,
            });
        }
        catch (error) {
            terminalLogger.error('Failed to create refund', { error });
            reply.status(500).send({
                success: false,
                error: 'Failed to create refund',
            });
        }
    });
    // Get reader status
    fastify.get('/terminal/readers/:readerId/status', async (request, reply) => {
        try {
            const { readerId } = request.params;
            const status = await stripeTerminalService_1.stripeTerminalService.getReaderStatus(readerId);
            reply.send({
                success: true,
                data: status,
            });
        }
        catch (error) {
            terminalLogger.error('Failed to get reader status', { error });
            reply.status(500).send({
                success: false,
                error: 'Failed to get reader status',
            });
        }
    });
    // Get payment method details
    fastify.get('/terminal/payment-intents/:paymentIntentId/payment-method', async (request, reply) => {
        try {
            const { paymentIntentId } = request.params;
            const paymentMethodDetails = await stripeTerminalService_1.stripeTerminalService.getPaymentMethodDetails(paymentIntentId);
            reply.send({
                success: true,
                data: paymentMethodDetails,
            });
        }
        catch (error) {
            terminalLogger.error('Failed to get payment method details', { error });
            reply.status(500).send({
                success: false,
                error: 'Failed to get payment method details',
            });
        }
    });
}
//# sourceMappingURL=stripe-terminal.routes.js.map