"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = default_1;
const stripeController_1 = require("../controllers/stripeController");
async function default_1(app) {
    app.post('/stripe/connection_token', stripeController_1.getConnectionToken);
    app.post('/stripe/payment_intent', stripeController_1.createIntent);
    app.post('/stripe/capture_intent', stripeController_1.captureIntent);
}
//# sourceMappingURL=stripe.routes.js.map