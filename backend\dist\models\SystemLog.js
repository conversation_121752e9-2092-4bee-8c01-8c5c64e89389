"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemLog = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const systemLogSchema = new mongoose_1.Schema({
    timestamp: {
        type: String,
        required: true,
        default: () => new Date().toISOString()
    },
    level: {
        type: String,
        required: true,
        enum: ['info', 'warn', 'error', 'debug']
    },
    category: {
        type: String,
        required: true,
        enum: ['payment', 'hardware', 'protocol', 'system', 'security']
    },
    message: {
        type: String,
        required: true
    },
    details: {
        type: mongoose_1.Schema.Types.Mixed,
        required: false
    },
    userId: {
        type: String,
        required: false
    },
    sessionId: {
        type: String,
        required: false
    }
}, {
    timestamps: true,
    collection: 'systemlogs'
});
// Index for efficient querying
systemLogSchema.index({ timestamp: -1 });
systemLogSchema.index({ level: 1 });
systemLogSchema.index({ category: 1 });
systemLogSchema.index({ userId: 1 });
systemLogSchema.index({ sessionId: 1 });
exports.SystemLog = mongoose_1.default.model('SystemLog', systemLogSchema);
//# sourceMappingURL=SystemLog.js.map