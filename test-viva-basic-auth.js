/**
 * Test Viva Wallet Basic Auth for Payment Creation
 * 
 * Test if we can create payments using Basic Auth instead of OAuth2
 * Run with: node test-viva-basic-auth.js
 */

const https = require('https');

// Your Viva Wallet credentials
const VIVA_MERCHANT_ID = '30481af3-63d9-42cd-93ea-1937a972b76d';
const VIVA_API_KEY = 'SothunZ2FxVRMkq666sbxbxB6VNbJG';

// Demo environment URLs
const API_URL = 'demo-api.vivapayments.com';

console.log('🔐 Testing Viva Wallet Basic Auth for Payment Creation...\n');

// Test Basic Auth for Payment Creation
function testBasicAuthPaymentCreation() {
  return new Promise((resolve, reject) => {
    console.log('📝 Testing Payment Creation with Basic Auth');
    console.log('Merchant ID:', VIVA_MERCHANT_ID);
    console.log('API Key:', VIVA_API_KEY.substring(0, 10) + '...');
    console.log('API URL:', API_URL);
    console.log('');

    // Create Basic Auth credentials
    const credentials = Buffer.from(`${VIVA_MERCHANT_ID}:${VIVA_API_KEY}`).toString('base64');
    console.log('Basic Auth Credentials:', credentials.substring(0, 50) + '...');

    // Prepare payment order data
    const paymentData = {
      amount: 100, // £1.00 in pence
      customerTrns: 'Test payment with Basic Auth',
      customer: {
        email: '<EMAIL>',
        fullName: 'Test Customer',
        requestLang: 'en-GB'
      },
      sourceCode: 'Default',
      merchantTrns: 'BASIC_AUTH_TEST_' + Date.now(),
      currencyCode: '826', // GBP currency code
      paymentTimeout: 1800
    };

    const postData = JSON.stringify(paymentData);

    // Request options
    const options = {
      hostname: API_URL,
      port: 443,
      path: '/checkout/v2/orders',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${credentials}`,
        'Accept': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log('Request Options:', JSON.stringify(options, null, 2));
    console.log('Payment Data:', JSON.stringify(paymentData, null, 2));
    console.log('');

    const req = https.request(options, (res) => {
      console.log(`✅ Response Status: ${res.statusCode}`);
      console.log('Response Headers:', JSON.stringify(res.headers, null, 2));
      console.log('');

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log('Raw Response:', data);
        console.log('');

        try {
          const response = JSON.parse(data);
          
          if (res.statusCode === 200 && response.orderCode) {
            console.log('🎉 Payment Creation with Basic Auth Successful!');
            console.log('Order Code:', response.orderCode);
            console.log('Checkout URL: https://demo.vivapayments.com/web/checkout?ref=' + response.orderCode);
            console.log('');
            resolve(response);
          } else if (res.statusCode === 401) {
            console.log('❌ Basic Auth Failed - Invalid credentials');
            console.log('Error Response:', response);
            resolve(null);
          } else if (res.statusCode === 403) {
            console.log('❌ Basic Auth Not Allowed for Payment Creation');
            console.log('OAuth2 is required for this endpoint');
            console.log('Error Response:', response);
            resolve(null);
          } else {
            console.log('❌ Payment Creation Failed');
            console.log('Error Response:', response);
            resolve(null);
          }
        } catch (error) {
          console.log('❌ Failed to parse response as JSON');
          console.log('Raw response:', data);
          resolve(null);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Request Error:', error.message);
      resolve(null);
    });

    req.write(postData);
    req.end();
  });
}

// Test alternative payment creation endpoints
function testAlternativePaymentEndpoints() {
  return new Promise((resolve, reject) => {
    console.log('📝 Testing Alternative Payment Endpoints');
    console.log('');

    // Create Basic Auth credentials
    const credentials = Buffer.from(`${VIVA_MERCHANT_ID}:${VIVA_API_KEY}`).toString('base64');

    // Try the older API endpoint
    const paymentData = {
      amount: 100,
      customerTrns: 'Test payment alternative endpoint',
      sourceCode: 'Default',
      merchantTrns: 'ALT_TEST_' + Date.now()
    };

    const postData = JSON.stringify(paymentData);

    // Request options for older endpoint
    const options = {
      hostname: API_URL,
      port: 443,
      path: '/api/orders', // Alternative endpoint
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${credentials}`,
        'Accept': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log('Trying alternative endpoint: /api/orders');
    console.log('Request Options:', JSON.stringify(options, null, 2));
    console.log('');

    const req = https.request(options, (res) => {
      console.log(`✅ Response Status: ${res.statusCode}`);
      console.log('Response Headers:', JSON.stringify(res.headers, null, 2));
      console.log('');

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log('Raw Response:', data);
        console.log('');

        if (res.statusCode === 200) {
          console.log('🎉 Alternative endpoint works!');
        } else if (res.statusCode === 404) {
          console.log('❌ Alternative endpoint not found');
        } else {
          console.log('❌ Alternative endpoint failed');
        }
        
        resolve(true);
      });
    });

    req.on('error', (error) => {
      console.log('❌ Request Error:', error.message);
      resolve(false);
    });

    req.write(postData);
    req.end();
  });
}

// Main execution
async function main() {
  try {
    console.log('🚀 Starting Viva Wallet Basic Auth Tests\n');
    
    // Test Basic Auth for payment creation
    const result = await testBasicAuthPaymentCreation();
    
    if (!result) {
      console.log('🔄 Trying alternative endpoints...\n');
      await testAlternativePaymentEndpoints();
    }
    
    console.log('\n📋 Summary:');
    console.log('- Basic Auth credentials are valid (confirmed from previous test)');
    console.log('- OAuth2 credentials need to be verified in Viva dashboard');
    console.log('- Check Settings > API Access > Smart Checkout Credentials');
    console.log('- Make sure the Client Secret is regenerated if needed');
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
main();
