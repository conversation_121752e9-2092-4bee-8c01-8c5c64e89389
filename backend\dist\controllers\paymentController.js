"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.simulatePayment = void 0;
const transactionService_1 = require("../services/transactionService");
const zod_1 = require("zod");
const logger_1 = require("../config/logger");
const axios_1 = __importDefault(require("axios"));
const env_1 = require("../config/env");
const controllerLogger = logger_1.logger.child({ module: 'payment-controller' });
const simulatePaymentSchema = zod_1.z.object({
    amount: zod_1.z.number().positive().int(),
    status: zod_1.z.enum(['success', 'failure']),
    protocolCode: zod_1.z.enum(['101.1', '101.3', '101.5', '101.8']).optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
});
const simulatePayment = async (request, reply) => {
    try {
        const validatedBody = simulatePaymentSchema.parse(request.body);
        controllerLogger.info({
            amount: validatedBody.amount,
            status: validatedBody.status,
            protocolCode: validatedBody.protocolCode
        }, 'Simulating payment');
        const transaction = await transactionService_1.transactionService.createTransaction({
            amount: validatedBody.amount,
            status: validatedBody.status,
            protocolCode: validatedBody.protocolCode,
            metadata: validatedBody.metadata,
        });
        if (validatedBody.protocolCode) {
            try {
                const bankMessage = {
                    protocolEventCode: validatedBody.protocolCode,
                    mti: '0200',
                    transactionId: String(transaction._id),
                    timestamp: new Date().toISOString(),
                    de4_transactionAmount: validatedBody.amount,
                    de39_responseCode: validatedBody.status === 'success' ? '00' : '05',
                };
                controllerLogger.info({ protocolCode: validatedBody.protocolCode }, 'Sending message to mock bank');
                const bankResponse = await axios_1.default.post(env_1.env.MOCK_BANK_URL, bankMessage, {
                    timeout: 5000,
                    headers: { 'Content-Type': 'application/json' }
                });
                controllerLogger.info({
                    bankResponseCode: bankResponse.data?.data?.bankResponseCode
                }, 'Received response from mock bank');
            }
            catch (bankError) {
                controllerLogger.warn({ error: bankError }, 'Failed to communicate with mock bank');
            }
        }
        return reply.status(201).send({
            success: true,
            data: {
                transaction,
                message: `Payment ${validatedBody.status} simulation completed`
            }
        });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            controllerLogger.warn({ error: error.errors }, 'Invalid payment simulation request');
            return reply.status(400).send({
                success: false,
                error: 'VALIDATION_ERROR',
                message: 'Invalid request parameters',
                details: error.errors
            });
        }
        controllerLogger.error({ error }, 'Failed to simulate payment');
        return reply.status(500).send({
            success: false,
            error: 'PAYMENT_SIMULATION_ERROR',
            message: 'Failed to simulate payment'
        });
    }
};
exports.simulatePayment = simulatePayment;
//# sourceMappingURL=paymentController.js.map