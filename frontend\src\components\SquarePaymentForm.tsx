import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { ExternalLink } from 'lucide-react';
import { useSquare, useSquareConfig } from '../hooks/useSquare';
import { generateOrderId } from '../utils/paymentUtils';
import { AndroidNumericKeypad } from './AndroidNumericKeypad';

// Square Web Payments SDK types
declare global {
  interface Window {
    Square?: any;
  }
}

interface SquarePaymentFormProps {
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
  className?: string;
}

const SquarePaymentForm: React.FC<SquarePaymentFormProps> = ({
  onSuccess,
  onError,
  className = '',
}) => {
  const [amount, setAmount] = useState<number>(0);
  const [amountInput, setAmountInput] = useState(''); // Start empty like Move form
  const [payerName, setPayerName] = useState('');
  const [payerEmail, setPayerEmail] = useState('');
  const [showOptions, setShowOptions] = useState(false);

  const { createPayment, isLoading } = useSquare();
  const { data: squareConfig } = useSquareConfig();

  // Get currency from backend configuration (USD for sandbox, EUR for production)
  const selectedCurrency = squareConfig?.currency || 'USD';

  const getSuccessUrl = () => `${window.location.origin}/payment/success`;
  const getCancelUrl = () => `${window.location.origin}/payment/cancel`;

  // Helper function to handle amount changes from keyboard
  const handleAmountChange = (value: string) => {
    setAmountInput(value);
    const numericValue = parseFloat(value);
    if (!isNaN(numericValue) && numericValue >= 0) {
      setAmount(Math.round(numericValue * 100)); // Convert to cents
    }
  };

  // Square now uses hosted checkout - no SDK initialization needed

  const handleGetPaymentURL = async () => {
    if (amount <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (!payerName.trim()) {
      toast.error('Please enter payer name');
      return;
    }

    try {
      const orderId = generateOrderId();
      const result = await createPayment({
        orderId,
        amount: amount,
        currency: selectedCurrency,
        payerName: payerName.trim(),
        payerEmail: payerEmail.trim() || undefined,
        successUrl: getSuccessUrl(),
        cancelUrl: getCancelUrl(),
        type: 'url',
      });

      if (result.success && result.data) {
        // Use robust redirect function
        performRedirect(result.data.redirect_url);
        toast.success('Redirecting to payment page...');
      } else {
        toast.error(result.message || 'Failed to generate payment URL');
      }
    } catch (error) {
      console.error('Payment URL generation error:', error);
      toast.error('Failed to generate payment URL');
    }
  };

  const handleMoreOptions = () => {
    setShowOptions(true);
  };

  // Robust redirect function that tries multiple methods
  const performRedirect = (url: string) => {
    console.log('Attempting redirect to:', url);

    // Method 1: Direct assignment (most reliable)
    try {
      window.location.assign(url);
      return;
    } catch (e1) {
      console.warn('Method 1 failed:', e1);
    }

    // Method 2: Replace current page
    try {
      window.location.replace(url);
      return;
    } catch (e2) {
      console.warn('Method 2 failed:', e2);
    }

    // Method 3: Standard href assignment
    try {
      window.location.href = url;
      return;
    } catch (e3) {
      console.warn('Method 3 failed:', e3);
    }

    // Method 4: Create and click a link element
    try {
      const link = document.createElement('a');
      link.href = url;
      link.target = '_self';
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      return;
    } catch (e4) {
      console.warn('Method 4 failed:', e4);
    }

    // Method 5: Force navigation using form submission
    try {
      const form = document.createElement('form');
      form.method = 'GET';
      form.action = url;
      form.target = '_self';
      form.style.display = 'none';
      document.body.appendChild(form);
      form.submit();
      document.body.removeChild(form);
      return;
    } catch (e5) {
      console.warn('Method 5 failed:', e5);
    }

    // If all methods fail, show the URL to the user
    console.error('All redirect methods failed. Showing URL to user.');
    toast.error('Automatic redirect failed. Please click the link below to complete payment.');

    // Create a visible link for the user to click
    const linkElement = document.createElement('a');
    linkElement.href = url;
    linkElement.target = '_blank';
    linkElement.textContent = 'Click here to complete payment';
    linkElement.style.cssText = 'display: block; margin: 10px; padding: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; text-align: center;';

    // Find a container to add the link
    const container = document.querySelector('.space-y-6') || document.body;
    container.appendChild(linkElement);
  };



  const handlePaymentUrl = async () => {
    try {
      const orderId = generateOrderId();
      const result = await createPayment({
        orderId,
        amount: amount,
        currency: selectedCurrency,
        payerName: payerName.trim(),
        payerEmail: payerEmail.trim() || undefined,
        successUrl: getSuccessUrl(),
        cancelUrl: getCancelUrl(),
        type: 'url',
      });

      if (result.success && result.data) {
        console.log('=== SQUARE PAYMENT URL GENERATED ===');
        console.log('Payment URL:', result.data.redirect_url);
        console.log('Payment ID:', result.data.payment_id);
        console.log('Order ID:', result.data.order_id);
        console.log('=== END SQUARE PAYMENT URL ===');

        // Use robust redirect function
        performRedirect(result.data.redirect_url);
        toast.success('Redirecting to payment page...');

        // Reset form state
        setShowOptions(false);
      } else {
        toast.error(result.message || 'Failed to generate payment URL');
      }
    } catch (error) {
      console.error('Payment URL generation error:', error);
      toast.error('Failed to generate payment URL');
    }
  };



  // const copyToClipboard = async (text: string) => {
  //   try {
  //     await navigator.clipboard.writeText(text);
  //     toast.success('Copied to clipboard!');
  //   } catch (error) {
  //     toast.error('Failed to copy to clipboard');
  //   }
  // };

  // const handleSuccess = (result: any) => {
  //   toast.success('Payment completed successfully!');
  //   onSuccess?.(result);
  // };

  // const handleError = (error: any) => {
  //   toast.error('Payment failed');
  //   onError?.(error);
  // };

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 font-['Poppins'] mb-2">
          Square Payment
        </h2>
        <p className="text-gray-600 font-['Poppins']">
          Secure payment processing
        </p>
      </div>

      {!showOptions ? (
        <div className="space-y-6">
          {/* Amount Input with Keyboard */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 font-['Poppins'] mb-2">
              Amount ({selectedCurrency}) *
            </label>
            <AndroidNumericKeypad
              value={amountInput}
              onChange={handleAmountChange}
              placeholder="0.00"
              maxLength={8}
              allowDecimal={true}
              className="w-full"
              currency={selectedCurrency}
            />
          </div>

          {/* Payer Name */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 font-['Poppins'] mb-2">
              Payer Name *
            </label>
            <input
              type="text"
              placeholder="Enter payer name"
              className="w-full px-4 py-3 text-lg border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={payerName}
              onChange={(e) => setPayerName(e.target.value)}
              disabled={isLoading}
            />
          </div>

          {/* Email (Optional) */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 font-['Poppins'] mb-2">
              Email (Optional)
            </label>
            <input
              type="email"
              placeholder="Enter email address"
              className="w-full px-4 py-3 text-lg border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={payerEmail}
              onChange={(e) => setPayerEmail(e.target.value)}
              disabled={isLoading}
            />
          </div>

          {/* Get Payment URL Button */}
          <button
            onClick={handleGetPaymentURL}
            disabled={isLoading || amount <= 0 || !payerName.trim()}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed font-['Poppins'] font-medium mb-3"
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Generating Payment URL...
              </div>
            ) : (
              'Get Payment URL'
            )}
          </button>

          {/* More Options Button */}
          <button
            onClick={handleMoreOptions}
            disabled={isLoading || amount <= 0 || !payerName.trim()}
            className="w-full bg-gray-600 text-white py-3 px-4 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed font-['Poppins'] font-medium mb-3"
          >
            More Options
          </button>

          {/* Cancel Button */}
          <button
            onClick={() => window.location.reload()}
            className="w-full bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 font-['Poppins']"
          >
            Cancel
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="text-center">
            <h4 className="text-lg font-medium text-gray-900 mb-2 font-['Poppins']">
              Payment Amount: {selectedCurrency === 'USD' ? '$' : '€'}{(amount / 100).toFixed(2)}
            </h4>
            <p className="text-sm text-gray-600 mb-4 font-['Poppins']">Choose your payment method</p>
          </div>

          <div className="grid grid-cols-1 gap-3">
            <button
              onClick={handlePaymentUrl}
              disabled={isLoading}
              className="flex items-center justify-center gap-3 w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed font-['Poppins'] font-medium"
            >
              <ExternalLink className="w-5 h-5" />
              {isLoading ? 'Opening...' : 'Open Payment Page'}
            </button>
          </div>

          <button
            onClick={() => setShowOptions(false)}
            className="w-full text-gray-600 py-2 px-4 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 font-['Poppins']"
          >
            Back to Form
          </button>
        </div>
      )}

      {/* Square now uses hosted checkout - no modal needed */}
    </div>
  );
};

export default SquarePaymentForm;
