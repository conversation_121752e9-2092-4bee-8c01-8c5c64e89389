"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerMongooseShutdown = exports.disconnectDB = exports.connectDB = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const env_1 = require("./env");
const logger_1 = __importDefault(require("../lib/logger")); // Use the default export
let isConnected = false;
const connectDB = async () => {
    if (isConnected) {
        logger_1.default.info('MongoDB is already connected.');
        return;
    }
    try {
        logger_1.default.info(`Attempting to connect to MongoDB at ${env_1.env.MONGO_URI.split('@')[1] || env_1.env.MONGO_URI}...`); // Avoid logging credentials
        mongoose_1.default.set('strictQuery', true); // Recommended for preparing for Mongoose 7
        await mongoose_1.default.connect(env_1.env.MONGO_URI, {
            // useNewUrlParser: true, // Deprecated in Mongoose 6+
            // useUnifiedTopology: true, // Deprecated in Mongoose 6+
            // useCreateIndex: true, // Not supported in Mongoose 6+ (indexes are created automatically)
            // useFindAndModify: false, // Not supported in Mongoose 6+ (use findOneAndUpdate, etc.)
            serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
            connectTimeoutMS: 10000, // Connection timeout
        });
        isConnected = true;
        logger_1.default.info('MongoDB connected successfully.');
        mongoose_1.default.connection.on('error', (err) => {
            logger_1.default.error('MongoDB connection error:', err);
            isConnected = false; // Update connection status on error
            // Potentially attempt to reconnect or handle error appropriately
        });
        mongoose_1.default.connection.on('disconnected', () => {
            logger_1.default.info('MongoDB disconnected.');
            isConnected = false;
            // Potentially attempt to reconnect
        });
        mongoose_1.default.connection.on('reconnected', () => {
            logger_1.default.info('MongoDB reconnected.');
            isConnected = true;
        });
    }
    catch (error) {
        logger_1.default.error('Failed to connect to MongoDB:', error);
        // process.exit(1); // Exit if DB connection is critical for startup
        throw error; // Re-throw to be caught by the caller in app.ts
    }
};
exports.connectDB = connectDB;
const disconnectDB = async () => {
    if (!isConnected) {
        logger_1.default.info('MongoDB is not connected.');
        return;
    }
    try {
        await mongoose_1.default.disconnect();
        isConnected = false;
        logger_1.default.info('MongoDB disconnected successfully via disconnectDB function.');
    }
    catch (error) {
        logger_1.default.error('Error disconnecting MongoDB:', error);
        throw error;
    }
};
exports.disconnectDB = disconnectDB;
// Graceful shutdown for Mongoose connection
const registerMongooseShutdown = (appCloseSignal) => {
    appCloseSignal.then(async () => {
        if (isConnected) {
            logger_1.default.info('Closing MongoDB connection due to app shutdown...');
            await mongoose_1.default.disconnect();
            logger_1.default.info('MongoDB connection closed.');
        }
    });
};
exports.registerMongooseShutdown = registerMongooseShutdown;
// You can also register this directly with process signals if not using Fastify's app.close
// process.on('SIGINT', async () => {
//   await disconnectDB();
//   process.exit(0);
// });
// process.on('SIGTERM', async () => {
//   await disconnectDB();
//   process.exit(0);
// });
//# sourceMappingURL=db.js.map