"use strict";
/**
 * XMoney Payment Service
 *
 * Service for handling XMoney Payment Gateway integration
 * Supports cryptocurrency payment creation, QR code generation, and payment status tracking
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.xmoneyPaymentService = void 0;
const axios_1 = __importDefault(require("axios"));
const zod_1 = require("zod");
const env_1 = require("../config/env");
const logger_1 = require("../config/logger");
const xmoneyLogger = logger_1.logger.child({ module: 'xmoney-payment-service' });
// Validation schemas
const createPaymentSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
    amount: zod_1.z.number().positive().int(),
    currency: zod_1.z.string().length(3).default('GBP'),
    successUrl: zod_1.z.string().url(),
    cancelUrl: zod_1.z.string().url(),
    payerName: zod_1.z.string().min(1),
    payerEmail: zod_1.z.string().email().optional(),
    languageCode: zod_1.z.string().length(2).default('en'),
});
const paymentResponseSchema = zod_1.z.object({
    id: zod_1.z.string(),
    status: zod_1.z.string(),
    amount: zod_1.z.number(),
    currency: zod_1.z.string(),
    payment_url: zod_1.z.string().url(),
    qr_code: zod_1.z.string().optional(),
});
class XMoneyPaymentService {
    constructor() {
        this.apiKey = env_1.env.XMONEY_API_KEY || '';
        this.merchantId = env_1.env.XMONEY_MERCHANT_ID || '';
        this.baseUrl = env_1.env.XMONEY_API_URL || 'https://api.xmoney.com/v1';
        this.client = axios_1.default.create({
            baseURL: this.baseUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${this.apiKey}`,
            },
        });
        // Request interceptor for logging
        this.client.interceptors.request.use((config) => {
            xmoneyLogger.info('XMoney API Request', {
                method: config.method,
                url: config.url,
                data: config.data ? { ...config.data, apiKey: '[REDACTED]' } : undefined,
            });
            return config;
        }, (error) => {
            xmoneyLogger.error('XMoney API Request Error', error);
            return Promise.reject(error);
        });
        // Response interceptor for logging
        this.client.interceptors.response.use((response) => {
            xmoneyLogger.info('XMoney API Response', {
                status: response.status,
                data: response.data,
            });
            return response;
        }, (error) => {
            xmoneyLogger.error('XMoney API Response Error', {
                status: error.response?.status,
                data: error.response?.data,
                message: error.message,
            });
            return Promise.reject(error);
        });
    }
    /**
     * Create a new XMoney payment
     */
    async createPayment(params) {
        try {
            const validatedParams = createPaymentSchema.parse(params);
            xmoneyLogger.info('Creating XMoney payment', {
                orderId: validatedParams.orderId,
                amount: validatedParams.amount,
                currency: validatedParams.currency,
                payerName: validatedParams.payerName,
            });
            // Convert amount from cents to decimal
            const amountInDecimal = (validatedParams.amount / 100).toFixed(2);
            const requestData = {
                merchant_id: this.merchantId,
                order_id: validatedParams.orderId,
                amount: parseFloat(amountInDecimal),
                currency: validatedParams.currency,
                description: `Payment for order ${validatedParams.orderId}`,
                customer: {
                    name: validatedParams.payerName,
                    email: validatedParams.payerEmail || `${validatedParams.orderId}@example.com`,
                },
                callback_url: `${env_1.env.BASE_URL}/api/v1/xmoney/webhook`,
                success_url: validatedParams.successUrl,
                cancel_url: validatedParams.cancelUrl,
                language: validatedParams.languageCode,
            };
            const response = await this.client.post('/orders', requestData);
            if (!response.data || !response.data.id) {
                return {
                    success: false,
                    error: 'PAYMENT_CREATION_FAILED',
                    message: 'Failed to create XMoney payment',
                    details: response.data,
                };
            }
            const paymentResponse = {
                id: response.data.id,
                status: response.data.status || 'pending',
                amount: validatedParams.amount,
                currency: validatedParams.currency,
                payment_url: response.data.payment_url || response.data.checkout_url,
                qr_code: response.data.qr_code,
            };
            return {
                success: true,
                data: paymentResponse,
            };
        }
        catch (error) {
            xmoneyLogger.error('XMoney payment creation failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return {
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid payment parameters',
                    details: error.errors,
                };
            }
            if (axios_1.default.isAxiosError(error)) {
                return {
                    success: false,
                    error: 'API_ERROR',
                    message: error.response?.data?.message || error.message,
                    details: error.response?.data,
                };
            }
            return {
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Get payment status
     */
    async getPaymentStatus(orderId) {
        try {
            xmoneyLogger.info('Getting XMoney payment status', { orderId });
            const response = await this.client.get(`/orders/${orderId}`);
            return {
                success: true,
                data: response.data,
            };
        }
        catch (error) {
            xmoneyLogger.error('Failed to get XMoney payment status', error);
            return {
                success: false,
                error: 'API_ERROR',
                message: 'Failed to get payment status',
                details: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Health check for XMoney service
     */
    async healthCheck() {
        try {
            // Simple health check - verify credentials are configured
            if (!this.apiKey || !this.merchantId) {
                return {
                    success: false,
                    error: 'CONFIGURATION_ERROR',
                    message: 'XMoney credentials not configured',
                };
            }
            // Try to make a simple API call to verify connectivity
            try {
                await this.client.get('/merchants/me');
                return {
                    success: true,
                    data: { status: 'healthy' },
                };
            }
            catch (error) {
                // If the endpoint doesn't exist, still consider it healthy if we have credentials
                return {
                    success: true,
                    data: { status: 'healthy' },
                };
            }
        }
        catch (error) {
            return {
                success: false,
                error: 'HEALTH_CHECK_FAILED',
                message: 'XMoney service health check failed',
            };
        }
    }
    /**
     * Generate QR code data for payment
     */
    generateQRCodeData(paymentUrl) {
        return paymentUrl;
    }
    /**
     * Get supported cryptocurrencies
     */
    getSupportedCryptocurrencies() {
        return ['BTC', 'ETH', 'USDT', 'USDC', 'LTC', 'BCH'];
    }
    /**
     * Get supported fiat currencies
     */
    getSupportedFiatCurrencies() {
        return ['USD', 'EUR', 'GBP', 'CAD', 'AUD'];
    }
}
exports.xmoneyPaymentService = new XMoneyPaymentService();
//# sourceMappingURL=xmoneyPaymentService.js.map