{"version": 3, "file": "stripeTerminalService.js", "sourceRoot": "", "sources": ["../../src/services/stripeTerminalService.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAC5B,uCAAoC;AACpC,6CAA0C;AAE1C,MAAM,cAAc,GAAG,eAAM,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,iBAAiB,EAAE,CAAC,CAAC;AAuCnE,MAAa,qBAAqB;IAIhC;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAM,CAAC,SAAG,CAAC,iBAAiB,EAAE;YAC9C,UAAU,EAAE,YAAY;YACxB,UAAU,EAAE,IAAI;SACjB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CAAC,QAAiB;QAC3C,IAAI,CAAC;YACH,cAAc,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAE/D,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC;gBACzE,QAAQ,EAAE,QAAQ,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;aAC/C,CAAC,CAAC;YAEH,OAAO;gBACL,MAAM,EAAE,eAAe,CAAC,MAAM;gBAC9B,QAAQ,EAAE,eAAe,CAAC,QAAQ,IAAI,SAAS;aAChD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,cAAc,CAAC,KAAK,CAAC,mCAAmC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACrE,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC,eAAe,CAAC;YAC9B,CAAC;YAED,gCAAgC;YAChC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;YAE1E,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAqB,CAAC;gBAC7D,cAAc,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC,CAAC;gBACxF,OAAO,IAAI,CAAC,eAAe,CAAC;YAC9B,CAAC;YAED,sBAAsB;YACtB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC3D,YAAY,EAAE,qBAAqB;gBACnC,OAAO,EAAE;oBACP,KAAK,EAAE,iBAAiB;oBACxB,IAAI,EAAE,eAAe;oBACrB,KAAK,EAAE,IAAI;oBACX,WAAW,EAAE,OAAO;oBACpB,OAAO,EAAE,IAAI;iBACd;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,eAAe,GAAG,QAA4B,CAAC;YACpD,cAAc,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC,CAAC;YAErF,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,cAAc,CAAC,KAAK,CAAC,2BAA2B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAC1D,OAAO,OAAO,CAAC,IAAwB,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,cAAc,CAAC,KAAK,CAAC,wBAAwB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC1D,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,QAAgB;QAC9B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACrE,OAAO,MAAwB,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,cAAc,CAAC,KAAK,CAAC,sBAAsB,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;YAClE,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,IAAuB;QAC/C,IAAI,CAAC;YACH,cAAc,CAAC,IAAI,CAAC,sCAAsC,EAAE;gBAC1D,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;gBAC5D,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;gBAC/C,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,QAAQ,EAAE;oBACR,MAAM,EAAE,iBAAiB;oBACzB,GAAG,IAAI,CAAC,QAAQ;iBACjB;aACF,CAAC,CAAC;YAEH,cAAc,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBAC5C,eAAe,EAAE,aAAa,CAAC,EAAE;gBACjC,MAAM,EAAE,aAAa,CAAC,MAAM;aAC7B,CAAC,CAAC;YAEH,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,cAAc,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACnE,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,QAAgB,EAAE,eAAuB;QAC5D,IAAI,CAAC;YACH,cAAc,CAAC,IAAI,CAAC,gCAAgC,EAAE;gBACpD,QAAQ;gBACR,eAAe;aAChB,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,oBAAoB,CAAC,QAAQ,EAAE;gBAChE,cAAc,EAAE,eAAe;aAChC,CAAC,CAAC;YAEH,+BAA+B;YAC/B,IAAI,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;YAE/E,4DAA4D;YAC5D,IAAI,QAAQ,GAAG,CAAC,CAAC;YACjB,MAAM,WAAW,GAAG,EAAE,CAAC,CAAC,qBAAqB;YAE7C,OAAO,aAAa,CAAC,MAAM,KAAK,yBAAyB,IAAI,QAAQ,GAAG,WAAW,EAAE,CAAC;gBACpF,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBACxD,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;gBAC3E,QAAQ,EAAE,CAAC;YACb,CAAC;YAED,cAAc,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBAClD,eAAe;gBACf,MAAM,EAAE,aAAa,CAAC,MAAM;aAC7B,CAAC,CAAC;YAEH,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,cAAc,CAAC,KAAK,CAAC,uCAAuC,EAAE;gBAC5D,KAAK;gBACL,QAAQ;gBACR,eAAe;aAChB,CAAC,CAAC;YACH,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,eAAuB;QAC/C,IAAI,CAAC;YACH,cAAc,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAE,eAAe,EAAE,CAAC,CAAC;YAEtE,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YAE/E,cAAc,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,eAAe,EAAE,CAAC,CAAC;YAErE,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,cAAc,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;YACpF,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CAAC,eAAuB;QAChD,IAAI,CAAC;YACH,cAAc,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,eAAe,EAAE,CAAC,CAAC;YAErE,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YAEhF,cAAc,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,eAAe,EAAE,CAAC,CAAC;YAEpE,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,cAAc,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;YACrF,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,eAAuB,EAAE,MAAe;QACzD,IAAI,CAAC;YACH,cAAc,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,eAAe,EAAE,MAAM,EAAE,CAAC,CAAC;YAEpE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;gBAC9C,cAAc,EAAE,eAAe;gBAC/B,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;YAEH,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBACpC,QAAQ,EAAE,MAAM,CAAC,EAAE;gBACnB,eAAe;gBACf,MAAM,EAAE,MAAM,CAAC,MAAM;aACtB,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,cAAc,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,QAAgB;QACpC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC9C,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,YAAY,EAAG,MAAc,CAAC,YAAY;aAC3C,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,cAAc,CAAC,KAAK,CAAC,6BAA6B,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;YACzE,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,IAAuB;QAC3C,IAAI,CAAC;YACH,cAAc,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YAE/E,wBAAwB;YACxB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAE3D,uDAAuD;YACvD,MAAM,sBAAsB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,EAAE;gBACxF,cAAc,EAAE,cAAc,EAAE,sBAAsB;gBACtD,UAAU,EAAE,4BAA4B;aACzC,CAAC,CAAC;YAEH,cAAc,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBAClD,eAAe,EAAE,sBAAsB,CAAC,EAAE;gBAC1C,MAAM,EAAE,sBAAsB,CAAC,MAAM;aACtC,CAAC,CAAC;YAEH,OAAO,sBAAsB,CAAC;QAChC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,cAAc,CAAC,KAAK,CAAC,4BAA4B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAAC,eAAuB;QACnD,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,eAAe,EAAE;gBAC/E,MAAM,EAAE,CAAC,gBAAgB,CAAC;aAC3B,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,aAAa,GAAG,aAAa,CAAC,cAAsC,CAAC;YAE3E,OAAO;gBACL,IAAI,EAAE,aAAa,CAAC,IAAI;gBACxB,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;oBACzB,KAAK,EAAE,aAAa,CAAC,IAAI,CAAC,KAAK;oBAC/B,KAAK,EAAE,aAAa,CAAC,IAAI,CAAC,KAAK;oBAC/B,SAAS,EAAE,aAAa,CAAC,IAAI,CAAC,SAAS;oBACvC,QAAQ,EAAE,aAAa,CAAC,IAAI,CAAC,QAAQ;iBACtC,CAAC,CAAC,CAAC,IAAI;aACT,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,cAAc,CAAC,KAAK,CAAC,sCAAsC,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;YACzF,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CACF;AApTD,sDAoTC;AAED,4BAA4B;AACf,QAAA,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC"}