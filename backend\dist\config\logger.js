"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createChildLogger = exports.logger = void 0;
const pino_1 = __importDefault(require("pino"));
const env_1 = require("./env");
const isDevelopment = env_1.env.NODE_ENV === 'development';
exports.logger = (0, pino_1.default)({
    level: env_1.env.LOG_LEVEL,
    transport: isDevelopment ? {
        target: 'pino-pretty',
        options: {
            colorize: true,
            translateTime: 'SYS:standard',
            ignore: 'pid,hostname',
        },
    } : undefined,
    formatters: {
        level: (label) => ({ level: label }),
    },
    timestamp: pino_1.default.stdTimeFunctions.isoTime,
    redact: {
        paths: [
            'req.headers.authorization',
            'req.headers.cookie',
            'res.headers["set-cookie"]',
            'stripe_secret_key',
            'jwt_secret',
            'password',
            'token',
        ],
        censor: '[REDACTED]',
    },
    serializers: {
        req: pino_1.default.stdSerializers.req,
        res: pino_1.default.stdSerializers.res,
        err: pino_1.default.stdSerializers.err,
    },
});
const createChildLogger = (context) => {
    return exports.logger.child(context);
};
exports.createChildLogger = createChildLogger;
//# sourceMappingURL=logger.js.map