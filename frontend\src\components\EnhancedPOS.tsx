/**
 * Enhanced POS Terminal Component
 *
 * Integrates multiple payment providers: PAX Hardware, Stripe Terminal, Novalnet, and Move Payment
 * Provides seamless fallback between payment methods
 */

import { useState, useEffect } from 'react';
import { StripeTerminalPOS } from './StripeTerminalPOS';
import { NovalnetPaymentForm } from './NovalnetPaymentForm';
import { MovePaymentForm } from './MovePaymentForm';
import { SkrillPaymentForm } from './SkrillPaymentForm';
import { NuveiPaymentForm } from './NuveiPaymentForm';
import { XMoneyPaymentForm } from './XMoneyPaymentForm';
import SquarePaymentForm from './SquarePaymentForm';
import PayoneerPaymentForm from './PayoneerPaymentForm';
import VividPaymentForm from './VividPaymentForm';
import VivaPaymentForm from './VivaPaymentForm';
import { PaxPosTerminal } from './PaxPosTerminal';
import { useSuccessToast, useErrorToast, useWarningToast } from './ui/toast';
// import { env } from '../config/env';

type PaymentProvider = 'pax' | 'stripe' | 'novalnet' | 'move' | 'skrill' | 'nuvei' | 'xmoney' | 'square' | 'payoneer' | 'vivid' | 'viva';
type PaymentState = 'idle' | 'provider-selection' | 'processing' | 'success' | 'error';

interface EnhancedPOSProps {
  onBack?: () => void;
  defaultProvider?: PaymentProvider;
  enableProviderSelection?: boolean;
}

interface PaymentResult {
  provider: PaymentProvider;
  transactionId: string;
  amount: number;
  currency: string;
  status: string;
  metadata?: any;
}

export function EnhancedPOS({ 
  onBack, 
  defaultProvider = 'stripe',
  enableProviderSelection = true 
}: EnhancedPOSProps) {
  const [selectedProvider, setSelectedProvider] = useState<PaymentProvider>(defaultProvider);
  const [paymentState, setPaymentState] = useState<PaymentState>('idle');
  const [amount, setAmount] = useState<number>(0);
  const [lastPaymentResult, setLastPaymentResult] = useState<PaymentResult | null>(null);

  const showSuccess = useSuccessToast();
  const showError = useErrorToast();
  const showWarning = useWarningToast();

  // All payment providers are now available without health checks

  // Available payment providers - Only Move and Square are active
  const availableProviders: { id: PaymentProvider; name: string; description: string; available: boolean }[] = [
    {
      id: 'move',
      name: 'Move Payment',
      description: 'QR code-based mobile payment (EUR)',
      available: true,
    },
    {
      id: 'square',
      name: 'Square Payment',
      description: 'Secure payment processing (GBP)',
      available: true,
    },
    {
      id: 'viva',
      name: 'Viva Wallet',
      description: 'European payment processing (GBP)',
      available: true,
    },
    // Disabled payment providers
    {
      id: 'pax',
      name: 'PAX POS Terminal',
      description: 'Hardware terminal payment processing (Disabled)',
      available: false,
    },
    {
      id: 'stripe',
      name: 'Stripe Terminal',
      description: 'PAX hardware or card payment fallback (Disabled)',
      available: false,
    },
    {
      id: 'novalnet',
      name: 'Novalnet',
      description: 'Web-based payment processing (Disabled)',
      available: false,
    },
    {
      id: 'skrill',
      name: 'Skrill Payment',
      description: 'Digital wallet and online payment (Disabled)',
      available: false,
    },
    {
      id: 'nuvei',
      name: 'Nuvei Payment',
      description: 'Secure payment processing via Nuvei Gateway (Disabled)',
      available: false,
    },
    {
      id: 'xmoney',
      name: 'XMoney Payment',
      description: 'Cryptocurrency and digital payment processing (Disabled)',
      available: false,
    },
    {
      id: 'payoneer',
      name: 'Payoneer Payment',
      description: 'Global payment processing via Payoneer (Disabled)',
      available: false,
    },
    {
      id: 'vivid',
      name: 'Vivid Money Payment',
      description: 'Digital banking payment solution (Disabled)',
      available: false,
    },
  ];

  const handleProviderSelect = (provider: PaymentProvider) => {
    setSelectedProvider(provider);
    setPaymentState('processing');
  };

  const handlePaymentSuccess = (result: any, provider: PaymentProvider) => {
    const paymentResult: PaymentResult = {
      provider,
      transactionId: result.transaction_id || result.id || 'unknown',
      amount: result.amount || amount,
      currency: result.currency || 'EUR',
      status: 'success',
      metadata: result,
    };

    setLastPaymentResult(paymentResult);
    setPaymentState('success');

    showSuccess(
      'Payment Successful',
      `Payment processed successfully via ${availableProviders.find(p => p.id === provider)?.name}`
    );
  };

  const handlePaymentError = (error: any, provider: PaymentProvider) => {
    console.error(`Payment failed with ${provider}:`, error);
    setPaymentState('error');

    const errorMessage = error?.message || error?.error || 'Payment processing failed';
    showError('Payment Failed', `${errorMessage} (${provider})`);

    // Auto-fallback logic - only suggest active providers
    if (enableProviderSelection) {
      // Suggest available alternatives (Move and Square)
      const moveAvailable = availableProviders.find(p => p.id === 'move')?.available;
      const squareAvailable = availableProviders.find(p => p.id === 'square')?.available;

      if (moveAvailable || squareAvailable) {
        const alternatives = [];
        if (moveAvailable) alternatives.push('Move Payment');
        if (squareAvailable) alternatives.push('Square Payment');

        showWarning(
          'Alternative Available',
          `Payment failed. You can try ${alternatives.join(' or ')} as an alternative.`
        );
        setTimeout(() => {
          setPaymentState('provider-selection');
        }, 3000);
      }
    }
  };

  // const handleAmountChange = (newAmount: number) => {
  //   setAmount(newAmount);
  // };

  const resetPayment = () => {
    setPaymentState('idle');
    setLastPaymentResult(null);
    setAmount(0);
  };

  const goToProviderSelection = () => {
    setPaymentState('provider-selection');
  };

  // Auto-select provider if only one is available
  useEffect(() => {
    if (!enableProviderSelection) {
      const availableCount = availableProviders.filter(p => p.available).length;
      if (availableCount === 1) {
        const onlyProvider = availableProviders.find(p => p.available);
        if (onlyProvider) {
          setSelectedProvider(onlyProvider.id);
        }
      }
    }
  }, [availableProviders, enableProviderSelection]);

  // Render provider selection screen
  if (paymentState === 'provider-selection' || (paymentState === 'idle' && enableProviderSelection)) {
    return (
      <div className="min-h-screen bg-gray-100 p-4">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-800 font-['Poppins']">
                Select Payment Method
              </h2>
              {onBack && (
                <button
                  onClick={onBack}
                  className="text-gray-600 hover:text-gray-800 font-['Poppins']"
                >
                  ← Back
                </button>
              )}
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              {availableProviders.map((provider) => (
                <button
                  key={provider.id}
                  onClick={() => provider.available && handleProviderSelect(provider.id)}
                  disabled={!provider.available}
                  className={`p-6 rounded-lg border-2 text-left transition-all ${
                    provider.available
                      ? 'border-blue-200 hover:border-blue-400 hover:bg-blue-50 cursor-pointer'
                      : 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-50'
                  }`}
                >
                  <h3 className="text-lg font-semibold text-gray-800 mb-2 font-['Poppins']">
                    {provider.name}
                  </h3>
                  <p className="text-gray-600 text-sm font-['Poppins']">
                    {provider.description}
                  </p>
                  {!provider.available && (
                    <p className="text-red-500 text-xs mt-2 font-['Poppins']">
                      Currently unavailable
                    </p>
                  )}
                </button>
              ))}
            </div>

            {/* Service Status */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-2 font-['Poppins']">
                Service Status
              </h4>
              <div className="space-y-1 text-xs">
                <div className="text-center">
                  <span className="text-green-600 font-['Poppins'] font-semibold">All Payment Methods Available</span>
                </div>
                <div className="text-center text-gray-500 text-xs">
                  <span className="font-['Poppins']">Health checks disabled - all providers enabled</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render success screen
  if (paymentState === 'success' && lastPaymentResult) {
    return (
      <div className="min-h-screen bg-gray-100 p-4">
        <div className="max-w-md mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-6 text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            
            <h2 className="text-2xl font-bold text-gray-800 mb-2 font-['Poppins']">
              Payment Successful!
            </h2>
            
            <div className="space-y-2 text-gray-600 mb-6">
              <p className="font-['Poppins']">
                Amount: <span className="font-semibold">
                  ${(lastPaymentResult.amount / 100).toFixed(2)}
                </span>
              </p>
              <p className="font-['Poppins']">
                Provider: <span className="font-semibold">
                  {availableProviders.find(p => p.id === lastPaymentResult.provider)?.name}
                </span>
              </p>
              <p className="font-['Poppins']">
                Transaction ID: <span className="font-mono text-sm">
                  {lastPaymentResult.transactionId}
                </span>
              </p>
            </div>

            <div className="space-y-3">
              <button
                onClick={resetPayment}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 font-medium font-['Poppins']"
              >
                New Payment
              </button>
              
              {onBack && (
                <button
                  onClick={onBack}
                  className="w-full bg-gray-300 text-gray-700 py-3 px-4 rounded-md hover:bg-gray-400 font-medium font-['Poppins']"
                >
                  Back to Dashboard
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render selected payment provider
  if (paymentState === 'processing') {
    switch (selectedProvider) {
      case 'pax':
        return (
          <PaxPosTerminal
            onBack={enableProviderSelection ? goToProviderSelection : onBack}
            onPaymentComplete={(result) => handlePaymentSuccess(result, 'pax')}
            defaultAmount={amount / 100} // Convert from cents to dollars
          />
        );

      case 'stripe':
        return (
          <StripeTerminalPOS
            onBack={enableProviderSelection ? goToProviderSelection : onBack}
          />
        );

      case 'novalnet':
        return (
          <div className="min-h-screen bg-gray-100 p-4">
            <div className="max-w-2xl mx-auto">
              {enableProviderSelection && (
                <div className="mb-4">
                  <button
                    onClick={goToProviderSelection}
                    className="text-blue-600 hover:text-blue-800 font-['Poppins']"
                  >
                    ← Change Payment Method
                  </button>
                </div>
              )}

              <NovalnetPaymentForm
                amount={amount || 1000} // Default to $10.00 if no amount set
                currency="USD"
                onSuccess={(result) => handlePaymentSuccess(result, 'novalnet')}
                onError={(error) => handlePaymentError(error, 'novalnet')}
                onCancel={enableProviderSelection ? goToProviderSelection : onBack}
                testMode={true}
                className="w-full"
              />
            </div>
          </div>
        );

      case 'move':
        return (
          <div className="min-h-screen bg-gray-100 p-4">
            <div className="max-w-2xl mx-auto">
              {enableProviderSelection && (
                <div className="mb-4">
                  <button
                    onClick={goToProviderSelection}
                    className="text-blue-600 hover:text-blue-800 font-['Poppins']"
                  >
                    ← Change Payment Method
                  </button>
                </div>
              )}

              <MovePaymentForm
                amount={amount || 0} // Start with 0 if no amount set
                currency="GBP"
                onSuccess={(result) => handlePaymentSuccess(result, 'move')}
                onError={(error) => handlePaymentError(error, 'move')}
                onCancel={enableProviderSelection ? goToProviderSelection : onBack}
                className="w-full"
              />
            </div>
          </div>
        );

      case 'skrill':
        return (
          <div className="min-h-screen bg-gray-100 p-4">
            <div className="max-w-2xl mx-auto">
              {enableProviderSelection && (
                <div className="mb-4">
                  <button
                    onClick={goToProviderSelection}
                    className="text-blue-600 hover:text-blue-800 font-['Poppins']"
                  >
                    ← Change Payment Method
                  </button>
                </div>
              )}

              <SkrillPaymentForm
                onSuccess={(result) => handlePaymentSuccess(result, 'skrill')}
                onError={(error) => handlePaymentError(error, 'skrill')}
              />
            </div>
          </div>
        );

      case 'nuvei':
        return (
          <div className="min-h-screen bg-gray-100 p-4">
            <div className="max-w-2xl mx-auto">
              {enableProviderSelection && (
                <div className="mb-4">
                  <button
                    onClick={goToProviderSelection}
                    className="text-blue-600 hover:text-blue-800 font-['Poppins']"
                  >
                    ← Change Payment Method
                  </button>
                </div>
              )}

              <NuveiPaymentForm
                amount={amount || 1000} // Default to £10.00 if no amount set
                currency="GBP"
                onSuccess={(result) => handlePaymentSuccess(result, 'nuvei')}
                onError={(error) => handlePaymentError(error, 'nuvei')}
                onCancel={enableProviderSelection ? goToProviderSelection : onBack}
                className="w-full"
              />
            </div>
          </div>
        );

      case 'xmoney':
        return (
          <div className="min-h-screen bg-gray-100 p-4">
            <div className="max-w-2xl mx-auto">
              {enableProviderSelection && (
                <div className="mb-4">
                  <button
                    onClick={goToProviderSelection}
                    className="text-blue-600 hover:text-blue-800 font-['Poppins']"
                  >
                    ← Change Payment Method
                  </button>
                </div>
              )}

              <XMoneyPaymentForm
                amount={amount || 1000} // Default to £10.00 if no amount set
                currency="GBP"
                onSuccess={(result) => handlePaymentSuccess(result, 'xmoney')}
                onError={(error) => handlePaymentError(error, 'xmoney')}
                onCancel={enableProviderSelection ? goToProviderSelection : onBack}
                className="w-full"
              />
            </div>
          </div>
        );

      case 'square':
        return (
          <div className="min-h-screen bg-gray-100 p-4">
            <div className="max-w-2xl mx-auto">
              {enableProviderSelection && (
                <div className="mb-4">
                  <button
                    onClick={goToProviderSelection}
                    className="text-blue-600 hover:text-blue-800 font-['Poppins']"
                  >
                    ← Change Payment Method
                  </button>
                </div>
              )}

              <SquarePaymentForm
                onSuccess={(result) => handlePaymentSuccess(result, 'square')}
                onError={(error) => handlePaymentError(error, 'square')}
                className="w-full"
              />
            </div>
          </div>
        );

      case 'payoneer':
        return (
          <div className="min-h-screen bg-gray-100 p-4">
            <div className="max-w-2xl mx-auto">
              {enableProviderSelection && (
                <div className="mb-4">
                  <button
                    onClick={goToProviderSelection}
                    className="text-blue-600 hover:text-blue-800 font-['Poppins']"
                  >
                    ← Change Payment Method
                  </button>
                </div>
              )}

              <PayoneerPaymentForm
                onSuccess={(result) => handlePaymentSuccess(result, 'payoneer')}
                onError={(error) => handlePaymentError(error, 'payoneer')}
                className="w-full"
              />
            </div>
          </div>
        );

      case 'vivid':
        return (
          <div className="min-h-screen bg-gray-100 p-4">
            <div className="max-w-2xl mx-auto">
              {enableProviderSelection && (
                <div className="mb-4">
                  <button
                    onClick={goToProviderSelection}
                    className="text-blue-600 hover:text-blue-800 font-['Poppins']"
                  >
                    ← Change Payment Method
                  </button>
                </div>
              )}

              <VividPaymentForm
                onSuccess={(result) => handlePaymentSuccess(result, 'vivid')}
                onError={(error) => handlePaymentError(error, 'vivid')}
                className="w-full"
              />
            </div>
          </div>
        );

      case 'viva':
        return (
          <div className="min-h-screen bg-gray-100 p-4">
            <div className="max-w-2xl mx-auto">
              {enableProviderSelection && (
                <div className="mb-4">
                  <button
                    onClick={goToProviderSelection}
                    className="text-blue-600 hover:text-blue-800 font-['Poppins']"
                  >
                    ← Change Payment Method
                  </button>
                </div>
              )}

              <VivaPaymentForm
                onSuccess={(result) => handlePaymentSuccess(result, 'viva')}
                onError={(error) => handlePaymentError(error, 'viva')}
                className="w-full"
              />
            </div>
          </div>
        );

      default:
        return (
          <div className="min-h-screen bg-gray-100 p-4 flex items-center justify-center">
            <div className="text-center">
              <h2 className="text-xl font-bold text-gray-800 mb-2 font-['Poppins']">
                Payment Provider Not Available
              </h2>
              <p className="text-gray-600 mb-4 font-['Poppins']">
                The selected payment provider is currently unavailable.
              </p>
              <button
                onClick={goToProviderSelection}
                className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 font-['Poppins']"
              >
                Select Different Provider
              </button>
            </div>
          </div>
        );
    }
  }

  // Default idle state - show provider selection
  return (
    <div className="min-h-screen bg-gray-100 p-4 flex items-center justify-center">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-800 mb-4 font-['Poppins']">
          Enhanced POS Terminal
        </h2>
        <p className="text-gray-600 mb-6 font-['Poppins']">
          Multiple payment providers available
        </p>
        <button
          onClick={goToProviderSelection}
          className="bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 font-medium font-['Poppins']"
        >
          Start Payment
        </button>
      </div>
    </div>
  );
}
