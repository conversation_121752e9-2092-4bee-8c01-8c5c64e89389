import { config } from 'dotenv';
import { z } from 'zod';
import path from 'path';

// Load environment files in order of precedence
// 1. Load .env.development (or .env.production/.env.test based on NODE_ENV)
// 2. Load .env as fallback
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' :
                process.env.NODE_ENV === 'test' ? '.env.test' : '.env.development';

// Try to load from project root (monorepo root)
config({ path: path.resolve(process.cwd(), '../../', envFile) });
config({ path: path.resolve(process.cwd(), '../../', '.env'), override: false });

// Also try to load from current directory as fallback
config({ path: path.resolve(process.cwd(), envFile), override: false });
config({ path: path.resolve(process.cwd(), '.env'), override: false });

const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1).max(65535)).default('3001'),
  MONGO_URI: z.string().default('mongodb://localhost:27017/stripe_pos_system'),
  STRIPE_SECRET_KEY: z.string().default('sk_test_development_key_placeholder'),
  STRIPE_PUBLISHABLE_KEY: z.string().default('pk_test_development_key_placeholder'),
  STRIPE_WEBHOOK_SECRET: z.string().default('whsec_development_secret_placeholder'),

  // Novalnet Configuration
  NOVALNET_PAYMENT_ACCESS_KEY: z.string().default('1b08a97dde5aec3854f2217845212Sab'),
  NOVALNET_ACTIVATION_KEY: z.string().default('7ibc7ob5|tuJEH3gNbeWJfIHah||nbobljbnmdli0poys|doU3HJVoym7MQ44qf7cpn7pc'),
  NOVALNET_TARIFF_ID: z.string().default('10004'),
  NOVALNET_WEBHOOK_SECRET: z.string().default('novalnet_webhook_secret_placeholder'),
  NOVALNET_API_URL: z.string().url().default('https://payport.novalnet.de/v2'),
  NOVALNET_ENABLED: z.string().transform(val => val !== 'false').default('true'),

  // Move Payment Configuration
  MOVE_PAYMENT_API_URL: z.string().url().default('https://api.aws.movepayment.eu/api/ecommerce'),
  MOVE_PAYMENT_API_KEY: z.string().default('your_move_payment_api_key_here'),
  MOVE_PAYMENT_MOVE_ID: z.string().default('<EMAIL>'),
  MOVE_PAYMENT_ENABLED: z.string().transform(val => val !== 'false').default('true'),

  // Nuvei Payment Configuration
  NUVEI_MERCHANT_ID: z.string().default('your_nuvei_merchant_id'),
  NUVEI_MERCHANT_SITE_ID: z.string().default('your_nuvei_site_id'),
  NUVEI_SECRET_KEY: z.string().default('your_nuvei_secret_key'),
  NUVEI_API_URL: z.string().url().default('https://ppp-test.nuvei.com/ppp/api/v1'),
  NUVEI_ENVIRONMENT: z.enum(['sandbox', 'production']).default('sandbox'),
  NUVEI_ENABLED: z.string().transform(val => val !== 'false').default('true'),

  // XMoney Payment Configuration
  XMONEY_API_KEY: z.string().default('your_xmoney_api_key'),
  XMONEY_MERCHANT_ID: z.string().default('your_xmoney_merchant_id'),
  XMONEY_API_URL: z.string().url().default('https://api.xmoney.com/v1'),
  XMONEY_ENABLED: z.string().transform(val => val !== 'false').default('true'),

  // Payoneer Payment Configuration
  PAYONEER_API_KEY: z.string().default('your_payoneer_api_key'),
  PAYONEER_MERCHANT_ID: z.string().default('your_payoneer_merchant_id'),
  PAYONEER_API_URL: z.string().url().default('https://api.payoneer.com/v2'),
  PAYONEER_ENABLED: z.string().transform(val => val !== 'false').default('true'),

  // Square Payment Configuration
  SQUARE_ACCESS_TOKEN: z.string().default('your_square_access_token'),
  SQUARE_APPLICATION_ID: z.string().default('your_square_application_id'),
  SQUARE_LOCATION_ID: z.string().default('LNEMG6VQ7GMEE'),
  SQUARE_ENVIRONMENT: z.enum(['sandbox', 'production']).default('sandbox'),
  SQUARE_ENABLED: z.string().transform(val => val !== 'false').default('true'),

  // Vivid Money Payment Configuration
  VIVID_API_KEY: z.string().default('your_vivid_api_key'),
  VIVID_MERCHANT_ID: z.string().default('your_vivid_merchant_id'),
  VIVID_API_URL: z.string().url().default('https://api.vivid.money/v1'),
  VIVID_ENABLED: z.string().transform(val => val !== 'false').default('true'),

  // Viva Wallet Payment Configuration
  VIVA_MERCHANT_ID: z.string().default('your_viva_merchant_id'),
  VIVA_API_KEY: z.string().default('your_viva_api_key'),
  VIVA_CLIENT_ID: z.string().default('your_viva_client_id'),
  VIVA_CLIENT_SECRET: z.string().default('your_viva_client_secret'),
  VIVA_SOURCE_CODE: z.string().default('Default'),
  VIVA_ENVIRONMENT: z.enum(['demo', 'production']).default('demo'),
  VIVA_API_URL: z.string().url().optional(),
  VIVA_ACCOUNTS_URL: z.string().url().optional(),
  VIVA_CHECKOUT_URL: z.string().url().optional(),
  VIVA_HELP_URL: z.string().url().default('https://www.vivapayments.com/'),
  VIVA_ENABLED: z.string().transform(val => val !== 'false').default('true'),

  // Base URL for webhooks and callbacks
  BASE_URL: z.string().url().default('http://localhost:3001'),

  MOCK_BANK_URL: z.string().url().default('http://localhost:3002/financial-message'),
  LOG_LEVEL: z.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']).default('info'),
  JWT_SECRET: z.string().default('development_jwt_secret_at_least_32_characters_long_for_dev'),
  CORS_ORIGIN: z.string().default('http://localhost:3000'),
  RATE_LIMIT_MAX: z.string().transform(val => parseInt(val, 10)).pipe(z.number().positive()).default('100'),
  RATE_LIMIT_WINDOW: z.string().transform(val => parseInt(val, 10)).pipe(z.number().positive()).default('900000'),

  // Terminal Configuration
  TERMINAL_ID: z.string().default('STRIPE_TERMINAL_001'),
  MERCHANT_ID: z.string().default('MERCHANT_001'),
  DEFAULT_CURRENCY: z.string().default('usd'),
  PAYMENT_TIMEOUT: z.string().transform(val => parseInt(val, 10)).pipe(z.number().positive()).default('30000'),
  RETRY_ATTEMPTS: z.string().transform(val => parseInt(val, 10)).pipe(z.number().positive()).default('3'),
  MIN_AMOUNT: z.string().transform(val => parseInt(val, 10)).pipe(z.number().positive()).default('50'),
  MAX_AMOUNT: z.string().transform(val => parseInt(val, 10)).pipe(z.number().positive()).default('99999999'),

  // Feature Flags
  CARD_READER_ENABLED: z.string().transform(val => val !== 'false').default('true'),
  PRINTER_ENABLED: z.string().transform(val => val !== 'false').default('true'),
  AUDIO_ENABLED: z.string().transform(val => val !== 'false').default('true'),
  VIBRATION_ENABLED: z.string().transform(val => val !== 'false').default('true'),
  MANUAL_ENTRY_ENABLED: z.string().transform(val => val !== 'false').default('true'),

  // Receipt Configuration
  RECEIPT_ENABLED: z.string().transform(val => val !== 'false').default('true'),
  RECEIPT_COPIES: z.string().transform(val => parseInt(val, 10)).pipe(z.number().positive()).default('2'),
  RECEIPT_SIGNATURE: z.string().transform(val => val !== 'false').default('true'),
  RECEIPT_FOOTER: z.string().default('Thank you for your business!'),

  // UI Configuration
  UI_THEME: z.enum(['light', 'dark', 'auto']).default('auto'),
  UI_LANGUAGE: z.string().default('en'),
  UI_FONT_SIZE: z.enum(['small', 'medium', 'large']).default('medium'),
  UI_ORIENTATION: z.enum(['portrait', 'landscape', 'auto']).default('portrait'),

  // PAX POS Configuration
  PAX_TERMINAL_IP: z.string().default('*************'),
  PAX_TERMINAL_PORT: z.string().default('8080'),
  PAX_TIMEOUT: z.string().default('30000'),
  PAX_MERCHANT_ID: z.string().default('MERCHANT001'),
  PAX_MERCHANT_NAME: z.string().default('PaxSoft POS'),
  PAX_SIMULATION_MODE: z.string().default('false'),
  PAX_RECEIPT_ENABLED: z.string().default('true'),
  PAX_SIGNATURE_REQUIRED: z.string().default('false'),
});

function validateEnv() {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join('\n');
      throw new Error(`Environment validation failed:\n${missingVars}`);
    }
    throw error;
  }
}

export const env = validateEnv();
export type Environment = typeof env;
