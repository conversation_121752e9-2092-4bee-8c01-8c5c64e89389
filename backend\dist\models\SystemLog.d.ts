import mongoose, { Document } from 'mongoose';
interface ISystemLog {
    _id: string;
    timestamp: string;
    level: 'info' | 'warn' | 'error' | 'debug';
    category: 'payment' | 'hardware' | 'protocol' | 'system' | 'security';
    message: string;
    details?: any;
    userId?: string;
    sessionId?: string;
    createdAt: string;
    updatedAt: string;
}
export interface SystemLogDocument extends Omit<ISystemLog, '_id' | 'createdAt' | 'updatedAt'>, Document {
    _id: string;
    createdAt: Date;
    updatedAt: Date;
}
export declare const SystemLog: mongoose.Model<SystemLogDocument, {}, {}, {}, mongoose.Document<unknown, {}, SystemLogDocument, {}> & SystemLogDocument & Required<{
    _id: string;
}> & {
    __v: number;
}, any>;
export {};
//# sourceMappingURL=SystemLog.d.ts.map