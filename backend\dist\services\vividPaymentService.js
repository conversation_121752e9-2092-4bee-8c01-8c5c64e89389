"use strict";
/**
 * Vivid Money Payment Service
 *
 * Service for handling Vivid Money Payment Gateway integration
 * Supports payment creation, QR code generation, and payment status tracking
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.vividPaymentService = void 0;
const axios_1 = __importDefault(require("axios"));
const zod_1 = require("zod");
const env_1 = require("../config/env");
const logger_1 = require("../config/logger");
const vividLogger = (0, logger_1.createChildLogger)({ module: 'vivid-payment' });
// Validation schemas
const createPaymentSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
    amount: zod_1.z.number().positive(),
    currency: zod_1.z.string().default('GBP'),
    successUrl: zod_1.z.string().url(),
    cancelUrl: zod_1.z.string().url(),
    payerName: zod_1.z.string().min(1),
    payerEmail: zod_1.z.string().email().optional(),
    languageCode: zod_1.z.string().default('en'),
});
const paymentResponseSchema = zod_1.z.object({
    id: zod_1.z.string(),
    status: zod_1.z.string(),
    amount: zod_1.z.number(),
    currency: zod_1.z.string(),
    payment_url: zod_1.z.string().url(),
    qr_code: zod_1.z.string().optional(),
});
/**
 * Vivid Money Payment Service Class
 */
class VividPaymentService {
    constructor() {
        this.apiKey = env_1.env.VIVID_API_KEY;
        this.merchantId = env_1.env.VIVID_MERCHANT_ID;
        this.baseUrl = env_1.env.VIVID_API_URL;
        this.client = axios_1.default.create({
            baseURL: this.baseUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.apiKey}`,
                'X-Merchant-ID': this.merchantId,
            },
        });
        // Request interceptor for logging
        this.client.interceptors.request.use((config) => {
            vividLogger.info('Vivid API Request', {
                method: config.method?.toUpperCase(),
                url: config.url,
                headers: { ...config.headers, Authorization: '[REDACTED]' },
            });
            return config;
        }, (error) => {
            vividLogger.error('Vivid API Request Error', error);
            return Promise.reject(error);
        });
        // Response interceptor for logging
        this.client.interceptors.response.use((response) => {
            vividLogger.info('Vivid API Response', {
                status: response.status,
                statusText: response.statusText,
                data: response.data,
            });
            return response;
        }, (error) => {
            vividLogger.error('Vivid API Response Error', {
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data,
                message: error.message,
            });
            return Promise.reject(error);
        });
    }
    /**
     * Create a new Vivid Money payment
     */
    async createPayment(params) {
        try {
            const validatedParams = createPaymentSchema.parse(params);
            vividLogger.info('Creating Vivid Money payment', {
                orderId: validatedParams.orderId,
                amount: validatedParams.amount,
                currency: validatedParams.currency,
                payerName: validatedParams.payerName,
            });
            // Convert amount from cents to decimal
            const amountInDecimal = (validatedParams.amount / 100).toFixed(2);
            const requestData = {
                merchant_id: this.merchantId,
                order_id: validatedParams.orderId,
                amount: parseFloat(amountInDecimal),
                currency: validatedParams.currency,
                description: `Payment for order ${validatedParams.orderId}`,
                customer: {
                    name: validatedParams.payerName,
                    email: validatedParams.payerEmail || `${validatedParams.orderId}@example.com`,
                },
                callback_url: `${env_1.env.BASE_URL}/api/v1/vivid/webhook`,
                success_url: validatedParams.successUrl,
                cancel_url: validatedParams.cancelUrl,
                language: validatedParams.languageCode,
            };
            const response = await this.client.post('/payments', requestData);
            if (!response.data || !response.data.id) {
                return {
                    success: false,
                    error: 'PAYMENT_CREATION_FAILED',
                    message: 'Failed to create Vivid Money payment',
                    details: response.data,
                };
            }
            const paymentResponse = {
                id: response.data.id,
                status: response.data.status || 'pending',
                amount: validatedParams.amount,
                currency: validatedParams.currency,
                payment_url: response.data.payment_url || response.data.checkout_url,
                qr_code: response.data.qr_code,
            };
            return {
                success: true,
                data: paymentResponse,
            };
        }
        catch (error) {
            vividLogger.error('Failed to create Vivid Money payment', error);
            if (axios_1.default.isAxiosError(error)) {
                return {
                    success: false,
                    error: 'PAYMENT_CREATION_FAILED',
                    message: error.response?.data?.message || error.message,
                    details: error.response?.data,
                };
            }
            return {
                success: false,
                error: 'PAYMENT_CREATION_FAILED',
                message: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Get payment status
     */
    async getPaymentStatus(orderId) {
        try {
            vividLogger.info('Getting Vivid Money payment status', { orderId });
            const response = await this.client.get(`/payments/${orderId}`);
            if (!response.data) {
                return {
                    success: false,
                    error: 'STATUS_RETRIEVAL_FAILED',
                    message: 'Failed to get payment status',
                };
            }
            return {
                success: true,
                data: response.data,
            };
        }
        catch (error) {
            vividLogger.error('Failed to get Vivid Money payment status', error);
            if (axios_1.default.isAxiosError(error)) {
                return {
                    success: false,
                    error: 'STATUS_RETRIEVAL_FAILED',
                    message: error.response?.data?.message || error.message,
                    details: error.response?.data,
                };
            }
            return {
                success: false,
                error: 'STATUS_RETRIEVAL_FAILED',
                message: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Health check for Vivid Money service
     */
    async healthCheck() {
        try {
            // Simple health check - verify credentials are configured
            if (!this.apiKey || !this.merchantId) {
                return {
                    success: false,
                    error: 'CONFIGURATION_ERROR',
                    message: 'Vivid Money credentials not configured',
                };
            }
            // Try to get merchant info to verify API connectivity
            try {
                await this.client.get('/merchant/info');
                return {
                    success: true,
                    data: { status: 'healthy' },
                };
            }
            catch (error) {
                return {
                    success: false,
                    error: 'API_CONNECTION_FAILED',
                    message: 'Failed to connect to Vivid Money API',
                };
            }
        }
        catch (error) {
            return {
                success: false,
                error: 'HEALTH_CHECK_FAILED',
                message: 'Vivid Money service health check failed',
            };
        }
    }
    /**
     * Generate QR code data for payment
     */
    generateQRCodeData(paymentUrl) {
        return paymentUrl;
    }
    /**
     * Get supported payment methods
     */
    getSupportedPaymentMethods() {
        return ['card', 'bank_transfer', 'digital_wallet'];
    }
    /**
     * Get supported currencies
     */
    getSupportedCurrencies() {
        return ['EUR', 'GBP', 'USD'];
    }
    /**
     * Check if Vivid Money Payment is enabled
     */
    isEnabled() {
        return env_1.env.VIVID_ENABLED;
    }
}
// Export singleton instance
exports.vividPaymentService = new VividPaymentService();
//# sourceMappingURL=vividPaymentService.js.map