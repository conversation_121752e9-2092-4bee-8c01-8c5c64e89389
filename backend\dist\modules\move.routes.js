"use strict";
/**
 * Move Payment Routes
 *
 * Handles Move Payment Gateway processing endpoints
 * Provides API for creating payments, getting QR codes, and checking payment status
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = moveRoutes;
const movePaymentService_1 = require("../services/movePaymentService");
const logger_1 = require("../config/logger");
const Transaction_mongo_1 = __importDefault(require("../models/Transaction.mongo"));
const zod_1 = require("zod");
const moveLogger = logger_1.logger.child({ module: 'move-routes' });
// Validation schemas
const createPaymentSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
    amount: zod_1.z.number().positive().int(),
    moveId: zod_1.z.string().email().optional(),
    languageCode: zod_1.z.string().length(2).default('en'),
    successUrl: zod_1.z.string().url(),
    cancelUrl: zod_1.z.string().url(),
    payerName: zod_1.z.string().min(1),
    type: zod_1.z.enum(['qr', 'url']).optional().default('qr'), // 'qr' for QR code, 'url' for payment URL
});
const tokenParamsSchema = zod_1.z.object({
    token: zod_1.z.string().min(1),
});
const paymentUrlQuerySchema = zod_1.z.object({
    returnUrl: zod_1.z.string().url(),
});
const orderParamsSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
});
async function moveRoutes(fastify) {
    /**
     * Create Move payment
     */
    fastify.post('/move/payment', async (request, reply) => {
        try {
            console.log('=== RAW REQUEST BODY ===', JSON.stringify(request.body, null, 2));
            const validatedBody = createPaymentSchema.parse(request.body);
            moveLogger.info('Creating Move payment', {
                orderId: validatedBody.orderId,
                amount: validatedBody.amount,
                payerName: validatedBody.payerName,
            });
            // Check for existing payment (idempotency)
            const existingTransaction = await Transaction_mongo_1.default.findOne({
                'metadata.order_id': validatedBody.orderId,
                'metadata.payment_provider': 'move',
            });
            if (existingTransaction) {
                moveLogger.info('Returning existing Move payment', {
                    orderId: validatedBody.orderId,
                    transaction_id: existingTransaction._id,
                    token: existingTransaction.metadata.move_token,
                });
                // Handle different response types for existing transactions
                if (validatedBody.type === 'url') {
                    // For URL type, get the payment URL using the existing token
                    const paymentUrlResponse = await movePaymentService_1.movePaymentService.getPaymentUrl(existingTransaction.metadata.move_token, validatedBody.successUrl);
                    if (paymentUrlResponse.success && paymentUrlResponse.data) {
                        return reply.send({
                            success: true,
                            data: {
                                transaction_id: existingTransaction._id,
                                order_id: validatedBody.orderId,
                                token: existingTransaction.metadata.move_token,
                                expires_at: paymentUrlResponse.data.expires_at,
                                redirect_url: paymentUrlResponse.data.redirect_url,
                                amount: validatedBody.amount,
                                currency: 'EUR',
                                type: 'url',
                            },
                        });
                    }
                }
                // Default QR code response for existing transactions
                return reply.send({
                    success: true,
                    data: {
                        transaction_id: existingTransaction._id,
                        order_id: validatedBody.orderId,
                        token: existingTransaction.metadata.move_token,
                        expires_at: existingTransaction.metadata.expires_at,
                        redirect_url: existingTransaction.metadata.redirect_url,
                        amount: validatedBody.amount,
                        currency: 'EUR',
                        type: validatedBody.type || 'qr',
                    },
                });
            }
            // Create payment with Move
            console.log('=== CALLING MOVE PAYMENT SERVICE ===');
            const moveResponse = await movePaymentService_1.movePaymentService.createPayment(validatedBody);
            console.log('=== MOVE SERVICE RESPONSE ===', JSON.stringify(moveResponse, null, 2));
            if (!moveResponse.success) {
                moveLogger.warn('Move payment creation failed', {
                    orderId: validatedBody.orderId,
                    error: moveResponse.error,
                    message: moveResponse.message,
                });
                return reply.status(400).send({
                    success: false,
                    error: moveResponse.error || 'PAYMENT_CREATION_FAILED',
                    message: moveResponse.message || 'Failed to create Move payment',
                    details: moveResponse.details,
                });
            }
            // Save transaction to database
            console.log('=== SAVING TRANSACTION TO DATABASE ===');
            const transaction = new Transaction_mongo_1.default({
                amount: validatedBody.amount,
                currency: 'eur', // Move Payment typically uses EUR (lowercase for schema)
                status: 'pending',
                paymentMethod: 'move_payment',
                paymentProvider: 'move',
                metadata: {
                    payment_provider: 'move',
                    order_id: validatedBody.orderId,
                    move_token: moveResponse.data?.token,
                    payer_name: validatedBody.payerName,
                    language_code: validatedBody.languageCode,
                    success_url: validatedBody.successUrl,
                    cancel_url: validatedBody.cancelUrl,
                    expires_at: moveResponse.data?.expires_at,
                    redirect_url: moveResponse.data?.redirect_url,
                },
            });
            console.log('=== TRANSACTION OBJECT ===', JSON.stringify(transaction.toObject(), null, 2));
            try {
                await transaction.save();
                console.log('=== TRANSACTION SAVED SUCCESSFULLY ===');
            }
            catch (saveError) {
                console.log('=== DATABASE SAVE ERROR ===');
                console.log('Save Error:', saveError);
                console.log('=== END DATABASE SAVE ERROR ===');
                throw saveError;
            }
            moveLogger.info('Move payment created successfully', {
                orderId: validatedBody.orderId,
                transaction_id: transaction._id,
                token: moveResponse.data?.token,
                type: validatedBody.type,
            });
            // Handle different response types based on the 'type' parameter
            if (validatedBody.type === 'url') {
                // For URL type, get the payment URL using the token
                const paymentUrlResponse = await movePaymentService_1.movePaymentService.getPaymentUrl(moveResponse.data?.token || '', validatedBody.successUrl);
                if (paymentUrlResponse.success && paymentUrlResponse.data) {
                    return reply.send({
                        success: true,
                        data: {
                            transaction_id: transaction._id,
                            order_id: validatedBody.orderId,
                            token: moveResponse.data?.token,
                            expires_at: paymentUrlResponse.data.expires_at,
                            redirect_url: paymentUrlResponse.data.redirect_url,
                            amount: validatedBody.amount,
                            currency: 'EUR',
                            type: 'url',
                        },
                    });
                }
            }
            // Default QR code response (or fallback if URL generation fails)
            return reply.send({
                success: true,
                data: {
                    transaction_id: transaction._id,
                    order_id: validatedBody.orderId,
                    token: moveResponse.data?.token,
                    expires_at: moveResponse.data?.expires_at,
                    redirect_url: moveResponse.data?.redirect_url,
                    amount: validatedBody.amount,
                    currency: 'EUR',
                    type: validatedBody.type || 'qr',
                },
            });
        }
        catch (error) {
            moveLogger.error('Move payment creation failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid request data',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Get QR code for Move payment
     */
    fastify.get('/move/payment/:token/qr-code', async (request, reply) => {
        try {
            const { token } = tokenParamsSchema.parse(request.params);
            moveLogger.info('Getting Move payment QR code', { token });
            // Get QR code from Move
            const moveResponse = await movePaymentService_1.movePaymentService.getPaymentQRCode(token);
            if (!moveResponse.success) {
                moveLogger.warn('Move QR code retrieval failed', {
                    token,
                    error: moveResponse.error,
                    message: moveResponse.message,
                });
                return reply.status(400).send({
                    success: false,
                    error: moveResponse.error || 'QR_CODE_RETRIEVAL_FAILED',
                    message: moveResponse.message || 'Failed to get QR code',
                    details: moveResponse.details,
                });
            }
            moveLogger.info('Move QR code retrieved successfully', {
                token,
                expires_at: moveResponse.data?.expires_at,
            });
            return reply.send({
                success: true,
                data: moveResponse.data,
            });
        }
        catch (error) {
            moveLogger.error('Move QR code retrieval failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid token parameter',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Get payment URL for Move payment
     */
    fastify.get('/move/payment/:token/pay-url', async (request, reply) => {
        try {
            const { token } = tokenParamsSchema.parse(request.params);
            const { returnUrl } = paymentUrlQuerySchema.parse(request.query);
            moveLogger.info('Getting Move payment URL', { token, returnUrl });
            // Get payment URL from Move
            const moveResponse = await movePaymentService_1.movePaymentService.getPaymentUrl(token, returnUrl);
            if (!moveResponse.success) {
                moveLogger.warn('Move payment URL retrieval failed', {
                    token,
                    returnUrl,
                    error: moveResponse.error,
                    message: moveResponse.message,
                });
                return reply.status(400).send({
                    success: false,
                    error: moveResponse.error || 'PAYMENT_URL_FAILED',
                    message: moveResponse.message || 'Failed to get payment URL',
                });
            }
            moveLogger.info('Move payment URL retrieved successfully', {
                token,
                returnUrl,
                redirect_url: moveResponse.data?.redirect_url,
            });
            return reply.send({
                success: true,
                data: moveResponse.data,
            });
        }
        catch (error) {
            moveLogger.error('Move payment URL retrieval failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid parameters',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Get Move payment details and status
     */
    fastify.get('/move/payment/:token', async (request, reply) => {
        try {
            const { token } = tokenParamsSchema.parse(request.params);
            moveLogger.info('Getting Move payment details', { token });
            // Get payment details from Move
            const moveResponse = await movePaymentService_1.movePaymentService.getPaymentDetails(token);
            if (!moveResponse.success) {
                moveLogger.warn('Move payment details retrieval failed', {
                    token,
                    error: moveResponse.error,
                    message: moveResponse.message,
                });
                return reply.status(400).send({
                    success: false,
                    error: moveResponse.error || 'PAYMENT_DETAILS_RETRIEVAL_FAILED',
                    message: moveResponse.message || 'Failed to get payment details',
                    details: moveResponse.details,
                });
            }
            // Update transaction status if needed
            const transaction = await Transaction_mongo_1.default.findOne({
                'metadata.move_token': token,
                'metadata.payment_provider': 'move',
            });
            if (transaction) {
                // Update transaction with latest payment details
                transaction.metadata = {
                    ...transaction.metadata,
                    payment_details: moveResponse.data,
                    last_checked: new Date().toISOString(),
                };
                await transaction.save();
                moveLogger.info('Transaction updated with payment details', {
                    token,
                    transaction_id: transaction._id,
                    orderId: moveResponse.data?.orderId,
                });
            }
            moveLogger.info('Move payment details retrieved successfully', {
                token,
                orderId: moveResponse.data?.orderId,
                paymentId: moveResponse.data?.data.paymentId,
            });
            return reply.send({
                success: true,
                data: moveResponse.data,
            });
        }
        catch (error) {
            moveLogger.error('Move payment details retrieval failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid token parameter',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Get payment status by order ID
     */
    fastify.get('/move/payment-status/:orderId', async (request, reply) => {
        try {
            const { orderId } = orderParamsSchema.parse(request.params);
            moveLogger.info('Getting Move payment status by order ID', { orderId });
            // Find transaction by order ID
            const transaction = await Transaction_mongo_1.default.findOne({
                'metadata.order_id': orderId,
                'metadata.payment_provider': 'move',
            });
            if (!transaction) {
                return reply.status(404).send({
                    success: false,
                    error: 'TRANSACTION_NOT_FOUND',
                    message: 'Transaction not found for the given order ID.',
                });
            }
            moveLogger.info('Move payment status retrieved successfully', {
                orderId,
                transaction_id: transaction._id,
                status: transaction.status,
            });
            return reply.send({
                success: true,
                data: {
                    transaction_id: transaction._id,
                    order_id: orderId,
                    status: transaction.status,
                    amount: transaction.amount,
                    currency: transaction.currency,
                    created_at: transaction.createdAt,
                    updated_at: transaction.updatedAt,
                    metadata: transaction.metadata,
                },
            });
        }
        catch (error) {
            moveLogger.error('Move payment status retrieval failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid order ID parameter',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Health check for Move Payment service
     */
    fastify.get('/move/health', async (_request, reply) => {
        try {
            const isEnabled = movePaymentService_1.movePaymentService.isEnabled();
            return reply.send({
                success: true,
                data: {
                    service: 'Move Payment Gateway',
                    enabled: isEnabled,
                    status: isEnabled ? 'available' : 'disabled',
                    timestamp: new Date().toISOString(),
                },
            });
        }
        catch (error) {
            moveLogger.error('Move health check failed', error);
            return reply.status(500).send({
                success: false,
                error: 'HEALTH_CHECK_FAILED',
                message: 'Health check failed',
            });
        }
    });
}
//# sourceMappingURL=move.routes.js.map