import { FastifyRequest, FastifyReply } from 'fastify';
export declare const getConnectionToken: (_request: FastifyRequest, reply: FastifyReply) => Promise<never>;
export declare const createIntent: (request: FastifyRequest, reply: FastifyReply) => Promise<never>;
export declare const captureIntent: (request: FastifyRequest, reply: FastifyReply) => Promise<never>;
//# sourceMappingURL=stripeController.d.ts.map