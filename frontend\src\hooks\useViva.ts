/**
 * Viva Wallet React Hooks
 * 
 * React hooks for managing Viva Wallet payment flow
 * Provides payment creation, status polling, and utilities
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { 
  vivaPaymentService, 
  VivaPaymentRequest, 
  VivaPaymentResponse,
  VivaPaymentStatusResponse,
  VivaConfigResponse,
  VivaHealthResponse,
  VivaServiceResponse 
} from '../services/vivaPaymentService';

// Query keys for React Query
const queryKeys = {
  viva: {
    all: ['viva'] as const,
    config: () => [...queryKeys.viva.all, 'config'] as const,
    health: () => [...queryKeys.viva.all, 'health'] as const,
    paymentStatus: (orderCode: number) => [...queryKeys.viva.all, 'payment-status', orderCode] as const,
  },
};

/**
 * Hook for creating Viva Wallet payments
 */
export function useCreateVivaPayment() {
  return useMutation({
    mutationFn: async (params: VivaPaymentRequest): Promise<VivaServiceResponse<VivaPaymentResponse>> => {
      return await vivaPaymentService.createPayment(params);
    },
    onSuccess: (data) => {
      console.log('Viva Wallet payment created successfully:', data);
    },
    onError: (error) => {
      console.error('Viva Wallet payment creation failed:', error);
    },
  });
}

/**
 * Hook for cancelling Viva Wallet payments
 */
export function useCancelVivaPayment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (orderCode: number): Promise<VivaServiceResponse<{ orderCode: number; cancelled: boolean }>> => {
      return await vivaPaymentService.cancelPayment(orderCode);
    },
    onSuccess: (data, orderCode) => {
      console.log('Viva Wallet payment cancelled successfully:', data);
      // Invalidate payment status query for this order
      queryClient.invalidateQueries({ queryKey: queryKeys.viva.paymentStatus(orderCode) });
    },
    onError: (error) => {
      console.error('Viva Wallet payment cancellation failed:', error);
    },
  });
}

/**
 * Hook for getting Viva Wallet configuration
 */
export function useVivaConfig() {
  return useQuery({
    queryKey: queryKeys.viva.config(),
    queryFn: async (): Promise<VivaConfigResponse> => {
      const response = await vivaPaymentService.getConfig();
      if (!response.success || !response.data) {
        throw new Error(response.message || 'Failed to fetch Viva Wallet configuration');
      }
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
  });
}

/**
 * Hook for Viva Wallet health check
 */
export function useVivaHealth() {
  return useQuery({
    queryKey: queryKeys.viva.health(),
    queryFn: async (): Promise<VivaHealthResponse> => {
      const response = await vivaPaymentService.healthCheck();
      if (!response.success || !response.data) {
        throw new Error(response.message || 'Viva Wallet service is not available');
      }
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
  });
}

/**
 * Hook for managing Viva Wallet payment flow state
 */
export function useVivaPaymentFlow() {
  const queryClient = useQueryClient();

  const createPayment = useCreateVivaPayment();
  const cancelPayment = useCancelVivaPayment();

  const resetPaymentFlow = () => {
    // Clear any cached payment data
    queryClient.removeQueries({ queryKey: queryKeys.viva.all });
  };

  const isLoading = createPayment.isPending || cancelPayment.isPending;
  const error = createPayment.error || cancelPayment.error;

  return {
    createPayment: createPayment.mutateAsync,
    cancelPayment: cancelPayment.mutateAsync,
    resetPaymentFlow,
    isLoading,
    error,
    createPaymentResult: createPayment.data,
    cancelPaymentResult: cancelPayment.data,
  };
}

/**
 * Hook for polling Viva Wallet payment status until completion
 */
export function usePollVivaPaymentStatus(
  orderCode: number | null,
  enabled: boolean = true,
  onSuccess?: (data: VivaServiceResponse<VivaPaymentStatusResponse>) => void
) {
  return useQuery({
    queryKey: queryKeys.viva.paymentStatus(orderCode || 0),
    queryFn: async (): Promise<VivaPaymentStatusResponse> => {
      if (!orderCode) {
        throw new Error('Order code is required');
      }
      
      const response = await vivaPaymentService.getPaymentStatus(orderCode);
      if (!response.success || !response.data) {
        throw new Error(response.message || 'Failed to get payment status');
      }
      
      return response.data;
    },
    enabled: enabled && !!orderCode,
    refetchInterval: (query) => {
      // Stop polling if payment is completed or failed
      if (query?.state?.data) {
        const status = query.state.data.status;
        if (status === 'completed' || status === 'failed' || status === 'cancelled' || status === 'expired') {
          if (status === 'completed' && onSuccess) {
            onSuccess({ success: true, data: query.state.data });
          }
          return false; // Stop polling
        }
      }
      return 5000; // Poll every 5 seconds
    },
    staleTime: 0, // Always refetch
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
  });
}

/**
 * Hook for Viva Wallet payment utilities
 */
export function useVivaPaymentUtils() {
  return {
    generateOrderId: vivaPaymentService.generateOrderId,
    formatAmount: vivaPaymentService.formatAmount,
    getSuccessUrl: vivaPaymentService.getSuccessUrl,
    getCancelUrl: vivaPaymentService.getCancelUrl,
    isPaymentExpired: vivaPaymentService.isPaymentExpired,
    getTimeRemaining: vivaPaymentService.getTimeRemaining,
    formatTimeRemaining: vivaPaymentService.formatTimeRemaining,
    generatePaymentUrl: vivaPaymentService.generatePaymentUrl,
    mapStateIdToStatusText: vivaPaymentService.mapStateIdToStatusText,
    getStatusColor: vivaPaymentService.getStatusColor,
  };
}

/**
 * Hook for getting payment status with manual trigger
 */
export function useVivaPaymentStatus(orderCode: number | null) {
  return useQuery({
    queryKey: queryKeys.viva.paymentStatus(orderCode || 0),
    queryFn: async (): Promise<VivaPaymentStatusResponse> => {
      if (!orderCode) {
        throw new Error('Order code is required');
      }
      
      const response = await vivaPaymentService.getPaymentStatus(orderCode);
      if (!response.success || !response.data) {
        throw new Error(response.message || 'Failed to get payment status');
      }
      
      return response.data;
    },
    enabled: !!orderCode,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
  });
}

/**
 * Main Viva Wallet hook that combines all functionality
 */
export function useViva() {
  // Get configuration and health status
  const { data: config, isLoading: configLoading, error: configError } = useVivaConfig();
  const { data: health, isLoading: healthLoading, error: healthError } = useVivaHealth();

  // Payment flow management
  const paymentFlow = useVivaPaymentFlow();

  // Utilities
  const utils = useVivaPaymentUtils();

  // Payment status queries
  const usePaymentStatus = (orderCode: number | null, enabled: boolean = false) => {
    return useVivaPaymentStatus(orderCode);
  };

  const usePollPaymentStatus = (
    orderCode: number | null, 
    enabled: boolean = false,
    onSuccess?: (data: VivaServiceResponse<VivaPaymentStatusResponse>) => void
  ) => {
    return usePollVivaPaymentStatus(orderCode, enabled, onSuccess);
  };

  return {
    // Configuration and health
    config,
    health,
    isConfigLoading: configLoading,
    isHealthLoading: healthLoading,
    configError,
    healthError,
    
    // Payment operations
    createPayment: paymentFlow.createPayment,
    cancelPayment: paymentFlow.cancelPayment,
    resetPaymentFlow: paymentFlow.resetPaymentFlow,
    
    // Loading states
    isLoading: paymentFlow.isLoading,
    error: paymentFlow.error,
    
    // Results
    createPaymentResult: paymentFlow.createPaymentResult,
    cancelPaymentResult: paymentFlow.cancelPaymentResult,
    
    // Status queries
    usePaymentStatus,
    usePollPaymentStatus,
    
    // Utilities
    ...utils,
  };
}
