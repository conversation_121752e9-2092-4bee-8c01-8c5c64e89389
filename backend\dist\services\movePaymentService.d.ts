/**
 * Move Payment Service
 *
 * Service for handling Move Payment Gateway integration
 * Supports payment creation, QR code generation, and payment status tracking
 */
export interface MovePaymentRequest {
    orderId: string;
    amount: number;
    moveId?: string;
    languageCode?: string;
    successUrl: string;
    cancelUrl: string;
    payerName: string;
}
export interface MovePaymentResponse {
    expires_at?: string | null;
    redirect_url: string;
    token: string;
}
export interface MoveQRCodeResponse {
    expires_at?: string | null;
    redirect_url: string;
    token: string;
}
export interface MovePaymentDetails {
    orderId: string;
    amount: number;
    moveId: string;
    languageCode: string;
    data: {
        move_id: string;
        amount: number;
        type: string;
        avatar?: string;
        name: string;
        userExternalCode: string | null;
        paymentId: string;
        successUrl: string;
        cancelUrl: string;
    };
}
export interface MoveServiceResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
    details?: any;
}
export declare class MovePaymentService {
    private client;
    private apiUrl;
    private apiKey;
    private defaultMoveId;
    constructor();
    /**
     * Create a new Move payment
     */
    createPayment(params: MovePaymentRequest): Promise<MoveServiceResponse<MovePaymentResponse>>;
    /**
     * Get QR code for payment
     */
    getPaymentQRCode(token: string): Promise<MoveServiceResponse<MoveQRCodeResponse>>;
    /**
     * Get payment URL for a payment
     * This creates a payment URL that can be used to redirect users to the payment page
     */
    getPaymentUrl(token: string, returnUrl: string): Promise<MoveServiceResponse<MoveQRCodeResponse>>;
    /**
     * Get payment details and status
     */
    getPaymentDetails(token: string): Promise<MoveServiceResponse<MovePaymentDetails>>;
    /**
     * Check if Move Payment is enabled
     */
    isEnabled(): boolean;
}
export declare const movePaymentService: MovePaymentService;
//# sourceMappingURL=movePaymentService.d.ts.map