"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.defaultConfig = exports.ConfigManager = exports.CancelRequestSchema = exports.RefundRequestSchema = exports.PaymentRequestSchema = void 0;
const env_1 = require("./env");
const zod_1 = require("zod");
// Validation schemas for dynamic configuration
exports.PaymentRequestSchema = zod_1.z.object({
    amount: zod_1.z.number().min(env_1.env.MIN_AMOUNT).max(env_1.env.MAX_AMOUNT),
    currency: zod_1.z.string().length(3).default(env_1.env.DEFAULT_CURRENCY),
    payment_method_data: zod_1.z.object({
        type: zod_1.z.literal('card'),
        card: zod_1.z.object({
            number: zod_1.z.string().optional(),
            exp_month: zod_1.z.string().optional(),
            exp_year: zod_1.z.string().optional(),
            cvc: zod_1.z.string().optional(),
        }).optional(),
    }).optional(),
    confirm: zod_1.z.boolean().default(true),
    metadata: zod_1.z.record(zod_1.z.string()).optional(),
});
exports.RefundRequestSchema = zod_1.z.object({
    payment_intent_id: zod_1.z.string().startsWith('pi_'),
    amount: zod_1.z.number().positive().optional(),
    reason: zod_1.z.enum(['duplicate', 'fraudulent', 'requested_by_customer']).optional(),
});
exports.CancelRequestSchema = zod_1.z.object({
    payment_intent_id: zod_1.z.string().startsWith('pi_'),
    cancellation_reason: zod_1.z.enum(['duplicate', 'fraudulent', 'requested_by_customer', 'abandoned']).optional(),
});
// Default configuration from environment variables
const defaultConfig = {
    terminal: {
        id: env_1.env.TERMINAL_ID,
        model: 'Stripe WisePOS E',
        location: 'Store Location',
        timezone: 'America/New_York'
    },
    merchant: {
        id: env_1.env.MERCHANT_ID,
        name: 'Your Business Name',
        address: '123 Business St, City, State 12345',
        phone: '(*************',
        email: '<EMAIL>'
    },
    payment: {
        currency: env_1.env.DEFAULT_CURRENCY,
        timeout: env_1.env.PAYMENT_TIMEOUT,
        retryAttempts: env_1.env.RETRY_ATTEMPTS,
        minimumAmount: env_1.env.MIN_AMOUNT,
        maximumAmount: env_1.env.MAX_AMOUNT
    },
    receipt: {
        enabled: env_1.env.RECEIPT_ENABLED,
        copies: env_1.env.RECEIPT_COPIES,
        includeSignature: env_1.env.RECEIPT_SIGNATURE,
        footerText: env_1.env.RECEIPT_FOOTER
    },
    features: {
        cardReader: env_1.env.CARD_READER_ENABLED,
        printer: env_1.env.PRINTER_ENABLED,
        audio: env_1.env.AUDIO_ENABLED,
        vibration: env_1.env.VIBRATION_ENABLED,
        manualEntry: env_1.env.MANUAL_ENTRY_ENABLED
    },
    ui: {
        theme: env_1.env.UI_THEME,
        language: env_1.env.UI_LANGUAGE,
        fontSize: env_1.env.UI_FONT_SIZE,
        orientation: env_1.env.UI_ORIENTATION
    }
};
exports.defaultConfig = defaultConfig;
// Runtime configuration storage
let currentConfig = { ...defaultConfig };
// Configuration management class
class ConfigManager {
    static getConfig() {
        return { ...currentConfig };
    }
    static updateConfig(updates) {
        currentConfig = this.mergeDeep(currentConfig, updates);
    }
    static resetConfig() {
        currentConfig = { ...defaultConfig };
    }
    static getTerminalInfo() {
        return {
            id: currentConfig.terminal.id,
            model: currentConfig.terminal.model,
            location: currentConfig.terminal.location,
            timezone: currentConfig.terminal.timezone,
            merchantId: currentConfig.merchant.id,
            merchantName: currentConfig.merchant.name
        };
    }
    static getPaymentConfig() {
        return {
            currency: currentConfig.payment.currency,
            timeout: currentConfig.payment.timeout,
            retryAttempts: currentConfig.payment.retryAttempts,
            minimumAmount: currentConfig.payment.minimumAmount,
            maximumAmount: currentConfig.payment.maximumAmount
        };
    }
    static getReceiptConfig() {
        return {
            enabled: currentConfig.receipt.enabled,
            copies: currentConfig.receipt.copies,
            includeSignature: currentConfig.receipt.includeSignature,
            footerText: currentConfig.receipt.footerText
        };
    }
    static getFeatureFlags() {
        return { ...currentConfig.features };
    }
    static getUIConfig() {
        return { ...currentConfig.ui };
    }
    // Validation functions
    static validatePaymentRequest(data) {
        return exports.PaymentRequestSchema.parse(data);
    }
    static validateRefundRequest(data) {
        return exports.RefundRequestSchema.parse(data);
    }
    static validateCancelRequest(data) {
        return exports.CancelRequestSchema.parse(data);
    }
    // Amount validation
    static validateAmount(amount) {
        const config = this.getPaymentConfig();
        if (amount < config.minimumAmount) {
            return {
                valid: false,
                error: `Amount must be at least $${(config.minimumAmount / 100).toFixed(2)}`
            };
        }
        if (amount > config.maximumAmount) {
            return {
                valid: false,
                error: `Amount cannot exceed $${(config.maximumAmount / 100).toFixed(2)}`
            };
        }
        return { valid: true };
    }
    // Currency validation
    static validateCurrency(currency) {
        const supportedCurrencies = ['usd', 'eur', 'gbp', 'cad', 'aud'];
        return supportedCurrencies.includes(currency.toLowerCase());
    }
    // Generate transaction metadata
    static generateTransactionMetadata(additionalData) {
        const terminalInfo = this.getTerminalInfo();
        return {
            terminal_id: terminalInfo.id,
            merchant_id: terminalInfo.merchantId,
            location: terminalInfo.location,
            timestamp: new Date().toISOString(),
            ...additionalData
        };
    }
    // Deep merge utility
    static mergeDeep(target, source) {
        const output = { ...target };
        if (this.isObject(target) && this.isObject(source)) {
            Object.keys(source).forEach(key => {
                if (this.isObject(source[key])) {
                    if (!(key in target)) {
                        Object.assign(output, { [key]: source[key] });
                    }
                    else {
                        output[key] = this.mergeDeep(target[key], source[key]);
                    }
                }
                else {
                    Object.assign(output, { [key]: source[key] });
                }
            });
        }
        return output;
    }
    static isObject(item) {
        return item && typeof item === 'object' && !Array.isArray(item);
    }
    // Validate configuration
    static validateConfig(config) {
        const errors = [];
        if (config.payment) {
            if (config.payment.minimumAmount && config.payment.minimumAmount < 1) {
                errors.push('Minimum amount must be at least 1 cent');
            }
            if (config.payment.maximumAmount && config.payment.maximumAmount > 99999999) {
                errors.push('Maximum amount cannot exceed $999,999.99');
            }
            if (config.payment.timeout && (config.payment.timeout < 5000 || config.payment.timeout > 120000)) {
                errors.push('Payment timeout must be between 5 and 120 seconds');
            }
        }
        if (config.terminal?.id && !/^[A-Z0-9_]+$/.test(config.terminal.id)) {
            errors.push('Terminal ID must contain only uppercase letters, numbers, and underscores');
        }
        if (config.merchant?.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(config.merchant.email)) {
            errors.push('Invalid merchant email format');
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
    // Load configuration from external source (database, file, etc.)
    static async loadFromSource(source) {
        try {
            switch (source) {
                case 'database':
                    // Load from MongoDB
                    // const config = await ConfigModel.findOne({ type: 'terminal' });
                    // if (config) this.updateConfig(config.data);
                    break;
                case 'file':
                    // Load from JSON file
                    // const fs = await import('fs/promises');
                    // const configFile = await fs.readFile('./config.json', 'utf8');
                    // const config = JSON.parse(configFile);
                    // this.updateConfig(config);
                    break;
                case 'api':
                    // Load from remote API
                    // const response = await fetch('/api/config');
                    // const config = await response.json();
                    // this.updateConfig(config);
                    break;
            }
        }
        catch (error) {
            console.warn(`Failed to load configuration from ${source}:`, error);
        }
    }
    // Save configuration to external source
    static async saveToSource(source) {
        try {
            switch (source) {
                case 'database':
                    // Save to MongoDB
                    // await ConfigModel.updateOne(
                    //   { type: 'terminal' },
                    //   { data: currentConfig },
                    //   { upsert: true }
                    // );
                    break;
                case 'file':
                    // Save to JSON file
                    // const fs = await import('fs/promises');
                    // await fs.writeFile('./config.json', JSON.stringify(currentConfig, null, 2));
                    break;
                case 'api':
                    // Save to remote API
                    // await fetch('/api/config', {
                    //   method: 'POST',
                    //   headers: { 'Content-Type': 'application/json' },
                    //   body: JSON.stringify(currentConfig)
                    // });
                    break;
            }
        }
        catch (error) {
            console.error(`Failed to save configuration to ${source}:`, error);
        }
    }
}
exports.ConfigManager = ConfigManager;
exports.default = ConfigManager;
//# sourceMappingURL=dynamic.js.map