/**
 * Test Backend Viva Integration
 * 
 * Test the backend API endpoint to ensure it works with the updated OAuth2 implementation
 * Run with: node test-backend-viva.js
 */

const https = require('https');

console.log('🔐 Testing Backend Viva Wallet Integration');
console.log('=' .repeat(60));
console.log('');

async function testBackendVivaPayment() {
  return new Promise((resolve) => {
    console.log('📝 Testing Backend Payment Creation');
    console.log('🌐 Endpoint: http://localhost:3001/api/v1/viva/payment');
    console.log('');

    const paymentData = {
      orderId: 'TEST_ORDER_' + Date.now(),
      amount: 100, // €1.00 in cents
      currency: 'EUR',
      customerTrns: 'Backend integration test payment',
      payerName: 'Test Customer',
      payerEmail: '<EMAIL>',
      successUrl: 'http://localhost:3000/success',
      cancelUrl: 'http://localhost:3000/cancel'
    };

    const postData = JSON.stringify(paymentData);

    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/v1/viva/payment',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log('📤 Request Data:', JSON.stringify(paymentData, null, 2));
    console.log('');

    const req = https.request(options, (res) => {
      console.log(`📥 Response Status: ${res.statusCode} ${res.statusMessage}`);
      console.log('📥 Response Headers:', JSON.stringify(res.headers, null, 2));
      console.log('');

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log('📥 Response Body:', data || '(empty)');
        console.log('');

        try {
          const response = JSON.parse(data);
          
          if (res.statusCode === 200 && response.success) {
            console.log('🎉 SUCCESS! Backend Payment Creation Working');
            console.log('✅ Order Code:', response.data.orderCode);
            console.log('✅ Payment URL:', response.data.payment_url);
            console.log('✅ Checkout URL:', response.data.checkout_url);
            console.log('✅ Amount:', response.data.amount, 'cents');
            console.log('✅ Currency:', response.data.currency);
            console.log('✅ Status:', response.data.status);
            
            resolve({ success: true, data: response.data });
          } else {
            console.log('❌ Backend Payment Creation Failed');
            console.log('❌ Error:', response.error || 'Unknown error');
            console.log('❌ Message:', response.message || 'No message');
            console.log('❌ Details:', response.details || 'No details');
            
            resolve({ success: false, error: response });
          }
        } catch (parseError) {
          console.log('❌ Failed to parse response as JSON');
          console.log('❌ Parse error:', parseError.message);
          console.log('❌ Raw response:', data);
          
          resolve({ success: false, error: 'parse_error', rawResponse: data });
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Network Error:', error.message);
      console.log('💡 Make sure the backend server is running on port 3001');
      console.log('💡 Start it with: cd backend && npm run dev');
      
      resolve({ success: false, error: 'network_error', message: error.message });
    });

    req.write(postData);
    req.end();
  });
}

async function testBackendVivaConfig() {
  return new Promise((resolve) => {
    console.log('📝 Testing Backend Viva Configuration');
    console.log('🌐 Endpoint: http://localhost:3001/api/v1/viva/config');
    console.log('');

    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/v1/viva/config',
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    };

    const req = https.request(options, (res) => {
      console.log(`📥 Config Response Status: ${res.statusCode} ${res.statusMessage}`);

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log('📥 Config Response:', data || '(empty)');
        console.log('');

        try {
          const config = JSON.parse(data);
          
          if (res.statusCode === 200 && config.success) {
            console.log('✅ Backend Configuration:');
            console.log('   Environment:', config.data.environment);
            console.log('   Enabled:', config.data.enabled);
            console.log('   Currency:', config.data.currency);
            console.log('   Min Amount:', config.data.minAmount, 'cents');
            console.log('   Max Amount:', config.data.maxAmount, 'cents');
            console.log('');
            
            resolve({ success: true, config: config.data });
          } else {
            console.log('❌ Config request failed');
            resolve({ success: false });
          }
        } catch (e) {
          console.log('❌ Config parse error');
          resolve({ success: false });
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Config request error:', error.message);
      resolve({ success: false });
    });

    req.end();
  });
}

async function main() {
  console.log('🚀 Starting Backend Integration Tests');
  console.log('');

  // Test configuration first
  const configResult = await testBackendVivaConfig();
  
  if (configResult.success) {
    // Test payment creation
    const paymentResult = await testBackendVivaPayment();
    
    console.log('📋 FINAL SUMMARY');
    console.log('=' .repeat(60));
    
    if (paymentResult.success) {
      console.log('🎉 ALL TESTS PASSED!');
      console.log('✅ Backend configuration: Working');
      console.log('✅ Payment creation: Working');
      console.log('✅ OAuth2 integration: Working');
      console.log('✅ EUR currency: Supported');
      console.log('');
      console.log('🚀 Your Viva Wallet integration is fully functional!');
      console.log('💡 You can now use the frontend to create real payments.');
    } else {
      console.log('❌ PAYMENT CREATION FAILED');
      console.log('✅ Backend configuration: Working');
      console.log('❌ Payment creation: Failed');
      console.log('');
      console.log('🔧 Check the backend logs for more details.');
      console.log('💡 The OAuth2 credentials might need verification.');
    }
  } else {
    console.log('❌ BACKEND NOT ACCESSIBLE');
    console.log('💡 Make sure the backend server is running:');
    console.log('   cd backend && npm run dev');
  }
  
  console.log('');
  console.log('=' .repeat(60));
}

main();
