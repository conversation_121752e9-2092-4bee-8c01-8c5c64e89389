"use strict";
/**
 * Payoneer Payment Routes
 *
 * Handles Payoneer Checkout API processing endpoints
 * Provides API for creating payments, getting QR codes, and checking payment status
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = payoneerRoutes;
const payoneerPaymentService_1 = require("../services/payoneerPaymentService");
const logger_1 = require("../config/logger");
const Transaction_mongo_1 = __importDefault(require("../models/Transaction.mongo"));
const zod_1 = require("zod");
const payoneerLogger = logger_1.logger.child({ module: 'payoneer-routes' });
// Validation schemas
const createPaymentSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
    amount: zod_1.z.number().positive().int(),
    currency: zod_1.z.string().length(3).default('GBP'),
    successUrl: zod_1.z.string().url(),
    cancelUrl: zod_1.z.string().url(),
    payerName: zod_1.z.string().min(1),
    payerEmail: zod_1.z.string().email().optional(),
    languageCode: zod_1.z.string().length(2).default('en'),
    type: zod_1.z.enum(['qr', 'url']).optional().default('qr'), // 'qr' for QR code, 'url' for payment URL
});
const paymentStatusSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
});
const webhookSchema = zod_1.z.object({
    reference_id: zod_1.z.string().optional(),
    status: zod_1.z.string().optional(),
    transaction_id: zod_1.z.string().optional(),
    // Add other webhook fields as needed
});
async function payoneerRoutes(fastify) {
    /**
     * Create Payoneer payment
     */
    fastify.post('/payoneer/payment', async (request, reply) => {
        try {
            console.log('=== RAW REQUEST BODY ===', JSON.stringify(request.body, null, 2));
            const validatedBody = createPaymentSchema.parse(request.body);
            payoneerLogger.info('Creating Payoneer payment', {
                orderId: validatedBody.orderId,
                amount: validatedBody.amount,
                currency: validatedBody.currency,
                payerName: validatedBody.payerName,
            });
            // Check for existing payment (idempotency)
            const existingTransaction = await Transaction_mongo_1.default.findOne({
                'metadata.order_id': validatedBody.orderId,
                'metadata.payment_provider': 'payoneer',
            });
            if (existingTransaction) {
                payoneerLogger.info('Returning existing Payoneer payment', {
                    orderId: validatedBody.orderId,
                    transactionId: existingTransaction._id,
                });
                return reply.send({
                    success: true,
                    data: {
                        transaction_id: existingTransaction._id,
                        order_id: validatedBody.orderId,
                        checkout_id: existingTransaction.metadata?.checkout_id,
                        checkout_url: existingTransaction.metadata?.checkout_url,
                        qr_code_data: existingTransaction.metadata?.qr_code_data,
                        amount: validatedBody.amount,
                        currency: validatedBody.currency,
                        type: validatedBody.type || 'qr',
                    },
                });
            }
            // Create payment with Payoneer
            const payoneerResponse = await payoneerPaymentService_1.payoneerPaymentService.createPayment({
                orderId: validatedBody.orderId,
                amount: validatedBody.amount,
                currency: validatedBody.currency,
                successUrl: validatedBody.successUrl,
                cancelUrl: validatedBody.cancelUrl,
                payerName: validatedBody.payerName,
                payerEmail: validatedBody.payerEmail,
                languageCode: validatedBody.languageCode,
            });
            if (!payoneerResponse.success || !payoneerResponse.data) {
                payoneerLogger.warn('Payoneer payment creation failed', {
                    orderId: validatedBody.orderId,
                    error: payoneerResponse.error,
                    message: payoneerResponse.message,
                });
                return reply.status(400).send({
                    success: false,
                    error: payoneerResponse.error || 'PAYMENT_CREATION_FAILED',
                    message: payoneerResponse.message || 'Failed to create Payoneer payment',
                    details: payoneerResponse.details,
                });
            }
            payoneerLogger.info('Payoneer payment created successfully', {
                orderId: validatedBody.orderId,
                checkoutId: payoneerResponse.data.id,
                status: payoneerResponse.data.status,
            });
            // Save transaction to database
            console.log('=== SAVING TRANSACTION TO DATABASE ===');
            const transaction = new Transaction_mongo_1.default({
                amount: validatedBody.amount,
                currency: validatedBody.currency.toLowerCase(),
                status: 'pending',
                paymentMethod: 'payoneer_payment',
                paymentProvider: 'payoneer',
                metadata: {
                    payment_provider: 'payoneer',
                    order_id: validatedBody.orderId,
                    checkout_id: payoneerResponse.data.id,
                    payer_name: validatedBody.payerName,
                    payer_email: validatedBody.payerEmail,
                    language_code: validatedBody.languageCode,
                    success_url: validatedBody.successUrl,
                    cancel_url: validatedBody.cancelUrl,
                    checkout_url: payoneerResponse.data.checkout_url,
                    qr_code_data: payoneerResponse.data.checkout_url,
                    payoneer_status: payoneerResponse.data.status,
                },
            });
            const savedTransaction = await transaction.save();
            payoneerLogger.info('Transaction saved to database', {
                transactionId: savedTransaction._id,
                orderId: validatedBody.orderId,
            });
            // Return response based on type
            if (validatedBody.type === 'url' && payoneerResponse.data.checkout_url) {
                // For URL type, return the checkout URL for opening in new tab
                return reply.send({
                    success: true,
                    data: {
                        transaction_id: savedTransaction._id,
                        order_id: validatedBody.orderId,
                        checkout_id: payoneerResponse.data.id,
                        checkout_url: payoneerResponse.data.checkout_url,
                        amount: validatedBody.amount,
                        currency: validatedBody.currency,
                        type: 'url',
                    },
                });
            }
            // Default QR code response (or fallback if URL generation fails)
            return reply.send({
                success: true,
                data: {
                    transaction_id: savedTransaction._id,
                    order_id: validatedBody.orderId,
                    checkout_id: payoneerResponse.data.id,
                    checkout_url: payoneerResponse.data.checkout_url,
                    qr_code_data: payoneerResponse.data.checkout_url,
                    amount: validatedBody.amount,
                    currency: validatedBody.currency,
                    type: validatedBody.type || 'qr',
                },
            });
        }
        catch (error) {
            payoneerLogger.error('Payoneer payment creation failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid request data',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Get Payoneer payment status
     */
    fastify.get('/payoneer/payment/:orderId/status', async (request, reply) => {
        try {
            const { orderId } = request.params;
            payoneerLogger.info('Getting Payoneer payment status', { orderId });
            // Get status from Payoneer
            const statusResponse = await payoneerPaymentService_1.payoneerPaymentService.getPaymentStatus(orderId);
            if (!statusResponse.success) {
                return reply.status(400).send({
                    success: false,
                    error: statusResponse.error,
                    message: statusResponse.message,
                });
            }
            // Update transaction in database if needed
            const transaction = await Transaction_mongo_1.default.findOne({
                'metadata.order_id': orderId,
                'metadata.payment_provider': 'payoneer',
            });
            if (transaction && statusResponse.data) {
                const payoneerStatus = statusResponse.data.status;
                let transactionStatus = transaction.status;
                // Map Payoneer status to our transaction status
                if (payoneerStatus === 'completed' || payoneerStatus === 'approved') {
                    transactionStatus = 'success';
                }
                else if (payoneerStatus === 'failed' || payoneerStatus === 'cancelled' || payoneerStatus === 'declined') {
                    transactionStatus = 'failed';
                }
                else if (payoneerStatus === 'pending' || payoneerStatus === 'processing') {
                    transactionStatus = 'pending';
                }
                if (transactionStatus !== transaction.status) {
                    transaction.status = transactionStatus;
                    transaction.metadata = {
                        ...transaction.metadata,
                        payoneer_status: payoneerStatus,
                        payoneer_transaction_id: statusResponse.data.transaction_id,
                    };
                    await transaction.save();
                }
            }
            return reply.send({
                success: true,
                data: {
                    transaction_id: transaction?._id,
                    order_id: orderId,
                    status: transaction?.status || 'pending',
                    payoneer_status: statusResponse.data?.status,
                    amount: transaction?.amount,
                    currency: transaction?.currency,
                    created_at: transaction?.createdAt,
                    updated_at: transaction?.updatedAt,
                    metadata: statusResponse.data,
                },
            });
        }
        catch (error) {
            payoneerLogger.error('Failed to get Payoneer payment status', error);
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Failed to get payment status',
            });
        }
    });
    /**
     * Payoneer webhook endpoint
     */
    fastify.post('/payoneer/webhook', async (request, reply) => {
        try {
            payoneerLogger.info('Received Payoneer webhook', { body: request.body });
            const webhookData = request.body;
            const orderId = webhookData.reference_id;
            const status = webhookData.status;
            const transactionId = webhookData.transaction_id;
            if (!orderId) {
                payoneerLogger.warn('Webhook missing reference_id', { webhookData });
                return reply.status(400).send({ error: 'Missing reference_id' });
            }
            // Find and update transaction
            const transaction = await Transaction_mongo_1.default.findOne({
                'metadata.order_id': orderId,
                'metadata.payment_provider': 'payoneer',
            });
            if (!transaction) {
                payoneerLogger.warn('Transaction not found for webhook', { orderId });
                return reply.status(404).send({ error: 'Transaction not found' });
            }
            // Map Payoneer status to our transaction status
            let transactionStatus = transaction.status;
            if (status === 'completed' || status === 'approved') {
                transactionStatus = 'success';
            }
            else if (status === 'failed' || status === 'cancelled' || status === 'declined') {
                transactionStatus = 'failed';
            }
            else if (status === 'pending' || status === 'processing') {
                transactionStatus = 'pending';
            }
            // Update transaction
            transaction.status = transactionStatus;
            transaction.metadata = {
                ...transaction.metadata,
                payoneer_status: status,
                payoneer_transaction_id: transactionId,
                webhook_received_at: new Date().toISOString(),
            };
            await transaction.save();
            payoneerLogger.info('Transaction updated from webhook', {
                transactionId: transaction._id,
                orderId,
                status: transactionStatus,
                payoneerStatus: status,
            });
            return reply.send({ success: true });
        }
        catch (error) {
            payoneerLogger.error('Webhook processing failed', error);
            return reply.status(500).send({ error: 'Webhook processing failed' });
        }
    });
    /**
     * Payoneer health check
     */
    fastify.get('/payoneer/health', async (request, reply) => {
        try {
            const healthResponse = await payoneerPaymentService_1.payoneerPaymentService.healthCheck();
            if (healthResponse.success) {
                return reply.send({
                    success: true,
                    data: healthResponse.data,
                });
            }
            else {
                return reply.status(503).send({
                    success: false,
                    error: healthResponse.error,
                    message: healthResponse.message,
                });
            }
        }
        catch (error) {
            payoneerLogger.error('Health check failed', error);
            return reply.status(503).send({
                success: false,
                error: 'HEALTH_CHECK_FAILED',
                message: 'Payoneer service health check failed',
            });
        }
    });
}
//# sourceMappingURL=payoneer.routes.js.map