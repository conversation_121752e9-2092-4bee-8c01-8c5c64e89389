"use strict";
/**
 * Vivid Money Payment Routes
 *
 * Handles Vivid Money Payment Gateway processing endpoints
 * Provides API for creating payments, getting QR codes, and checking payment status
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = vividRoutes;
const zod_1 = require("zod");
const vividPaymentService_1 = require("../services/vividPaymentService");
const Transaction_mongo_1 = __importDefault(require("../models/Transaction.mongo"));
const logger_1 = require("../config/logger");
const vividLogger = (0, logger_1.createChildLogger)({ module: 'vivid-payment' });
// Validation schemas
const createPaymentSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
    amount: zod_1.z.number().positive(),
    currency: zod_1.z.string().default('GBP'),
    payerName: zod_1.z.string().min(1),
    payerEmail: zod_1.z.string().email().optional(),
    successUrl: zod_1.z.string().url(),
    cancelUrl: zod_1.z.string().url(),
    languageCode: zod_1.z.string().default('en'),
    type: zod_1.z.enum(['qr', 'url']).default('qr'),
});
const orderIdParamsSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
});
async function vividRoutes(fastify) {
    /**
     * Create Vivid Money payment
     */
    fastify.post('/vivid/payment', async (request, reply) => {
        try {
            console.log('=== RAW REQUEST BODY ===', JSON.stringify(request.body, null, 2));
            const validatedBody = createPaymentSchema.parse(request.body);
            vividLogger.info('Creating Vivid Money payment', {
                orderId: validatedBody.orderId,
                amount: validatedBody.amount,
                currency: validatedBody.currency,
                payerName: validatedBody.payerName,
            });
            // Check for existing payment (idempotency)
            const existingTransaction = await Transaction_mongo_1.default.findOne({
                'metadata.order_id': validatedBody.orderId,
                'metadata.payment_provider': 'vivid',
            });
            if (existingTransaction) {
                vividLogger.info('Returning existing Vivid Money payment', {
                    orderId: validatedBody.orderId,
                    transactionId: existingTransaction._id,
                });
                // Return existing payment data
                return reply.send({
                    success: true,
                    data: {
                        transaction_id: existingTransaction._id,
                        order_id: validatedBody.orderId,
                        payment_id: existingTransaction.metadata.vivid_payment_id,
                        payment_url: existingTransaction.metadata.payment_url,
                        amount: validatedBody.amount,
                        currency: validatedBody.currency,
                        type: validatedBody.type,
                    },
                });
            }
            // Create payment with Vivid Money
            const vividResponse = await vividPaymentService_1.vividPaymentService.createPayment({
                orderId: validatedBody.orderId,
                amount: validatedBody.amount,
                currency: validatedBody.currency,
                successUrl: validatedBody.successUrl,
                cancelUrl: validatedBody.cancelUrl,
                payerName: validatedBody.payerName,
                payerEmail: validatedBody.payerEmail,
                languageCode: validatedBody.languageCode,
            });
            if (!vividResponse.success) {
                vividLogger.warn('Vivid Money payment creation failed', {
                    orderId: validatedBody.orderId,
                    error: vividResponse.error,
                    message: vividResponse.message,
                });
                return reply.status(400).send({
                    success: false,
                    error: vividResponse.error || 'PAYMENT_CREATION_FAILED',
                    message: vividResponse.message || 'Failed to create Vivid Money payment',
                    details: vividResponse.details,
                });
            }
            // Create transaction record
            const transaction = new Transaction_mongo_1.default({
                amount: validatedBody.amount,
                currency: validatedBody.currency,
                status: 'pending',
                payment_method: 'vivid',
                metadata: {
                    order_id: validatedBody.orderId,
                    payment_provider: 'vivid',
                    vivid_payment_id: vividResponse.data.id,
                    payment_url: vividResponse.data.payment_url,
                    payer_name: validatedBody.payerName,
                    payer_email: validatedBody.payerEmail,
                    success_url: validatedBody.successUrl,
                    cancel_url: validatedBody.cancelUrl,
                    language_code: validatedBody.languageCode,
                    created_at: new Date().toISOString(),
                },
            });
            const savedTransaction = await transaction.save();
            vividLogger.info('Transaction saved to database', {
                transactionId: savedTransaction._id,
                orderId: validatedBody.orderId,
            });
            // Return response based on type
            if (validatedBody.type === 'url' && vividResponse.data.payment_url) {
                // For URL type, return the payment URL for opening in new tab
                return reply.send({
                    success: true,
                    data: {
                        transaction_id: savedTransaction._id,
                        order_id: validatedBody.orderId,
                        payment_id: vividResponse.data.id,
                        redirect_url: vividResponse.data.payment_url,
                        amount: validatedBody.amount,
                        currency: validatedBody.currency,
                        type: 'url',
                    },
                });
            }
            // Default QR code response (or fallback if URL generation fails)
            return reply.send({
                success: true,
                data: {
                    transaction_id: savedTransaction._id,
                    order_id: validatedBody.orderId,
                    payment_id: vividResponse.data.id,
                    payment_url: vividResponse.data.payment_url,
                    qr_code_data: vividResponse.data.qr_code || vividResponse.data.payment_url,
                    amount: validatedBody.amount,
                    currency: validatedBody.currency,
                    type: validatedBody.type || 'qr',
                },
            });
        }
        catch (error) {
            vividLogger.error('Vivid Money payment creation failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid request data',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Get Vivid Money payment status
     */
    fastify.get('/vivid/payment/:orderId/status', async (request, reply) => {
        try {
            const { orderId } = orderIdParamsSchema.parse(request.params);
            vividLogger.info('Getting Vivid Money payment status', { orderId });
            // Get payment status from Vivid Money
            const vividResponse = await vividPaymentService_1.vividPaymentService.getPaymentStatus(orderId);
            if (!vividResponse.success) {
                vividLogger.warn('Vivid Money payment status retrieval failed', {
                    orderId,
                    error: vividResponse.error,
                    message: vividResponse.message,
                });
                return reply.status(400).send({
                    success: false,
                    error: vividResponse.error || 'STATUS_RETRIEVAL_FAILED',
                    message: vividResponse.message || 'Failed to get payment status',
                    details: vividResponse.details,
                });
            }
            return reply.send({
                success: true,
                data: vividResponse.data,
            });
        }
        catch (error) {
            vividLogger.error('Vivid Money payment status retrieval failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid request parameters',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Vivid Money health check
     */
    fastify.get('/vivid/health', async (request, reply) => {
        try {
            vividLogger.info('Vivid Money health check requested');
            const healthResponse = await vividPaymentService_1.vividPaymentService.healthCheck();
            if (!healthResponse.success) {
                return reply.status(503).send({
                    success: false,
                    error: healthResponse.error || 'HEALTH_CHECK_FAILED',
                    message: healthResponse.message || 'Vivid Money service is not healthy',
                });
            }
            return reply.send({
                success: true,
                data: healthResponse.data,
            });
        }
        catch (error) {
            vividLogger.error('Vivid Money health check failed', error);
            return reply.status(500).send({
                success: false,
                error: 'HEALTH_CHECK_FAILED',
                message: 'Vivid Money health check failed',
            });
        }
    });
    /**
     * Vivid Money webhook handler
     */
    fastify.post('/vivid/webhook', async (request, reply) => {
        try {
            vividLogger.info('Vivid Money webhook received', { body: request.body });
            // Handle Vivid Money webhook events here
            // This is a placeholder implementation
            return reply.send({
                success: true,
                message: 'Webhook received',
            });
        }
        catch (error) {
            vividLogger.error('Vivid Money webhook processing failed', error);
            return reply.status(500).send({
                success: false,
                error: 'WEBHOOK_PROCESSING_FAILED',
                message: 'Failed to process webhook',
            });
        }
    });
}
//# sourceMappingURL=vivid.routes.js.map