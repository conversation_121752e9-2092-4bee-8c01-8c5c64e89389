"use strict";
/**
 * Novalnet Payment Service
 *
 * Handles Novalnet API communication for payment processing
 * Supports inline payment form integration, 3D Secure, and tokenization
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.novalnetService = void 0;
const axios_1 = __importDefault(require("axios"));
const crypto_1 = __importDefault(require("crypto"));
const env_1 = require("../config/env");
const logger_1 = require("../config/logger");
const zod_1 = require("zod");
const novalnetLogger = logger_1.logger.child({ module: 'novalnet-service' });
const tokenizePaymentSchema = zod_1.z.object({
    payment_ref: zod_1.z.object({
        token: zod_1.z.string(),
    }),
    amount: zod_1.z.number().positive().int(),
    currency: zod_1.z.string().length(3).default('USD'),
    test_mode: zod_1.z.boolean().default(true),
    order_no: zod_1.z.string(),
    customer: zod_1.z.object({
        first_name: zod_1.z.string(),
        last_name: zod_1.z.string(),
        email: zod_1.z.string().email(),
        customer_ip: zod_1.z.string().ip(),
    }),
});
class NovalnetService {
    constructor() {
        this.accessKey = env_1.env.NOVALNET_PAYMENT_ACCESS_KEY;
        this.activationKey = env_1.env.NOVALNET_ACTIVATION_KEY;
        this.tariffId = env_1.env.NOVALNET_TARIFF_ID;
        // Debug logging
        novalnetLogger.info('Novalnet credentials loaded', {
            accessKey: this.accessKey.substring(0, 8) + '...',
            activationKey: this.activationKey.substring(0, 8) + '...',
            tariffId: this.tariffId,
        });
        this.client = axios_1.default.create({
            baseURL: env_1.env.NOVALNET_API_URL,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'charset': 'utf-8',
                'Accept': 'application/json',
            },
        });
        // Add request interceptor for authentication
        this.client.interceptors.request.use((config) => {
            // Base64 encode the Payment Access Key as per Novalnet documentation
            const encodedAccessKey = Buffer.from(this.accessKey).toString('base64');
            // Set headers exactly as per Novalnet documentation
            config.headers['Content-Type'] = 'application/json';
            config.headers['Accept'] = 'application/json';
            config.headers['Charset'] = 'utf-8';
            config.headers['X-NN-Access-Key'] = encodedAccessKey;
            // Debug logging
            novalnetLogger.info('Novalnet request headers', {
                url: config.url,
                headers: {
                    'Content-Type': config.headers['Content-Type'],
                    'X-NN-Access-Key': encodedAccessKey.substring(0, 10) + '...',
                }
            });
            return config;
        });
        // Add response interceptor for logging
        this.client.interceptors.response.use((response) => {
            novalnetLogger.info('Novalnet API response received', {
                status: response.status,
                statusText: response.statusText,
            });
            return response;
        }, (error) => {
            novalnetLogger.error('Novalnet API error', {
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data,
            });
            return Promise.reject(error);
        });
    }
    /**
     * Create a hosted payment page for overlay integration (Step 1)
     */
    async createPayment(params) {
        try {
            novalnetLogger.info('Creating Novalnet overlay payment', {
                amount: params.amount,
                currency: params.currency,
                order_no: params.order_no,
            });
            // Build request data for overlay integration (exact format from documentation)
            const requestData = {
                merchant: {
                    signature: this.activationKey, // API signature
                    tariff: this.tariffId,
                },
                customer: {
                    first_name: params.customer.first_name,
                    last_name: params.customer.last_name,
                    email: params.customer.email,
                    customer_ip: params.customer.customer_ip,
                    customer_no: params.customer.customer_no || Date.now(), // Generate if not provided
                    billing: {
                        house_no: params.customer.billing.house_no,
                        street: params.customer.billing.street,
                        city: params.customer.billing.city,
                        zip: params.customer.billing.zip,
                        country_code: params.customer.billing.country_code,
                    },
                },
                transaction: {
                    amount: params.amount,
                    currency: params.currency,
                    test_mode: params.test_mode ? 1 : 0, // Use 1/0 instead of boolean
                    order_no: params.order_no,
                    return_url: params.return_url,
                    error_return_url: params.error_return_url,
                    hook_url: params.hook_url,
                    create_token: 1
                },
                hosted_page: {
                // Don't hide any payments - let users choose
                // Don't hide blocks - show all information
                // Don't skip pages - show full flow
                },
                custom: {
                    lang: "EN"
                }
            };
            const response = await this.client.post('/seamless/payment', requestData);
            novalnetLogger.info('Novalnet overlay payment created successfully', {
                status: response.data.result?.status,
                txn_secret: response.data.transaction?.txn_secret,
                redirect_url: response.data.result?.redirect_url,
            });
            return response.data;
        }
        catch (error) {
            novalnetLogger.error('Failed to create Novalnet overlay payment', error);
            throw new Error('Failed to create payment');
        }
    }
    /**
     * Verify overlay response (Step 3)
     */
    async verifyOverlayResponse(params) {
        try {
            if (!params.checksum || !params.tid || !params.txn_secret || !params.status) {
                return {
                    isValid: false,
                    message: 'Missing required parameters for verification'
                };
            }
            // Build token string: tid + txn_secret + status + reverse(payment_access_key)
            const tokenString = params.tid + params.txn_secret + params.status + this.accessKey.split('').reverse().join('');
            // Generate SHA256 checksum
            const crypto = require('crypto');
            const generatedChecksum = crypto.createHash('sha256').update(tokenString).digest('hex');
            const isValid = generatedChecksum === params.checksum;
            novalnetLogger.info('Overlay response verification', {
                tid: params.tid,
                status: params.status,
                isValid,
            });
            return {
                isValid,
                message: isValid ? 'Verification successful' : 'Checksum verification failed'
            };
        }
        catch (error) {
            novalnetLogger.error('Failed to verify overlay response', error);
            return {
                isValid: false,
                message: 'Verification error'
            };
        }
    }
    /**
     * Process payment with saved token (for repeat customers)
     */
    async processTokenPayment(params) {
        try {
            const validatedParams = tokenizePaymentSchema.parse({
                payment_ref: {
                    token: params.token,
                },
                amount: params.amount,
                currency: params.currency || 'USD',
                test_mode: params.test_mode ?? true,
                order_no: params.order_no,
                customer: params.customer,
            });
            novalnetLogger.info('Processing Novalnet token payment', {
                amount: validatedParams.amount,
                currency: validatedParams.currency,
                order_no: validatedParams.order_no,
            });
            const requestData = {
                merchant: {
                    signature: this.activationKey,
                    tariff: this.tariffId,
                },
                transaction: {
                    payment_type: 'CREDITCARD',
                    amount: validatedParams.amount,
                    currency: validatedParams.currency,
                    test_mode: validatedParams.test_mode ? 1 : 0,
                    order_no: validatedParams.order_no,
                },
                ...validatedParams,
            };
            const response = await this.client.post('/payment', requestData);
            novalnetLogger.info('Novalnet token payment processed successfully', {
                status: response.data.result?.status,
                tid: response.data.transaction?.tid,
            });
            return response.data;
        }
        catch (error) {
            novalnetLogger.error('Failed to process Novalnet token payment', error);
            throw new Error('Failed to process token payment');
        }
    }
    /**
     * Get transaction details
     */
    async getTransactionDetails(tid) {
        try {
            novalnetLogger.info('Fetching Novalnet transaction details', { tid });
            const requestData = {
                merchant: {
                    signature: this.activationKey,
                },
                transaction: {
                    tid,
                },
            };
            const response = await this.client.post('/transaction/details', requestData);
            novalnetLogger.info('Novalnet transaction details fetched successfully', {
                tid,
                status: response.data.transaction?.status,
            });
            return response.data;
        }
        catch (error) {
            novalnetLogger.error('Failed to fetch Novalnet transaction details', error);
            throw new Error('Failed to fetch transaction details');
        }
    }
    /**
     * Verify webhook signature
     */
    verifyWebhookSignature(payload, receivedSignature) {
        try {
            const expectedSignature = crypto_1.default
                .createHmac('sha256', env_1.env.NOVALNET_WEBHOOK_SECRET)
                .update(payload)
                .digest('hex');
            return crypto_1.default.timingSafeEqual(Buffer.from(receivedSignature, 'hex'), Buffer.from(expectedSignature, 'hex'));
        }
        catch (error) {
            novalnetLogger.error('Failed to verify webhook signature', error);
            return false;
        }
    }
}
exports.novalnetService = new NovalnetService();
//# sourceMappingURL=novalnetService.js.map