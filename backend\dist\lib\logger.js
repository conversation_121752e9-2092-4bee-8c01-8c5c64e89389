"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = void 0;
const pino_1 = __importDefault(require("pino"));
const env_1 = require("../config/env");
const pinoOptions = {
    level: env_1.env.LOG_LEVEL || 'info',
};
// Use pino-pretty for local development for more readable logs
if (env_1.env.NODE_ENV !== 'production' && env_1.env.NODE_ENV !== 'test') {
    pinoOptions.transport = {
        target: 'pino-pretty',
        options: {
            colorize: true,
            translateTime: 'SYS:standard', // More readable timestamp
            ignore: 'pid,hostname', // Exclude pid and hostname from pretty print
        },
    };
}
exports.logger = (0, pino_1.default)(pinoOptions);
// Example of creating a child logger for a specific module/context
// export const transactionLogger = logger.child({ module: 'transactions' });
// Make sure to export the main logger instance
exports.default = exports.logger;
//# sourceMappingURL=logger.js.map