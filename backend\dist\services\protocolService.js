"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.protocolService = exports.ProtocolService = void 0;
const axios_1 = __importDefault(require("axios"));
const env_1 = require("../config/env");
const logger_1 = require("../config/logger");
const zod_1 = require("zod");
const protocolLogger = logger_1.logger.child({ module: 'protocol-service' });
const protocolMessageSchema = zod_1.z.object({
    protocolEventCode: zod_1.z.enum(['101.1', '101.3', '101.5', '101.8']),
    transactionId: zod_1.z.string(),
    stripePaymentIntentId: zod_1.z.string().optional(),
    amount: zod_1.z.number().positive(),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
});
class ProtocolService {
    generateSTAN() {
        return Math.floor(Math.random() * 999999).toString().padStart(6, '0');
    }
    generateRRN() {
        return Math.floor(Math.random() * 999999999999).toString().padStart(12, '0');
    }
    formatTime() {
        const now = new Date();
        return now.toTimeString().slice(0, 8).replace(/:/g, '');
    }
    formatDate() {
        const now = new Date();
        return (now.getMonth() + 1).toString().padStart(2, '0') +
            now.getDate().toString().padStart(2, '0');
    }
    createProtocolMessage(params) {
        const now = new Date();
        const baseMessage = {
            protocolEventCode: params.protocolEventCode,
            mti: this.getMTIForProtocol(params.protocolEventCode),
            transactionId: params.transactionId,
            stripePaymentIntentId: params.stripePaymentIntentId,
            timestamp: now.toISOString(),
            de4_transactionAmount: params.amount,
            de11_stan: this.generateSTAN(),
            de12_localTime: this.formatTime(),
            de13_localDate: this.formatDate(),
            de18_merchantCategoryCode: '5999',
            de37_retrievalReferenceNumber: this.generateRRN(),
            de41_cardAcceptorTerminalId: 'PAX920PRO',
            de42_cardAcceptorIdCode: 'MERCHANT001',
        };
        switch (params.protocolEventCode) {
            case '101.1':
                baseMessage.de3_processingCode = '000000';
                break;
            case '101.3':
                baseMessage.de3_processingCode = '000000';
                baseMessage.de38_approvalCode = Math.floor(Math.random() * 999999).toString().padStart(6, '0');
                break;
            case '101.5':
                baseMessage.de3_processingCode = '200000';
                break;
            case '101.8':
                baseMessage.de3_processingCode = '920000';
                break;
        }
        return baseMessage;
    }
    getMTIForProtocol(protocolCode) {
        switch (protocolCode) {
            case '101.1': return '0100';
            case '101.3': return '0220';
            case '101.5': return '0400';
            case '101.8': return '0500';
            default: return '0200';
        }
    }
    async sendProtocolMessage(params) {
        try {
            const validatedParams = protocolMessageSchema.parse(params);
            protocolLogger.info({
                protocolCode: validatedParams.protocolEventCode,
                transactionId: validatedParams.transactionId
            }, 'Sending protocol message to bank');
            const message = this.createProtocolMessage(validatedParams);
            const response = await axios_1.default.post(env_1.env.MOCK_BANK_URL, message, {
                timeout: 10000,
                headers: {
                    'Content-Type': 'application/json',
                    'X-Protocol-Version': '1.0',
                },
            });
            protocolLogger.info({
                protocolCode: validatedParams.protocolEventCode,
                transactionId: validatedParams.transactionId,
                bankResponseCode: response.data?.data?.bankResponseCode
            }, 'Received response from bank');
            return response.data;
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                protocolLogger.error({ error: error.errors }, 'Invalid protocol message parameters');
                throw new Error('Invalid protocol message parameters');
            }
            if (axios_1.default.isAxiosError(error)) {
                protocolLogger.error({
                    error: error.message,
                    status: error.response?.status,
                    data: error.response?.data
                }, 'Bank communication error');
                throw new Error('Failed to communicate with bank');
            }
            protocolLogger.error({ error }, 'Unexpected error sending protocol message');
            throw new Error('Failed to send protocol message');
        }
    }
    getProtocolDescription(code) {
        switch (code) {
            case '101.1': return 'Authorization Request';
            case '101.3': return 'Capture Request';
            case '101.5': return 'Reversal Request';
            case '101.8': return 'Settlement Request';
            default: return 'Unknown Protocol';
        }
    }
}
exports.ProtocolService = ProtocolService;
exports.protocolService = new ProtocolService();
//# sourceMappingURL=protocolService.js.map