"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateReceipt = exports.ReceiptService = void 0;
const logger_1 = require("../config/logger");
class ReceiptService {
    static generateReceipt(transaction, customerCopy = true) {
        try {
            const receiptData = {
                transactionId: transaction._id?.toString() || '',
                amount: transaction.amount,
                status: transaction.status,
                timestamp: new Date(transaction.createdAt || new Date()),
                paymentMethod: 'Credit Card',
                cardLast4: '****',
                authCode: this.generateAuthCode(),
                protocolCode: transaction.protocolCode,
                merchantInfo: this.defaultMerchantInfo,
                customerCopy
            };
            return this.formatReceipt(receiptData);
        }
        catch (error) {
            logger_1.logger.error('Error generating receipt:', error);
            throw new Error('Failed to generate receipt');
        }
    }
    static generateMerchantReceipt(transaction) {
        return this.generateReceipt(transaction, false);
    }
    static generateCustomerReceipt(transaction) {
        return this.generateReceipt(transaction, true);
    }
    static formatReceipt(data) {
        const width = this.printerConfig.width;
        const lines = [];
        // Header
        lines.push(this.centerText('', width, '='));
        lines.push(this.centerText(data.merchantInfo.name.toUpperCase(), width));
        lines.push(this.centerText(data.merchantInfo.address, width));
        lines.push(this.centerText(`${data.merchantInfo.city}, ${data.merchantInfo.state} ${data.merchantInfo.zipCode}`, width));
        lines.push(this.centerText(data.merchantInfo.phone, width));
        if (data.merchantInfo.website) {
            lines.push(this.centerText(data.merchantInfo.website, width));
        }
        lines.push(this.centerText('', width, '='));
        lines.push('');
        // Transaction details
        lines.push(this.leftRightText('Date:', this.formatDate(data.timestamp), width));
        lines.push(this.leftRightText('Time:', this.formatTime(data.timestamp), width));
        lines.push(this.leftRightText('Trans ID:', data.transactionId.slice(-8), width));
        if (data.authCode) {
            lines.push(this.leftRightText('Auth Code:', data.authCode, width));
        }
        if (data.protocolCode) {
            lines.push(this.leftRightText('Protocol:', data.protocolCode, width));
        }
        lines.push('');
        // Payment details
        lines.push(this.centerText('PAYMENT DETAILS', width, '-'));
        lines.push(this.leftRightText('Payment Method:', data.paymentMethod || 'Credit Card', width));
        if (data.cardLast4) {
            lines.push(this.leftRightText('Card:', `****${data.cardLast4}`, width));
        }
        lines.push('');
        // Amount
        lines.push(this.centerText('', width, '-'));
        lines.push(this.leftRightText('AMOUNT:', `$${(data.amount / 100).toFixed(2)}`, width));
        lines.push(this.centerText('', width, '-'));
        lines.push('');
        // Status
        const statusText = data.status === 'success' ? 'APPROVED' : 'DECLINED';
        lines.push(this.centerText(`*** ${statusText} ***`, width));
        lines.push('');
        // Copy type
        const copyType = data.customerCopy ? 'CUSTOMER COPY' : 'MERCHANT COPY';
        lines.push(this.centerText(copyType, width));
        lines.push('');
        // Footer
        if (data.status === 'success') {
            lines.push(this.centerText('Thank you for your business!', width));
            lines.push(this.centerText('Please keep this receipt', width));
            lines.push(this.centerText('for your records', width));
        }
        else {
            lines.push(this.centerText('Transaction declined', width));
            lines.push(this.centerText('Please try another payment method', width));
        }
        lines.push('');
        lines.push(this.centerText('', width, '='));
        lines.push('');
        return lines.join('\n');
    }
    static centerText(text, width, fillChar = ' ') {
        if (text.length >= width)
            return text.substring(0, width);
        const padding = width - text.length;
        const leftPad = Math.floor(padding / 2);
        const rightPad = padding - leftPad;
        return fillChar.repeat(leftPad) + text + fillChar.repeat(rightPad);
    }
    static leftRightText(left, right, width) {
        const totalTextLength = left.length + right.length;
        if (totalTextLength >= width) {
            return (left + right).substring(0, width);
        }
        const spaces = width - totalTextLength;
        return left + ' '.repeat(spaces) + right;
    }
    static formatDate(date) {
        return date.toLocaleDateString('en-US', {
            month: '2-digit',
            day: '2-digit',
            year: 'numeric'
        });
    }
    static formatTime(date) {
        return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true
        });
    }
    static generateAuthCode() {
        return Math.random().toString(36).substring(2, 8).toUpperCase();
    }
}
exports.ReceiptService = ReceiptService;
ReceiptService.defaultMerchantInfo = {
    name: 'Demo Store',
    address: '123 Main Street',
    city: 'Anytown',
    state: 'ST',
    zipCode: '12345',
    phone: '(*************',
    email: '<EMAIL>',
    website: 'www.demostore.com',
    taxId: 'TAX123456789'
};
ReceiptService.printerConfig = {
    width: 32,
    paperType: 'thermal',
    encoding: 'utf8'
};
// Legacy function for backward compatibility
const generateReceipt = (txn) => {
    return ReceiptService.generateReceipt(txn);
};
exports.generateReceipt = generateReceipt;
//# sourceMappingURL=receiptService.js.map