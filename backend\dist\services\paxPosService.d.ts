/**
 * PAX POS Service
 * Handles integration with PAX A920 terminal hardware
 * Provides web-compatible interface for cloud deployment
 */
export interface PaxPaymentRequest {
    amount: number;
    tenderType?: 'CREDIT' | 'DEBIT' | 'CASH';
    transType?: 'SALE' | 'REFUND' | 'VOID';
    referenceNumber?: string;
    items?: Array<{
        name: string;
        price: number;
        quantity: number;
    }>;
}
export interface PaxPaymentResponse {
    success: boolean;
    transactionId?: string;
    authCode?: string;
    resultCode?: string;
    message?: string;
    receiptData?: {
        customer: string;
        merchant: string;
    };
    cardInfo?: {
        last4?: string;
        brand?: string;
        entryMethod?: string;
    };
}
export interface PaxTerminalStatus {
    connected: boolean;
    ip?: string;
    port?: number;
    model?: string;
    serialNumber?: string;
    capabilities?: {
        contactless: boolean;
        emv: boolean;
        magneticStripe: boolean;
        printer: boolean;
    };
    lastResponse?: any;
}
export interface PaxConfiguration {
    terminal: {
        ip: string;
        port: number;
        timeout: number;
    };
    merchant: {
        id: string;
        name: string;
    };
    features: {
        receiptEnabled: boolean;
        signatureRequired: boolean;
    };
}
declare class PaxPosService {
    private isSimulationMode;
    private terminalConfig;
    constructor();
    /**
     * Determine if we should use simulation mode
     */
    private shouldUseSimulation;
    /**
     * Process a payment transaction
     */
    processPayment(request: PaxPaymentRequest): Promise<PaxPaymentResponse>;
    /**
     * Get terminal status
     */
    getTerminalStatus(): Promise<PaxTerminalStatus>;
    /**
     * Test terminal connection
     */
    testConnection(): Promise<{
        success: boolean;
        message: string;
    }>;
    /**
     * Cancel current transaction
     */
    cancelTransaction(): Promise<{
        success: boolean;
        message: string;
    }>;
    /**
     * Get terminal configuration
     */
    getConfiguration(): PaxConfiguration;
    /**
     * Simulate payment for development/cloud environments
     */
    private simulatePayment;
    /**
     * Get simulated terminal status
     */
    private getSimulatedStatus;
    /**
     * Generate simulated receipt
     */
    private generateSimulatedReceipt;
    /**
     * Process payment with actual hardware (placeholder)
     */
    private processHardwarePayment;
    /**
     * Get actual hardware status (placeholder)
     */
    private getHardwareStatus;
    /**
     * Test actual hardware connection (placeholder)
     */
    private testHardwareConnection;
    /**
     * Cancel actual hardware transaction (placeholder)
     */
    private cancelHardwareTransaction;
}
export declare const paxPosService: PaxPosService;
export default paxPosService;
//# sourceMappingURL=paxPosService.d.ts.map