"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.transactionService = exports.TransactionService = void 0;
const Transaction_mongo_1 = __importDefault(require("../models/Transaction.mongo"));
const logger_1 = require("../config/logger");
const zod_1 = require("zod");
const transactionLogger = logger_1.logger.child({ module: 'transaction-service' });
const createTransactionSchema = zod_1.z.object({
    amount: zod_1.z.number().positive(),
    status: zod_1.z.enum(['success', 'failure', 'pending']),
    protocolCode: zod_1.z.string().optional(),
    stripePaymentIntentId: zod_1.z.string().optional(),
    receiptUrl: zod_1.z.string().url().optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
});
class TransactionService {
    async createTransaction(data) {
        try {
            const validatedData = createTransactionSchema.parse(data);
            const now = new Date().toISOString();
            transactionLogger.info({ amount: validatedData.amount, status: validatedData.status }, 'Creating transaction');
            const transaction = new Transaction_mongo_1.default({
                ...validatedData,
                createdAt: now,
                updatedAt: now,
            });
            await transaction.save();
            transactionLogger.info({ transactionId: transaction._id }, 'Transaction created successfully');
            return transaction;
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                transactionLogger.error({ error: error.errors }, 'Invalid transaction data');
                throw new Error('Invalid transaction data');
            }
            transactionLogger.error({ error }, 'Failed to create transaction');
            throw new Error('Failed to create transaction');
        }
    }
    async getTransactions(limit = 50, offset = 0) {
        try {
            transactionLogger.info({ limit, offset }, 'Fetching transactions');
            const transactions = await Transaction_mongo_1.default
                .find()
                .sort({ createdAt: -1 })
                .limit(limit)
                .skip(offset)
                .lean();
            return transactions;
        }
        catch (error) {
            transactionLogger.error({ error }, 'Failed to fetch transactions');
            throw new Error('Failed to fetch transactions');
        }
    }
    async getTransactionById(id) {
        try {
            transactionLogger.info({ transactionId: id }, 'Fetching transaction by ID');
            const transaction = await Transaction_mongo_1.default.findById(id).lean();
            return transaction;
        }
        catch (error) {
            transactionLogger.error({ error, transactionId: id }, 'Failed to fetch transaction');
            throw new Error('Failed to fetch transaction');
        }
    }
    async updateTransaction(id, updates) {
        try {
            transactionLogger.info({ transactionId: id }, 'Updating transaction');
            const transaction = await Transaction_mongo_1.default.findByIdAndUpdate(id, { ...updates, updatedAt: new Date().toISOString() }, { new: true, runValidators: true }).lean();
            if (transaction) {
                transactionLogger.info({ transactionId: id }, 'Transaction updated successfully');
            }
            return transaction;
        }
        catch (error) {
            transactionLogger.error({ error, transactionId: id }, 'Failed to update transaction');
            throw new Error('Failed to update transaction');
        }
    }
}
exports.TransactionService = TransactionService;
exports.transactionService = new TransactionService();
//# sourceMappingURL=transactionService.js.map