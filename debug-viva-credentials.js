/**
 * Debug Viva Wallet Credentials
 * 
 * This script helps debug the exact credentials being sent to Viva
 * Run with: node debug-viva-credentials.js
 */

// Your Viva Wallet credentials
const VIVA_CLIENT_ID = '00pp9ggt8otvtzyfy3sv7y0d5u56oleukdkd7mma293z8.apps.vivapayments.com';
const VIVA_CLIENT_SECRET = '09ka9hqw5Pqgo744Uk1H9n7b30U3fR';
const VIVA_MERCHANT_ID = '30481af3-63d9-42cd-93ea-1937a972b76d';
const VIVA_API_KEY = 'SothunZ2FxVRMkq666sbxbxB6VNbJG';

console.log('🔍 DEBUGGING VIVA WALLET CREDENTIALS');
console.log('=' .repeat(60));
console.log('');

// Debug Client ID and Secret
console.log('📋 OAuth 2.0 Credentials:');
console.log('Client ID:', VIVA_CLIENT_ID);
console.log('Client ID Length:', VIVA_CLIENT_ID.length);
console.log('Client Secret:', VIVA_CLIENT_SECRET);
console.log('Client Secret Length:', VIVA_CLIENT_SECRET.length);
console.log('');

// Debug Basic Auth credentials
console.log('📋 Basic Auth Credentials:');
console.log('Merchant ID:', VIVA_MERCHANT_ID);
console.log('Merchant ID Length:', VIVA_MERCHANT_ID.length);
console.log('API Key:', VIVA_API_KEY);
console.log('API Key Length:', VIVA_API_KEY.length);
console.log('');

// Test Base64 encoding
console.log('🔒 Base64 Encoding Tests:');
console.log('');

// OAuth2 credentials
const oauthCredentials = `${VIVA_CLIENT_ID}:${VIVA_CLIENT_SECRET}`;
const oauthBase64 = Buffer.from(oauthCredentials).toString('base64');
console.log('OAuth2 Raw String:', oauthCredentials);
console.log('OAuth2 Raw Length:', oauthCredentials.length);
console.log('OAuth2 Base64:', oauthBase64);
console.log('OAuth2 Base64 Length:', oauthBase64.length);
console.log('');

// Basic Auth credentials
const basicCredentials = `${VIVA_MERCHANT_ID}:${VIVA_API_KEY}`;
const basicBase64 = Buffer.from(basicCredentials).toString('base64');
console.log('Basic Auth Raw String:', basicCredentials);
console.log('Basic Auth Raw Length:', basicCredentials.length);
console.log('Basic Auth Base64:', basicBase64);
console.log('Basic Auth Base64 Length:', basicBase64.length);
console.log('');

// Test decoding to verify
console.log('🔓 Base64 Decoding Verification:');
console.log('');

const decodedOAuth = Buffer.from(oauthBase64, 'base64').toString('utf8');
const decodedBasic = Buffer.from(basicBase64, 'base64').toString('utf8');

console.log('Decoded OAuth2:', decodedOAuth);
console.log('OAuth2 Match:', decodedOAuth === oauthCredentials ? '✅' : '❌');
console.log('');

console.log('Decoded Basic:', decodedBasic);
console.log('Basic Match:', decodedBasic === basicCredentials ? '✅' : '❌');
console.log('');

// Check for any hidden characters or encoding issues
console.log('🔍 Character Analysis:');
console.log('');

console.log('Client ID Characters:');
for (let i = 0; i < VIVA_CLIENT_ID.length; i++) {
  const char = VIVA_CLIENT_ID[i];
  const code = char.charCodeAt(0);
  if (code < 32 || code > 126) {
    console.log(`  Position ${i}: '${char}' (Code: ${code}) ⚠️ Non-printable`);
  }
}

console.log('Client Secret Characters:');
for (let i = 0; i < VIVA_CLIENT_SECRET.length; i++) {
  const char = VIVA_CLIENT_SECRET[i];
  const code = char.charCodeAt(0);
  if (code < 32 || code > 126) {
    console.log(`  Position ${i}: '${char}' (Code: ${code}) ⚠️ Non-printable`);
  }
}

console.log('');
console.log('🎯 CURL Command for Testing:');
console.log('');
console.log('You can test this manually with curl:');
console.log('');
console.log(`curl -L -X POST 'https://demo-accounts.vivapayments.com/connect/token' \\`);
console.log(`  -H 'Content-Type: application/x-www-form-urlencoded' \\`);
console.log(`  -H 'Authorization: Basic ${oauthBase64}' \\`);
console.log(`  --data-urlencode 'grant_type=client_credentials'`);
console.log('');

console.log('🎯 Alternative CURL Command (Form Parameters):');
console.log('');
console.log(`curl -L -X POST 'https://demo-accounts.vivapayments.com/connect/token' \\`);
console.log(`  -H 'Content-Type: application/x-www-form-urlencoded' \\`);
console.log(`  --data-urlencode 'grant_type=client_credentials' \\`);
console.log(`  --data-urlencode 'client_id=${VIVA_CLIENT_ID}' \\`);
console.log(`  --data-urlencode 'client_secret=${VIVA_CLIENT_SECRET}'`);
console.log('');

console.log('📝 POSSIBLE ISSUES TO CHECK:');
console.log('');
console.log('1. ✅ Client Secret is correct (you just copied it)');
console.log('2. ❓ Client ID might be wrong or inactive');
console.log('3. ❓ Credentials might be for production, not demo');
console.log('4. ❓ Smart Checkout might not be enabled');
console.log('5. ❓ Account might need additional verification');
console.log('');

console.log('📞 NEXT STEPS:');
console.log('');
console.log('1. Try the curl commands above manually');
console.log('2. Double-check the Client ID in Viva dashboard');
console.log('3. Verify you\'re in the demo environment');
console.log('4. Check if Smart Checkout is enabled');
console.log('5. Contact Viva support if needed');
console.log('');
console.log('=' .repeat(60));
