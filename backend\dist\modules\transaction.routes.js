"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = default_1;
const transactionController_1 = require("../controllers/transactionController");
async function default_1(app) {
    app.post('/transaction', transactionController_1.createTxn);
    app.get('/transaction', transactionController_1.listTxns);
    app.get('/transaction/:id', transactionController_1.getTxnById);
}
//# sourceMappingURL=transaction.routes.js.map