export declare function createApp(): Promise<import("fastify").FastifyInstance<import("http").Server<typeof import("http").IncomingMessage, typeof import("http").ServerResponse>, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, import("pino").Logger<never>, import("fastify").FastifyTypeProviderDefault>>;
export declare function startServer(): Promise<import("fastify").FastifyInstance<import("http").Server<typeof import("http").IncomingMessage, typeof import("http").ServerResponse>, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, import("pino").Logger<never>, import("fastify").FastifyTypeProviderDefault>>;
//# sourceMappingURL=app.d.ts.map