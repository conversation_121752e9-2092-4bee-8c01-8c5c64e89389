/**
 * Comprehensive Viva Wallet Authentication Test
 *
 * Tests both OAuth 2.0 and Basic Auth methods as per official Viva documentation
 *
 * OAuth 2.0 (IdentityServer) - Used for Smart Checkout payment creation
 * Basic Auth - Used for payment status, cancellation, and other operations
 *
 * Run with: node test-viva-auth.js
 */

const https = require('https');
const querystring = require('querystring');

// Your Viva Wallet credentials
const VIVA_CLIENT_ID = '00pp9ggt8otvtzyfy3sv7y0d5u56oleukdkd7mma293z8.apps.vivapayments.com';
const VIVA_CLIENT_SECRET = 'QMusPDEy8xF34j9r5V9yOqS7yeM2pM'; // Updated with correct Client Secret
const VIVA_MERCHANT_ID = '30481af3-63d9-42cd-93ea-1937a972b76d';
const VIVA_API_KEY = 'SothunZ2FxVRMkq666sbxbxB6VNbJG';

// Demo environment URLs (as per official documentation)
const ACCOUNTS_URL = 'demo-accounts.vivapayments.com';
const API_URL = 'demo-api.vivapayments.com';
const CHECKOUT_URL = 'demo.vivapayments.com';

console.log('🔐 Comprehensive Viva Wallet Authentication Test');
console.log('📚 Based on official Viva Wallet documentation');
console.log('🌐 Environment: Demo');
console.log('');

// Display credentials (masked for security)
console.log('📋 Credentials:');
console.log('Client ID:', VIVA_CLIENT_ID);
console.log('Client Secret:', VIVA_CLIENT_SECRET.substring(0, 10) + '...');
console.log('Merchant ID:', VIVA_MERCHANT_ID);
console.log('API Key:', VIVA_API_KEY.substring(0, 10) + '...');
console.log('');

// ========================================
// OAuth 2.0 Authentication Tests
// ========================================

/**
 * Step 1: OAuth 2.0 Authentication (Method 1 - Base64 Encoded Format)
 *
 * As per Viva documentation:
 * "Using this method, you must first Base64-encode your credentials
 * in the format Client_ID:Client_Secret"
 */
function testOAuth2AuthenticationBasic() {
  return new Promise((resolve) => {
    console.log('� OAuth 2.0 Authentication Test');
    console.log('📝 Method 1: Base64-encoded credentials in Authorization header');
    console.log('📍 Endpoint: https://demo-accounts.vivapayments.com/connect/token');
    console.log('');

    console.log('🔑 Credentials:');
    console.log('Client ID:', VIVA_CLIENT_ID);
    console.log('Client Secret:', VIVA_CLIENT_SECRET.substring(0, 10) + '...');
    console.log('');

    // Step 1: Base64-encode credentials in format Client_ID:Client_Secret
    const credentials = Buffer.from(`${VIVA_CLIENT_ID}:${VIVA_CLIENT_SECRET}`).toString('base64');
    console.log('🔒 Base64 Encoded Credentials:', credentials.substring(0, 50) + '...');
    console.log('');

    // Step 2: Prepare request data
    const postData = querystring.stringify({
      grant_type: 'client_credentials'
    });

    // Step 3: Configure request options
    const options = {
      hostname: ACCOUNTS_URL,
      port: 443,
      path: '/connect/token',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${credentials}`,
        'Accept': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log('📤 Request Configuration:');
    console.log('URL:', `https://${options.hostname}${options.path}`);
    console.log('Method:', options.method);
    console.log('Headers:', JSON.stringify(options.headers, null, 2));
    console.log('Body:', postData);
    console.log('');

    // Step 4: Make the request
    console.log('🚀 Sending OAuth 2.0 token request...');
    const req = https.request(options, (res) => {
      console.log(`📥 Response Status: ${res.statusCode}`);
      console.log('📥 Response Headers:', JSON.stringify(res.headers, null, 2));
      console.log('');

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log('📥 Raw Response Body:', data);
        console.log('');

        try {
          const response = JSON.parse(data);

          if (res.statusCode === 200 && response.access_token) {
            console.log('🎉 OAuth 2.0 Authentication SUCCESSFUL!');
            console.log('✅ Access Token:', response.access_token.substring(0, 50) + '...');
            console.log('✅ Token Type:', response.token_type);
            console.log('✅ Expires In:', response.expires_in, 'seconds (1 hour)');
            console.log('✅ Scope:', response.scope);
            console.log('');

            resolve(response.access_token);
          } else {
            console.log('❌ OAuth 2.0 Authentication FAILED');
            console.log('❌ Error:', response.error || 'Unknown error');
            console.log('❌ Error Description:', response.error_description || 'No description');
            console.log('');
            resolve(null);
          }
        } catch (error) {
          console.log('❌ Failed to parse JSON response');
          console.log('❌ Raw response:', data);
          console.log('');
          resolve(null);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Network Error:', error.message);
      resolve(null);
    });

    req.write(postData);
    req.end();
  });
}

/**
 * Step 2: OAuth 2.0 Authentication (Method 2 - Form Parameters)
 *
 * Alternative method: Pass credentials as parameters in HTTP call
 */
function testOAuth2AuthenticationForm() {
  return new Promise((resolve) => {
    console.log('� OAuth 2.0 Authentication Test');
    console.log('📝 Method 2: Credentials as form parameters');
    console.log('📍 Endpoint: https://demo-accounts.vivapayments.com/connect/token');
    console.log('');

    // Prepare the request data with credentials in form
    const postData = querystring.stringify({
      grant_type: 'client_credentials',
      client_id: VIVA_CLIENT_ID,
      client_secret: VIVA_CLIENT_SECRET
    });

    // Request options
    const options = {
      hostname: ACCOUNTS_URL,
      port: 443,
      path: '/connect/token',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log('📤 Request Configuration:');
    console.log('URL:', `https://${options.hostname}${options.path}`);
    console.log('Method:', options.method);
    console.log('Headers:', JSON.stringify(options.headers, null, 2));
    console.log('Body:', postData.replace(VIVA_CLIENT_SECRET, 'HIDDEN_SECRET'));
    console.log('');

    console.log('🚀 Sending OAuth 2.0 token request (form method)...');
    const req = https.request(options, (res) => {
      console.log(`📥 Response Status: ${res.statusCode}`);
      console.log('📥 Response Headers:', JSON.stringify(res.headers, null, 2));
      console.log('');

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log('📥 Raw Response Body:', data);
        console.log('');

        try {
          const response = JSON.parse(data);

          if (res.statusCode === 200 && response.access_token) {
            console.log('🎉 OAuth 2.0 Authentication SUCCESSFUL!');
            console.log('✅ Access Token:', response.access_token.substring(0, 50) + '...');
            console.log('✅ Token Type:', response.token_type);
            console.log('✅ Expires In:', response.expires_in, 'seconds (1 hour)');
            console.log('✅ Scope:', response.scope);
            console.log('');

            resolve(response.access_token);
          } else {
            console.log('❌ OAuth 2.0 Authentication FAILED');
            console.log('❌ Error:', response.error || 'Unknown error');
            console.log('❌ Error Description:', response.error_description || 'No description');
            console.log('');
            resolve(null);
          }
        } catch (error) {
          console.log('❌ Failed to parse JSON response');
          console.log('❌ Raw response:', data);
          console.log('');
          resolve(null);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Network Error:', error.message);
      resolve(null);
    });

    req.write(postData);
    req.end();
  });
}

// ========================================
// Basic Auth Authentication Tests
// ========================================

/**
 * Basic Auth Test - Used for payment status, cancellation, and other operations
 *
 * Uses Merchant ID and API Key for authentication
 */
function testBasicAuth() {
  return new Promise((resolve) => {
    console.log('🔐 Basic Auth Authentication Test');
    console.log('📝 Used for: Payment status, cancellation, refunds, etc.');
    console.log('📍 Test Endpoint: https://demo.vivapayments.com/api/orders/{orderCode}');
    console.log('');

    console.log('🔑 Credentials:');
    console.log('Merchant ID:', VIVA_MERCHANT_ID);
    console.log('API Key:', VIVA_API_KEY.substring(0, 10) + '...');
    console.log('');

    // Create Basic Auth credentials (Merchant_ID:API_Key)
    const credentials = Buffer.from(`${VIVA_MERCHANT_ID}:${VIVA_API_KEY}`).toString('base64');
    console.log('🔒 Base64 Encoded Credentials:', credentials.substring(0, 50) + '...');
    console.log('');

    // Test with a dummy order code (this will return 404 but confirms auth works)
    const testOrderCode = '1234567890123456';

    // Request options
    const options = {
      hostname: CHECKOUT_URL,
      port: 443,
      path: `/api/orders/${testOrderCode}`,
      method: 'GET',
      headers: {
        'Authorization': `Basic ${credentials}`,
        'Accept': 'application/json'
      }
    };

    console.log('📤 Request Configuration:');
    console.log('URL:', `https://${options.hostname}${options.path}`);
    console.log('Method:', options.method);
    console.log('Headers:', JSON.stringify(options.headers, null, 2));
    console.log('');

    console.log('🚀 Sending Basic Auth test request...');
    const req = https.request(options, (res) => {
      console.log(`📥 Response Status: ${res.statusCode}`);
      console.log('📥 Response Headers:', JSON.stringify(res.headers, null, 2));
      console.log('');

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log('📥 Raw Response Body:', data || '(empty)');
        console.log('');

        if (res.statusCode === 401) {
          console.log('❌ Basic Auth FAILED - Invalid credentials');
          console.log('❌ Check your Merchant ID and API Key');
          resolve(false);
        } else if (res.statusCode === 404) {
          console.log('🎉 Basic Auth SUCCESSFUL!');
          console.log('✅ Order not found (expected - dummy order code)');
          console.log('✅ Authentication credentials are valid');
          resolve(true);
        } else if (res.statusCode === 200) {
          console.log('🎉 Basic Auth SUCCESSFUL!');
          console.log('✅ Unexpected success - order exists');
          resolve(true);
        } else {
          console.log('⚠️ Basic Auth - Unexpected response');
          console.log('ℹ️ Status:', res.statusCode);
          resolve(true); // Assume auth is working if not 401
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Network Error:', error.message);
      resolve(false);
    });

    req.end();
  });
}

// Step 2: Test Payment Order Creation
function testPaymentOrderCreation(accessToken) {
  return new Promise((resolve, reject) => {
    console.log('📝 Step 2: Testing Payment Order Creation');
    console.log('Access Token:', accessToken.substring(0, 50) + '...');
    console.log('API URL:', API_URL);
    console.log('');

    // Prepare payment order data
    const paymentData = {
      amount: 100, // £1.00 in pence
      customerTrns: 'Test payment from Node.js script',
      customer: {
        email: '<EMAIL>',
        fullName: 'Test Customer',
        requestLang: 'en-GB'
      },
      sourceCode: 'Default',
      merchantTrns: 'TEST_ORDER_' + Date.now(),
      currencyCode: '826', // GBP currency code
      paymentTimeout: 1800
    };

    const postData = JSON.stringify(paymentData);

    // Request options
    const options = {
      hostname: API_URL,
      port: 443,
      path: '/checkout/v2/orders',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log('Request Options:', JSON.stringify(options, null, 2));
    console.log('Payment Data:', JSON.stringify(paymentData, null, 2));
    console.log('');

    const req = https.request(options, (res) => {
      console.log(`✅ Response Status: ${res.statusCode}`);
      console.log('Response Headers:', JSON.stringify(res.headers, null, 2));
      console.log('');

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log('Raw Response:', data);
        console.log('');

        try {
          const response = JSON.parse(data);
          
          if (res.statusCode === 200 && response.orderCode) {
            console.log('🎉 Payment Order Creation Successful!');
            console.log('Order Code:', response.orderCode);
            console.log('Checkout URL: https://demo.vivapayments.com/web/checkout?ref=' + response.orderCode);
            console.log('');
            
            resolve(response);
          } else {
            console.log('❌ Payment Order Creation Failed');
            console.log('Error Response:', response);
            reject(new Error(`Payment creation failed: ${JSON.stringify(response)}`));
          }
        } catch (error) {
          console.log('❌ Failed to parse response as JSON');
          console.log('Raw response:', data);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Request Error:', error.message);
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

// Step 3: Test Basic Auth (for payment status)
function testBasicAuth() {
  return new Promise((resolve, reject) => {
    console.log('📝 Step 3: Testing Basic Auth for Payment Status');
    console.log('Merchant ID:', VIVA_MERCHANT_ID);
    console.log('API Key:', VIVA_API_KEY.substring(0, 10) + '...');
    console.log('');

    // Create Basic Auth credentials
    const credentials = Buffer.from(`${VIVA_MERCHANT_ID}:${VIVA_API_KEY}`).toString('base64');
    console.log('Basic Auth Credentials:', credentials.substring(0, 50) + '...');

    // Test with a dummy order code (this will fail but we can see the auth response)
    const testOrderCode = '1234567890123456';

    // Request options
    const options = {
      hostname: 'demo.vivapayments.com',
      port: 443,
      path: `/api/orders/${testOrderCode}`,
      method: 'GET',
      headers: {
        'Authorization': `Basic ${credentials}`,
        'Accept': 'application/json'
      }
    };

    console.log('Request Options:', JSON.stringify(options, null, 2));
    console.log('');

    const req = https.request(options, (res) => {
      console.log(`✅ Response Status: ${res.statusCode}`);
      console.log('Response Headers:', JSON.stringify(res.headers, null, 2));
      console.log('');

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log('Raw Response:', data);
        console.log('');

        if (res.statusCode === 401) {
          console.log('❌ Basic Auth Failed - Invalid credentials');
          reject(new Error('Basic Auth failed'));
        } else if (res.statusCode === 404) {
          console.log('✅ Basic Auth Successful (Order not found is expected)');
          resolve(true);
        } else {
          console.log('✅ Basic Auth appears to be working');
          resolve(true);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Request Error:', error.message);
      reject(error);
    });

    req.end();
  });
}

// ========================================
// Main Test Execution
// ========================================

async function main() {
  try {
    console.log('🚀 COMPREHENSIVE VIVA WALLET AUTHENTICATION TEST');
    console.log('=' .repeat(60));
    console.log('📚 Based on official Viva Wallet documentation');
    console.log('🌐 Environment: Demo');
    console.log('📅 Date:', new Date().toISOString());
    console.log('=' .repeat(60));
    console.log('');

    // ========================================
    // Phase 1: Basic Auth Test
    // ========================================
    console.log('🔵 PHASE 1: Basic Auth Authentication');
    console.log('─'.repeat(40));
    const basicAuthResult = await testBasicAuth();
    console.log('');

    // ========================================
    // Phase 2: OAuth 2.0 Tests
    // ========================================
    console.log('🔵 PHASE 2: OAuth 2.0 Authentication');
    console.log('─'.repeat(40));

    // Try Method 1: Base64 encoded credentials
    let accessToken = await testOAuth2AuthenticationBasic();

    if (!accessToken) {
      console.log('🔄 Trying OAuth 2.0 Method 2...');
      console.log('─'.repeat(40));
      // Try Method 2: Form parameters
      accessToken = await testOAuth2AuthenticationForm();
    }

    console.log('');

    // ========================================
    // Phase 3: Payment Creation Test (if OAuth2 works)
    // ========================================
    if (accessToken) {
      console.log('🔵 PHASE 3: Payment Order Creation');
      console.log('─'.repeat(40));
      await testPaymentOrderCreation(accessToken);
      console.log('');
    }

    // ========================================
    // Final Results Summary
    // ========================================
    console.log('🔵 FINAL RESULTS SUMMARY');
    console.log('=' .repeat(60));

    if (basicAuthResult) {
      console.log('✅ Basic Auth: WORKING');
      console.log('   ├─ Merchant ID: Valid');
      console.log('   ├─ API Key: Valid');
      console.log('   └─ Use for: Payment status, cancellation, refunds');
    } else {
      console.log('❌ Basic Auth: FAILED');
      console.log('   └─ Check Merchant ID and API Key');
    }

    if (accessToken) {
      console.log('✅ OAuth 2.0: WORKING');
      console.log('   ├─ Client ID: Valid');
      console.log('   ├─ Client Secret: Valid');
      console.log('   ├─ Access Token: Obtained');
      console.log('   └─ Use for: Payment creation (Smart Checkout)');

      console.log('✅ Payment Creation: READY');
      console.log('   └─ Can create payment orders');
    } else {
      console.log('❌ OAuth 2.0: FAILED');
      console.log('   ├─ Client ID: Check validity');
      console.log('   ├─ Client Secret: Check validity');
      console.log('   └─ Cannot create payments until fixed');
    }

    console.log('');
    console.log('🔵 NEXT STEPS');
    console.log('─'.repeat(40));

    if (accessToken && basicAuthResult) {
      console.log('🎉 ALL AUTHENTICATION METHODS WORKING!');
      console.log('✅ Your Viva Wallet integration is ready');
      console.log('✅ Update your TypeScript service with these credentials');
      console.log('✅ Both payment creation and status checking will work');
    } else if (basicAuthResult && !accessToken) {
      console.log('⚠️ PARTIAL SUCCESS - OAuth 2.0 needs fixing');
      console.log('✅ Basic Auth works (payment status/cancellation)');
      console.log('❌ OAuth 2.0 failed (payment creation)');
      console.log('');
      console.log('🔧 TO FIX OAUTH 2.0:');
      console.log('1. Log into: https://demo.vivapayments.com/');
      console.log('2. Go to: Settings > API Access > Smart Checkout Credentials');
      console.log('3. Find Client ID:', VIVA_CLIENT_ID);
      console.log('4. Click on Client ID to view/regenerate Client Secret');
      console.log('5. Update VIVA_CLIENT_SECRET in your script and .env file');
      console.log('6. Re-run this test');
    } else {
      console.log('❌ AUTHENTICATION ISSUES DETECTED');
      console.log('🔧 CHECK YOUR CREDENTIALS:');
      console.log('1. Verify Merchant ID and API Key');
      console.log('2. Verify Client ID and Client Secret');
      console.log('3. Ensure you\'re using demo environment credentials');
      console.log('4. Check Viva dashboard: https://demo.vivapayments.com/');
    }

    console.log('');
    console.log('=' .repeat(60));
    console.log('📞 Support: https://developer.viva.com/');
    console.log('📧 Contact: Contact & Support page');
    console.log('=' .repeat(60));

  } catch (error) {
    console.log('❌ CRITICAL ERROR:', error.message);
    console.log('Please check your network connection and try again.');
    process.exit(1);
  }
}

// Run the tests
main();
