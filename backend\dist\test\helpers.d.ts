import { FastifyInstance } from 'fastify';
export declare function createTestApp(): Promise<FastifyInstance<import("http").Server<typeof import("http").IncomingMessage, typeof import("http").ServerResponse>, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, import("pino").Logger<never>, import("fastify").FastifyTypeProviderDefault>>;
export declare const mockStripeService: {
    createConnectionToken: import("vitest").Mock<any, any>;
    createPaymentIntent: import("vitest").Mock<any, any>;
    capturePaymentIntent: import("vitest").Mock<any, any>;
    retrievePaymentIntent: import("vitest").Mock<any, any>;
};
export declare const mockProtocolService: {
    sendProtocolMessage: import("vitest").Mock<any, any>;
    getProtocolDescription: import("vitest").Mock<any, any>;
};
export declare function createMockTransaction(overrides?: any): any;
export declare function createMockProtocolMessage(overrides?: any): any;
export declare function createMockStripePaymentIntent(overrides?: any): any;
export declare function createMockConnectionToken(overrides?: any): any;
export declare function makeRequest(app: FastifyInstance, method: 'GET' | 'POST' | 'PUT' | 'DELETE', url: string, payload?: any, headers?: Record<string, string>): Promise<{
    statusCode: number;
    body: any;
    headers: import("http").OutgoingHttpHeaders;
}>;
export declare function expectSuccessResponse(response: any, expectedData?: any): void;
export declare function expectErrorResponse(response: any, expectedStatusCode: number, expectedError?: string): void;
export declare function expectValidationError(response: any): void;
//# sourceMappingURL=helpers.d.ts.map